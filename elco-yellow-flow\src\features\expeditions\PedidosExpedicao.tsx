import React, { useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import { Input } from '@/components/ui/input';
import { But<PERSON> } from '@/components/ui/button';
import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from '@/components/ui/table';
import StatusBadge from '@/components/ui/StatusBadge';
import { Download, Eye, Search, Filter, CheckCircle, Clock, FileSpreadsheet, Link as LinkIcon, AlertCircle } from 'lucide-react';
import ViewModal from '@/features/expeditions/components/ViewModal';
import MultiVolumeModal from '@/features/expeditions/components/MultiVolumeModal';
import StatCard from '@/components/ui/StatCard';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from '@/components/ui/use-toast';
import VincularModal from '@/features/expeditions/components/VincularModal';
import { useNavigate } from 'react-router-dom';
import { useIsMobile } from '@/hooks/use-mobile';
import { Card, CardContent } from '@/components/ui/card';

const pedidos = [
  { id: '001', cliente: 'Empresa XYZ', data: '02/05/2023', status: 'completed', valor: 'R$ 1.250,00' },
  { id: '002', cliente: 'Distribuidora ABC', data: '05/05/2023', status: 'processing', valor: 'R$ 3.782,50' },
  { id: '003', cliente: 'Loja Central', data: '10/05/2023', status: 'pending', valor: 'R$ 950,20' },
  { id: '004', cliente: 'Mercado SuperPreço', data: '12/05/2023', status: 'completed', valor: 'R$ 2.430,75' },
  { id: '005', cliente: 'Indústria Nacional', data: '15/05/2023', status: 'error', valor: 'R$ 5.621,00' },
  { id: '006', cliente: 'Shopping Center', data: '18/05/2023', status: 'pending', valor: 'R$ 1.890,30' },
  { id: '007', cliente: 'Loja Departamentos', data: '20/05/2023', status: 'completed', valor: 'R$ 3.156,45' },
  { id: '008', cliente: 'Mercado Local', data: '23/05/2023', status: 'processing', valor: 'R$ 1.087,25' },
];

const PedidosExpedicao = () => {
  const [viewModalOpen, setViewModalOpen] = useState(false);
  const [volumeModalOpen, setVolumeModalOpen] = useState(false);
  const [selectedPedido, setSelectedPedido] = useState<any>(null);
  const [selectedPedidos, setSelectedPedidos] = useState<string[]>([]);
  const [vincularModalOpen, setVincularModalOpen] = useState(false);
  const [pedidoParaVincular, setPedidoParaVincular] = useState<any>(null);
  const navigate = useNavigate();
  const isMobile = useIsMobile();

  const handleSelectPedido = (id: string) => {
    if (selectedPedidos.includes(id)) {
      setSelectedPedidos(selectedPedidos.filter(pedidoId => pedidoId !== id));
    } else {
      setSelectedPedidos([...selectedPedidos, id]);
    }
  };

  const handleSelectAll = () => {
    if (selectedPedidos.length === pedidos.filter(p => p.status === 'pending').length) {
      setSelectedPedidos([]);
    } else {
      setSelectedPedidos(pedidos.filter(p => p.status === 'pending').map(p => p.id));
    }
  };

  const handleViewPedido = (pedido: any) => {
    setSelectedPedido(pedido);
    setViewModalOpen(true);
  };

  const handleOpenVolumeModal = () => {
    if (selectedPedidos.length === 0) {
      toast.error('Selecione pelo menos um pedido para criar romaneio');
      return;
    }
    setVolumeModalOpen(true);
  };

  const handleVincularPedido = (pedido: any) => {
    setPedidoParaVincular(pedido);
    setVincularModalOpen(true);
  };

  const handleViewRomaneios = (pedidoId: string) => {
    navigate(`/romaneios/pedido/${pedidoId}`);
  };

  const confirmarVinculo = (tipo: 'matriz' | 'filial') => {
    toast.success(`Pedido #${pedidoParaVincular.id} vinculado à ${tipo}`);
  };

  const completedCount = pedidos.filter(p => p.status === 'completed').length;
  const processingCount = pedidos.filter(p => p.status === 'linked').length;
  const pendingCount = pedidos.filter(p => p.status === 'pending').length;
  const errorCount = pedidos.filter(p => p.status === 'error').length;

  return (
    <MainLayout title="Pedidos de Expedição">
      <div className="mb-8 grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-4">
        <StatCard
          title="Pedidos finalizados"
          value={completedCount}
          variant="green"
          icon={<CheckCircle className="h-6 w-6" />}
        />

        <StatCard
          title="Vinculado"
          value={processingCount}
          variant="blue"
          icon={<Clock className="h-6 w-6" />}
        />

        <StatCard
          title="Pedidos pendentes"
          value={pendingCount}
          variant="amber"
          icon={<Clock className="h-6 w-6" />}
        />

        <StatCard
          title="Com erros"
          value={errorCount}
          variant="rose"
          icon={<AlertCircle className="h-6 w-6" />}
        />
      </div>

      <div className="mb-8 rounded-xl bg-white border shadow-sm p-3 sm:p-5">
        <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
          <div className="flex flex-col space-y-2 md:flex-row md:space-x-2 md:space-y-0">
            <div className="relative w-full md:max-w-sm">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input type="text" placeholder="Buscar pedidos..." className="pl-9" />
            </div>

            <Button variant="outline" className="flex gap-2">
              <Filter className="h-4 w-4" />
              Filtros
            </Button>
          </div>

          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
            {selectedPedidos.length > 0 && (
              <Button
                onClick={handleOpenVolumeModal}
                className="gap-2 bg-green-600 hover:bg-green-700 text-white"
              >
                <FileSpreadsheet className="h-4 w-4" />
                Criar Romaneio ({selectedPedidos.length})
              </Button>
            )}

            <Button variant="outline" className="gap-2">
              <Download className="h-4 w-4" />
              Exportar
            </Button>
          </div>
        </div>
      </div>

      {isMobile ? (
        // Mobile card view
        <div className="space-y-4">
          {pedidos.map((pedido) => (
            <Card key={pedido.id} className="overflow-hidden">
              <CardContent className="p-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      {pedido.status === 'pending' && (
                        <Checkbox
                          checked={selectedPedidos.includes(pedido.id)}
                          onCheckedChange={() => handleSelectPedido(pedido.id)}
                          aria-label={`Select order ${pedido.id}`}
                        />
                      )}
                      <span className="font-medium">Pedido #{pedido.id}</span>
                    </div>
                    <StatusBadge status={pedido.status as any} />
                  </div>
                  
                  <div>
                    <p className="font-medium text-gray-900">{pedido.cliente}</p>
                    <div className="flex justify-between text-sm text-gray-500 mt-1">
                      <span>{pedido.data}</span>
                      <span>{pedido.valor}</span>
                    </div>
                  </div>
                  
                  <div className="flex justify-end space-x-2 pt-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0 rounded-full"
                      onClick={() => handleViewPedido(pedido)}
                      title="Visualizar"
                    >
                      <Eye className="h-4 w-4" />
                    </Button>

                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0 rounded-full text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                      onClick={() => handleVincularPedido(pedido)}
                      title="Vincular"
                    >
                      <LinkIcon className="h-4 w-4" />
                    </Button>

                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0 rounded-full text-green-600 hover:text-green-700 hover:bg-green-50"
                      onClick={() => handleViewRomaneios(pedido.id)}
                      title="Romaneios"
                    >
                      <FileSpreadsheet className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        // Desktop table view
        <div className="rounded-xl overflow-hidden border shadow-sm">
          <div className="table-container">
            <Table>
              <TableHeader className="bg-gray-50">
                <TableRow>
                  <TableHead className="w-12">
                    <Checkbox
                      checked={selectedPedidos.length === pedidos.filter(p => p.status === 'pending').length && pedidos.filter(p => p.status === 'pending').length > 0}
                      onCheckedChange={handleSelectAll}
                      aria-label="Select all pending orders"
                    />
                  </TableHead>
                  <TableHead className="py-3 font-semibold text-gray-700">Pedido #</TableHead>
                  <TableHead className="py-3 font-semibold text-gray-700">Cliente</TableHead>
                  <TableHead className="py-3 font-semibold text-gray-700">Data</TableHead>
                  <TableHead className="py-3 font-semibold text-gray-700">Status</TableHead>
                  <TableHead className="py-3 font-semibold text-gray-700">Valor</TableHead>
                  <TableHead className="py-3 text-right font-semibold text-gray-700">Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {pedidos.map((pedido) => (
                  <TableRow key={pedido.id} className="hover:bg-gray-50/80 transition-colors">
                    <TableCell className="p-4 pl-4">
                      {pedido.status === 'pending' && (
                        <Checkbox
                          checked={selectedPedidos.includes(pedido.id)}
                          onCheckedChange={() => handleSelectPedido(pedido.id)}
                          aria-label={`Select order ${pedido.id}`}
                        />
                      )}
                    </TableCell>
                    <TableCell className="font-medium">{pedido.id}</TableCell>
                    <TableCell className="font-medium text-gray-900">{pedido.cliente}</TableCell>
                    <TableCell>{pedido.data}</TableCell>
                    <TableCell>
                      <StatusBadge status={pedido.status as any} />
                    </TableCell>
                    <TableCell>{pedido.valor}</TableCell>
                    <TableCell className="text-right space-x-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 rounded-full"
                        onClick={() => handleViewPedido(pedido)}
                        title="Visualizar"
                      >
                        <Eye className="h-4 w-4" />
                      </Button>

                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 rounded-full text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                        onClick={() => handleVincularPedido(pedido)}
                        title="Vincular"
                      >
                        <LinkIcon className="h-4 w-4" />
                      </Button>

                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 rounded-full text-green-600 hover:text-green-700 hover:bg-green-50"
                        onClick={() => handleViewRomaneios(pedido.id)}
                        title="Romaneios"
                      >
                        <FileSpreadsheet className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      )}

      {/* Modals */}
      {selectedPedido && (
        <ViewModal
          isOpen={viewModalOpen}
          onClose={() => setViewModalOpen(false)}
          title={`Pedido #${selectedPedido.id}`}
          data={selectedPedido}
          type="order"
        />
      )}

      <MultiVolumeModal
        isOpen={volumeModalOpen}
        onClose={() => {
          setVolumeModalOpen(false);
          setSelectedPedidos([]);
        }}
        orderIds={selectedPedidos}
        orders={pedidos.filter(p => selectedPedidos.includes(p.id))}
      />

      <VincularModal
        isOpen={vincularModalOpen}
        onClose={() => setVincularModalOpen(false)}
        pedido={pedidoParaVincular}
        onConfirm={confirmarVinculo}
      />
    </MainLayout>
  );
};

export default PedidosExpedicao;
