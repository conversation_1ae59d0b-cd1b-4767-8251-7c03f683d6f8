import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { RefreshCw, ArrowRight } from 'lucide-react';
import { RomaneioProps } from '@/hooks/useMultiVolumeModal';

interface GeneralInfoTabProps {
  romaneio: RomaneioProps;
  setRomaneio: React.Dispatch<React.SetStateAction<RomaneioProps>>;
  globalVinculo: 'matriz' | 'filial';
  setGlobalVinculo: (value: 'matriz' | 'filial') => void;
  carrierList: { value: string; label: string }[];
  driverList: { value: string; label: string }[];
  vehicleList: { value: string; label: string }[];
  packagingList: { value: string; label: string }[];
  hasDraftAvailable: boolean;
  isUnlinkedFromDraft: boolean;
  handleUnlinkFromDraft: (checked: boolean) => void;
  handleSaveDraft: () => void;
  setActiveTab: (tab: string) => void;
  isSubmitting: boolean;
}

const GeneralInfoTab: React.FC<GeneralInfoTabProps> = ({
  romaneio,
  setRomaneio,
  globalVinculo,
  setGlobalVinculo,
  carrierList,
  driverList,
  vehicleList,
  packagingList,
  hasDraftAvailable,
  isUnlinkedFromDraft,
  handleUnlinkFromDraft,
  handleSaveDraft,
  setActiveTab,
  isSubmitting,
}) => {
  return (
    <div className="space-y-4 mt-2">
      {hasDraftAvailable && (
        <Card className="border-l-4 border-l-yellow-500 bg-gradient-to-r from-yellow-50 to-white">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <RefreshCw className="h-5 w-5 text-yellow-600" />
                <CardTitle className="text-lg text-yellow-800">Modo de Criação</CardTitle>
              </div>
              <Badge variant={isUnlinkedFromDraft ? "destructive" : "secondary"} className="text-xs">
                {isUnlinkedFromDraft ? "Novo Romaneio" : "Com Rascunho"}
              </Badge>
            </div>
            <CardDescription className="text-yellow-700">
              {isUnlinkedFromDraft 
                ? "Criando um novo romaneio sem usar dados salvos anteriormente"
                : "Usando dados do rascunho salvo (se houver)"
              }
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="flex items-center justify-between p-4 bg-white rounded-lg border border-yellow-200">
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                  <div className={`h-3 w-3 rounded-full ${isUnlinkedFromDraft ? 'bg-red-500' : 'bg-yellow-500'}`}></div>
                </div>
                <div>
                  <Label htmlFor="unlink-draft" className="text-sm font-medium cursor-pointer">
                    Criar novo romaneio (desvincular do rascunho)
                  </Label>
                  <p className="text-xs text-muted-foreground mt-1">
                    {isUnlinkedFromDraft 
                      ? "Os dados do rascunho foram removidos, formulário resetado"
                      : "Marque para começar um novo romaneio sem dados salvos"
                    }
                  </p>
                </div>
              </div>
              <Switch
                id="unlink-draft"
                checked={isUnlinkedFromDraft}
                onCheckedChange={handleUnlinkFromDraft}
                className="data-[state=checked]:bg-red-500"
              />
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Informações de Transporte</CardTitle>
          <CardDescription>Preencha os dados do transporte para este romaneio</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="address">Endereço</Label>
              <Input
                id="address"
                placeholder="Informe o endereço"
                value={romaneio.address}
                onChange={(event) => {
                  setRomaneio({ ...romaneio, address: event.target.value });
                }}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="mainPackaging">Embalagem Principal</Label>
              <Select
                onValueChange={(value) => {
                  setRomaneio({ ...romaneio, mainPackagingId: value });
                }}
                value={
                  packagingList.find(
                    (option) => option.value === romaneio.mainPackagingId
                  )?.value ?? ''
                }
              >
                <SelectTrigger id="mainPackaging">
                  <SelectValue placeholder="Selecione uma Embalagem" />
                </SelectTrigger>
                <SelectContent>
                  {packagingList.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="secondaryPackaging">Segunda Embalagem Principal (Opcional)</Label>
              <Select
                onValueChange={(value) => {
                  setRomaneio({ ...romaneio, secondaryPackagingId: value === 'none' ? undefined : value });
                }}
                value={
                  romaneio.secondaryPackagingId 
                    ? packagingList.find((option) => option.value === romaneio.secondaryPackagingId)?.value ?? 'none'
                    : 'none'
                }
              >
                <SelectTrigger id="secondaryPackaging">
                  <SelectValue placeholder="Selecione uma Segunda Embalagem" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">Nenhuma segunda embalagem</SelectItem>
                  {packagingList.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            <div className="space-y-2">
              <Label htmlFor="carrier">Transportador</Label>
              <Select
                onValueChange={(value) => {
                  setRomaneio({ ...romaneio, carrierId: value });
                }}
                value={
                  carrierList.find(
                    (option) => option.value === romaneio.carrierId
                  )?.value ?? ''
                }
              >
                <SelectTrigger id="carrier">
                  <SelectValue placeholder="Selecione um Transportador" />
                </SelectTrigger>
                <SelectContent>
                  {carrierList.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="driver">Motorista</Label>
              <Select
                onValueChange={(value) => {
                  setRomaneio({ ...romaneio, driverId: value });
                }}
                value={
                  driverList.find(
                    (option) => option.value === romaneio.driverId
                  )?.value ?? ''
                }
              >
                <SelectTrigger id="driver">
                  <SelectValue placeholder="Selecione um Motorista" />
                </SelectTrigger>
                <SelectContent>
                  {driverList.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="vehicle">Placa</Label>
              <Select
                onValueChange={(value) => {
                  setRomaneio({ ...romaneio, vehicleId: value });
                }}
                value={
                  vehicleList.find(
                    (option) => option.value === romaneio.vehicleId
                  )?.value ?? ''
                }
              >
                <SelectTrigger id="vehicle">
                  <SelectValue placeholder="Selecione uma Placa" />
                </SelectTrigger>
                <SelectContent>
                  {vehicleList.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label className="font-medium">Escolha de Vinculação Global</Label>
            <RadioGroup
              value={globalVinculo}
              onValueChange={(value) => setGlobalVinculo(value as 'matriz' | 'filial')}
              className="flex flex-row space-x-4 mt-2"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="matriz" id="global-matriz" />
                <Label htmlFor="global-matriz">Matriz</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="filial" id="global-filial" />
                <Label htmlFor="global-filial">Filial</Label>
              </div>
            </RadioGroup>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Totais do Romaneio</CardTitle>
          <CardDescription>Valores calculados com base nas dimensões dos pedidos</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div className="space-y-1">
              <Label htmlFor="total-length" className="text-xs text-muted-foreground">Comprimento (cm)</Label>
              <Input
                id="total-length"
                className="text-center font-medium"
                value={romaneio.totalLength ? parseFloat(romaneio.totalLength.toString()).toFixed(2) : '0.00'}
                disabled
              />
            </div>

            <div className="space-y-1">
              <Label htmlFor="total-width" className="text-xs text-muted-foreground">Largura (cm)</Label>
              <Input
                id="total-width"
                className="text-center font-medium"
                value={romaneio.totalWidth ? parseFloat(romaneio.totalWidth.toString()).toFixed(2) : '0.00'}
                disabled
              />
            </div>

            <div className="space-y-1">
              <Label htmlFor="total-height" className="text-xs text-muted-foreground">Altura (cm)</Label>
              <Input
                id="total-height"
                className="text-center font-medium"
                value={romaneio.totalHeight ? parseFloat(romaneio.totalHeight.toString()).toFixed(2) : '0.00'}
                disabled
              />
            </div>

            <div className="space-y-1">
              <Label htmlFor="total-weight" className="text-xs text-muted-foreground">Peso Total (kg)</Label>
              <Input
                id="total-weight"
                className="text-center font-medium"
                value={romaneio.totalWeight ? parseFloat(romaneio.totalWeight.toString()).toFixed(2) : '0.00'}
                disabled
              />
            </div>

            <div className="space-y-1">
              <Label htmlFor="total-volume" className="text-xs text-muted-foreground">Volume Total (m³)</Label>
              <Input
                id="total-volume"
                className="text-center font-medium"
                value={romaneio.totalVolume ? parseFloat(romaneio.totalVolume.toString()).toFixed(3) : '0.000'}
                disabled
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-end mt-4">
        <Button 
          className={`mr-2 ${isUnlinkedFromDraft ? 'bg-gray-400 hover:bg-gray-500' : 'bg-yellow-500 hover:bg-yellow-600'} text-white`}
          onClick={handleSaveDraft} 
          disabled={isSubmitting || isUnlinkedFromDraft}
          title={isUnlinkedFromDraft ? "Desative o modo 'Novo Romaneio' para salvar rascunho" : "Salvar dados como rascunho"}
        >
          Salvar Rascunho
        </Button>
        <Button
          variant="outline"
          onClick={() => setActiveTab('packaging')}
          className="gap-2"
        >
          Avançar para Embalagens <ArrowRight className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

export default GeneralInfoTab; 