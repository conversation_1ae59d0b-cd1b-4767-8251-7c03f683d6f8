import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import MainLayout from '@/layouts/MainLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { toast } from '@/components/ui/use-toast';
import { api } from '@/lib/api';
import { Truck, Edit, ArrowLeft, ParkingCircle, BadgeInfo, BadgePlus, FileText } from 'lucide-react';

const formSchema = z.object({
  plate: z.string().min(6, 'Placa obrigatória'),
  model: z.string().min(2, 'Modelo obrigatório'),
  brand: z.string().min(2, 'Marca obrigatória'),
  vehicleDocument: z.instanceof(File).optional(),
});

type FormValues = z.infer<typeof formSchema>;

interface VehicleData {
  plate: string;
  model: string;
  brand: string;
  documentUrl?: string;
}

const EditVehicle = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [documentFile, setDocumentFile] = useState<File | null>(null);
  const [documentPreview, setDocumentPreview] = useState<string | null>(null);
  const [existingDocument, setExistingDocument] = useState<string | null>(null);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      plate: '',
      model: '',
      brand: '',
    },
  });

  const formatPlate = (value: string) => {
    // Convert letters to uppercase while preserving numbers and special characters
    return value.replace(/[a-zA-Z]/g, (letter) => letter.toUpperCase());
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        toast.error('Arquivo muito grande. Tamanho máximo: 5MB');
        return;
      }
      
      setDocumentFile(file);
      form.setValue('vehicleDocument', file);
      
      // Create preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setDocumentPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
      
      // Clear existing document reference
      setExistingDocument(null);
    }
  };

  useEffect(() => {
    const fetchVehicle = async () => {
      setIsLoading(true);
      try {
        const response = await api.get<VehicleData>(`/vehicles/view/${id}`);
        form.reset({
          plate: response.data.plate,
          model: response.data.model,
          brand: response.data.brand,
        });
        
        // Set document preview if exists
        if (response.data.documentUrl) {
          setExistingDocument(response.data.documentUrl);
        }
        
      } catch (error) {
        toast.error('Erro ao carregar veículo');
        navigate('/vehicles');
      } finally {
        setIsLoading(false);
      }
    };

    if (id) fetchVehicle();
  }, [id, form, navigate]);

  const onSubmit = async (data: FormValues) => {
    try {
      const formData = new FormData();
      formData.append('plate', data.plate);
      formData.append('model', data.model);
      formData.append('brand', data.brand);
      
      // If we have a new document file, include it
      if (data.vehicleDocument) {
        formData.append('vehicleDocument', data.vehicleDocument);
      }
      
      // If we're keeping the existing document, indicate that
      if (existingDocument) {
        formData.append('keepExistingDocument', 'true');
      }
      
      await api.put(`/vehicles/update/${id}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      toast.success('Veículo atualizado com sucesso!');
      navigate('/vehicles');
    } catch (error) {
      toast.error('Erro ao atualizar veículo');
    }
  };

  const handleBack = () => navigate('/vehicles');

  return (
    <MainLayout title="Editar Veículo">
      <div className="max-w-3xl mx-auto">
        <Button variant="ghost" className="mb-4" onClick={handleBack}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Voltar para Lista
        </Button>

        <Card>
          <CardHeader>
            <div className="flex items-center gap-2">
              <Edit className="h-6 w-6 text-primary" />
              <CardTitle>Editar Veículo</CardTitle>
            </div>
            <CardDescription>Atualize os dados do veículo</CardDescription>
          </CardHeader>

          <CardContent>
            {isLoading ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary" />
              </div>
            ) : (
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  <FormField
                    control={form.control}
                    name="plate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center gap-2">
                          <ParkingCircle className="h-4 w-4" />
                          Placa
                        </FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="ABC-1234" 
                            value={field.value}
                            onChange={(e) => field.onChange(formatPlate(e.target.value))}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="model"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center gap-2">
                          <BadgeInfo className="h-4 w-4" />
                          Modelo
                        </FormLabel>
                        <FormControl>
                          <Input placeholder="Ex: Sprinter 415" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="brand"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center gap-2">
                          <BadgePlus className="h-4 w-4" />
                          Marca
                        </FormLabel>
                        <FormControl>
                          <Input placeholder="Ex: Mercedes-Benz" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="vehicleDocument"
                    render={() => (
                      <FormItem>
                        <FormLabel className="flex items-center gap-2">
                          <FileText className="h-4 w-4" />
                          Documento do Veículo (CRLV)
                        </FormLabel>
                        <FormControl>
                          <div className="flex flex-col gap-4">
                            <Input
                              type="file"
                              accept="image/*,.pdf"
                              onChange={handleFileChange}
                              className="cursor-pointer"
                            />
                            
                            {/* Show new document preview if selected */}
                            {documentPreview && (
                              <div className="relative mt-2 max-w-sm">
                                {documentFile?.type.includes('image') ? (
                                  <img 
                                    src={documentPreview} 
                                    alt="Preview do documento" 
                                    className="rounded-md border max-h-48 object-contain"
                                  />
                                ) : (
                                  <div className="p-4 border rounded-md flex items-center justify-center bg-gray-50">
                                    <p className="text-sm text-gray-500">
                                      {documentFile?.name} ({(documentFile?.size / 1024 / 1024).toFixed(2)}MB)
                                    </p>
                                  </div>
                                )}
                                <Button
                                  type="button"
                                  variant="destructive"
                                  size="sm"
                                  className="absolute -top-2 -right-2"
                                  onClick={() => {
                                    setDocumentFile(null);
                                    setDocumentPreview(null);
                                    form.setValue('vehicleDocument', undefined);
                                    // Restore existing document if available
                                    if (existingDocument) {
                                      setExistingDocument(existingDocument);
                                    }
                                  }}
                                >
                                  &times;
                                </Button>
                              </div>
                            )}
                            
                            {/* Show existing document if available and no new one selected */}
                            {existingDocument && !documentPreview && (
                              <div className="relative mt-2 max-w-sm">
                                <div className="p-4 border rounded-md flex items-center bg-gray-50">
                                  <FileText className="h-5 w-5 mr-2 text-blue-500" />
                                  <p className="text-sm">
                                    Documento existente
                                  </p>
                                  <Button
                                    type="button"
                                    variant="destructive"
                                    size="sm"
                                    className="absolute -top-2 -right-2"
                                    onClick={() => {
                                      setExistingDocument(null);
                                    }}
                                  >
                                    &times;
                                  </Button>
                                </div>
                              </div>
                            )}
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="pt-4 flex justify-end space-x-4">
                    <Button type="button" variant="outline" onClick={handleBack}>
                      Cancelar
                    </Button>
                    <Button type="submit">
                      <Edit className="mr-2 h-4 w-4" />
                      Salvar Alterações
                    </Button>
                  </div>
                </form>
              </Form>
            )}
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};

export default EditVehicle;
