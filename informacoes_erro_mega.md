# Informações sobre Processamento de Erros do Mega

## Como o Sistema Processa Erros

### 1. Estrutura de Resposta do Mega

Quando o Mega retorna um erro, a resposta SOAP tem a seguinte estrutura:

```xml
<?xml version="1.0" encoding="utf-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/">
  <SOAP-ENV:Body>
    <v1:MegaIntegradorService___IntegraXMLResponse xmlns:v1="http://tempuri.org/">
      <v1:Result>
        <v1:Erro>true</v1:Erro>
        <v1:Mensagem>Mensagem detalhada do erro</v1:Mensagem>
        <v1:PKMega></v1:PKMega>
      </v1:Result>
    </v1:MegaIntegradorService___IntegraXMLResponse>
  </SOAP-ENV:Body>
</SOAP-ENV:Envelope>
```

### 2. Como o Sistema Interpreta a Resposta

No código do `MegaService`, a resposta é processada assim:

```typescript
const resultado = await parseStringPromise(response.data);
const erro = resultado['SOAP-ENV:Envelope']['SOAP-ENV:Body'][0]['v1:MegaIntegradorService___IntegraXMLResponse'][0]['v1:Result'][0]['v1:Erro'][0];
const mensagem = resultado['SOAP-ENV:Envelope']['SOAP-ENV:Body'][0]['v1:MegaIntegradorService___IntegraXMLResponse'][0]['v1:Result'][0]['v1:Mensagem'][0];

if (erro === 'true') {
  // Salvar erro na tabela invoice_errors
  const invoiceError = this.invoiceErrorRepository.create({
    idRomaneio,
    idOrdem,
    errorMessage: mensagem,
    xmlEnviado: notaFiscalXml,
    soapEnvelope
  });
  await this.invoiceErrorRepository.save(invoiceError);
  
  // Atualizar status do romaneio para erro (3)
  await this.updateRomaneioStatusWithRetry(romaneioId, 3);
}
```

### 3. Tabela de Erros

Os erros são salvos na tabela `invoice_errors` com os seguintes campos:

```sql
CREATE TABLE invoice_errors (
  id INT PRIMARY KEY AUTO_INCREMENT,
  idRomaneio INT,
  idOrdem INT,
  errorMessage TEXT,
  xmlEnviado TEXT,
  soapEnvelope TEXT,
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 4. Endpoint para Consultar Erros

```
GET /invoices/errors
```

Retorna todos os erros salvos com:
- ID do erro
- ID do romaneio
- ID da ordem
- Mensagem de erro
- XML enviado
- SOAP envelope
- Data de criação

### 5. Endpoint para Reenviar XML

```
POST /mega/errors/send-xml
```

Corpo da requisição:
```json
{
  "errorId": 123,
  "xmlEnviado": "XML corrigido aqui"
}
```

### 6. Logs do Sistema

O sistema gera logs detalhados em:

```typescript
// Log de resposta do Mega
console.log("🚀 ~ MegaService ~ enviarNotaFiscal ~ response:", response.data)

// Log de erro salvo
console.log(`[MegaService] Erro salvo na tabela invoice_errors para romaneio ${idRomaneio} e ordem ${idOrdem}: ${mensagem}`);

// Log de atualização de status
console.log(`[MegaService] Status do romaneio ${romaneioId} atualizado para ${statusId} na tentativa ${attempt}`);
```

### 7. Status dos Romaneios

- **Status 0:** Aguardando Notas
- **Status 1:** Pendente
- **Status 2:** Nota Emitida (sucesso)
- **Status 3:** Entregue (erro)
- **Status 4:** Verificado Automaticamente
- **Status 5:** Verificado Manualmente

### 8. Exemplo de Erro Típico

Se o campo `ITN_IN_ENQIPI` estiver incorreto, o Mega pode retornar:

```xml
<v1:Result>
  <v1:Erro>true</v1:Erro>
  <v1:Mensagem>Campo ITN_IN_ENQIPI inválido. Valor esperado deve estar na tabela de referência.</v1:Mensagem>
  <v1:PKMega></v1:PKMega>
</v1:Result>
```

### 9. Como Verificar Erros no Frontend

No frontend, os erros são exibidos na página `/notas-erros` com:

- Lista de todos os erros
- Detalhes do XML enviado
- Detalhes do SOAP envelope
- Opção para reenviar XML corrigido

### 10. Processo de Debug

Para debugar problemas com o Mega:

1. Verificar logs do console
2. Consultar tabela `invoice_errors`
3. Analisar XML enviado
4. Verificar resposta SOAP do Mega
5. Corrigir campos problemáticos
6. Reenviar via endpoint de reenvio

---

**Observação:** Todos os erros são salvos automaticamente para auditoria e correção posterior. 