import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import MainLayout from '@/layouts/MainLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from '@/hooks/use-toast';
import { Mail, ArrowLeft, Save } from 'lucide-react';
import { api } from '@/lib/api';

const supplierEmailSchema = z.object({
  supplierName: z.string().min(1, 'Nome do fornecedor é obrigatório'),
  email: z.string().email('Email inválido'),
});

type SupplierEmailFormData = z.infer<typeof supplierEmailSchema>;

type Supplier = {
  projectCode: number;
  projectDescription: string;
};

const EditFornecedorEmail: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [supplierOptions, setSupplierOptions] = useState<Supplier[]>([]);
  const [loadingSuppliers, setLoadingSuppliers] = useState(false);
  const [supplierInput, setSupplierInput] = useState('');
  const [selectedSupplier, setSelectedSupplier] = useState<Supplier | null>(null);

  const form = useForm<SupplierEmailFormData>({
    resolver: zodResolver(supplierEmailSchema),
    defaultValues: {
      supplierName: '',
      email: '',
    },
  });

  useEffect(() => {
    const fetchEmail = async () => {
      if (id) {
        const res = await api.get(`/supplier-emails/${id}`);
        const data = res.data;
        form.setValue('supplierName', data.supplierName);
        form.setValue('email', data.email);
      }
    };
    fetchEmail();
  }, [id, form]);

  const fetchSuppliers = async (search: string) => {
    setLoadingSuppliers(true);
    try {
      const res = await api.get(`/supplier-emails/projects?search=${encodeURIComponent(search)}`);
      setSupplierOptions(res.data);
    } catch (e) {
      setSupplierOptions([]);
    } finally {
      setLoadingSuppliers(false);
    }
  };

  const onSupplierInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSupplierInput(value);
    if (value.length >= 4) {
      fetchSuppliers(value);
    } else {
      setSupplierOptions([]);
    }
  };

  const onSupplierSelect = (projectCode: number, projectDescription: string) => {
    form.setValue('supplierName', projectDescription);
    setSelectedSupplier({ projectCode, projectDescription });
    setSupplierOptions([]);
  };

  const onSubmit = async (data: SupplierEmailFormData) => {
    try {
      await api.put(`/supplier-emails/${id}`, {
        supplierName: data.supplierName,
        email: data.email,
      });
      toast({
        title: 'Sucesso!',
        description: 'Email atualizado com sucesso.',
      });
      navigate('/supplier-emails');
    } catch (error) {
      toast({
        title: 'Erro!',
        description: 'Erro ao atualizar email',
        variant: 'destructive',
      });
    }
  };

  return (
    <MainLayout title="Editar Email">
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate('/supplier-emails')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Voltar
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5 text-elco-600" />
              Editar Email
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="supplierName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Fornecedor</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          placeholder="Digite o nome do fornecedor"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input 
                          type="email"
                          placeholder="Digite o email do fornecedor"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex gap-2">
                  <Button type="submit" className="flex items-center gap-2">
                    <Save className="h-4 w-4" />
                    Salvar Alterações
                  </Button>
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => navigate('/supplier-emails')}
                  >
                    Cancelar
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};

export default EditFornecedorEmail;
