
import React, { useState } from 'react';
import Layout from '@/layouts/Layout';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Eye, Link as LinkIcon, Search, Filter, Download } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import ViewModal from '@/features/expeditions/components/ViewModal';

// Dados de exemplo
const pedidos = [
  { 
    id: '001',
    cliente: 'Empresa XYZ',
    data: '02/05/2023',
    valor: 'R$ 1.250,00',
    vinculacao: 'matriz',
    responsavel: '<PERSON>'
  },
  { 
    id: '002',
    cliente: 'Distribuidora ABC',
    data: '05/05/2023',
    valor: 'R$ 3.782,50',
    vinculacao: 'filial',
    responsavel: 'Ana Silva'
  },
  { 
    id: '003',
    cliente: 'Loja Central',
    data: '10/05/2023',
    valor: 'R$ 950,20',
    vinculacao: 'pendente',
    responsavel: '-'
  },
  { 
    id: '004',
    cliente: 'Mercado SuperPreço',
    data: '12/05/2023',
    valor: 'R$ 2.430,75',
    vinculacao: 'matriz',
    responsavel: 'João Costa'
  },
  { 
    id: '005',
    cliente: 'Indústria Nacional',
    data: '15/05/2023',
    valor: 'R$ 5.621,00',
    vinculacao: 'filial',
    responsavel: 'Marina Santos'
  },
  { 
    id: '006',
    cliente: 'Shopping Center',
    data: '18/05/2023',
    valor: 'R$ 1.890,30',
    vinculacao: 'pendente',
    responsavel: '-'
  },
];

const VinculacaoPedidos = () => {
  const [viewModalOpen, setViewModalOpen] = useState(false);
  const [selectedPedido, setSelectedPedido] = useState<any>(null);

  const handleViewPedido = (pedido: any) => {
    setSelectedPedido(pedido);
    setViewModalOpen(true);
  };

  return (
    <Layout title="Vinculação de Pedidos">
      <div className="mb-6 grid grid-cols-1 gap-6 md:grid-cols-3">
        <Card className="overflow-hidden border-0 shadow-md bg-gradient-to-br from-white to-amber-50">
          <CardHeader className="pb-2 border-b border-amber-100/50">
            <CardTitle className="flex items-center text-elco-800">
              <span className="text-2xl">Matriz</span>
              <span className="ml-auto flex items-center justify-center w-10 h-10 rounded-full bg-elco-500/10 text-elco-600">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M3 9h18v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9Z"/><path d="m3 9 2.45-4.9A2 2 0 0 1 7.24 3h9.52a2 2 0 0 1 1.8 1.1L21 9"/><path d="M12 3v6"/></svg>
              </span>
            </CardTitle>
            <CardDescription>Pedidos vinculados à matriz</CardDescription>
          </CardHeader>
          <CardContent className="pt-4">
            <div className="text-3xl font-bold text-elco-800">42</div>
            <div className="mt-1 text-xs text-muted-foreground">
              <span className="inline-flex items-center text-green-600">
                <svg className="w-3 h-3 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M7 17L17 7M17 7H8M17 7V16" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                8% desde o último mês
              </span>
            </div>
          </CardContent>
        </Card>
        
        <Card className="overflow-hidden border-0 shadow-md bg-gradient-to-br from-white to-amber-50">
          <CardHeader className="pb-2 border-b border-amber-100/50">
            <CardTitle className="flex items-center text-elco-800">
              <span className="text-2xl">Filial</span>
              <span className="ml-auto flex items-center justify-center w-10 h-10 rounded-full bg-elco-500/10 text-elco-600">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect width="16" height="20" x="4" y="2" rx="2"/><path d="M9 22v-4h6v4"/><path d="M8 6h.01"/><path d="M16 6h.01"/><path d="M12 6h.01"/><path d="M8 10h.01"/><path d="M16 10h.01"/><path d="M12 10h.01"/><path d="M8 14h.01"/><path d="M16 14h.01"/><path d="M12 14h.01"/></svg>
              </span>
            </CardTitle>
            <CardDescription>Pedidos vinculados à filial</CardDescription>
          </CardHeader>
          <CardContent className="pt-4">
            <div className="text-3xl font-bold text-elco-800">38</div>
            <div className="mt-1 text-xs text-muted-foreground">
              <span className="inline-flex items-center text-green-600">
                <svg className="w-3 h-3 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M7 17L17 7M17 7H8M17 7V16" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                5% desde o último mês
              </span>
            </div>
          </CardContent>
        </Card>
        
        <Card className="overflow-hidden border-0 shadow-md bg-gradient-to-br from-white to-amber-50">
          <CardHeader className="pb-2 border-b border-amber-100/50">
            <CardTitle className="flex items-center text-elco-800">
              <span className="text-2xl">Pendentes</span>
              <span className="ml-auto flex items-center justify-center w-10 h-10 rounded-full bg-amber-500/10 text-amber-600">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M12 9v4"/><path d="M12 17h.01"/><circle cx="12" cy="12" r="10"/></svg>
              </span>
            </CardTitle>
            <CardDescription>Pedidos sem vinculação</CardDescription>
          </CardHeader>
          <CardContent className="pt-4">
            <div className="text-3xl font-bold text-elco-800">14</div>
            <div className="mt-1 text-xs text-muted-foreground">
              <span className="inline-flex items-center text-red-600">
                <svg className="w-3 h-3 mr-1 rotate-180" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M7 17L17 7M17 7H8M17 7V16" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                3% desde o último mês
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="mb-6 backdrop-blur-sm rounded-xl bg-white/80 border border-gray-100 shadow-sm p-4">
        <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
          <div className="flex flex-col space-y-2 md:flex-row md:space-x-2 md:space-y-0">
            <div className="flex w-full max-w-sm items-center space-x-2">
              <div className="relative w-full">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input type="text" placeholder="Buscar pedidos..." className="pl-9" />
              </div>
            </div>
            
            <div className="flex space-x-2">
              <Select>
                <SelectTrigger className="w-[180px] bg-white">
                  <SelectValue placeholder="Vinculação" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos</SelectItem>
                  <SelectItem value="matriz">Matriz</SelectItem>
                  <SelectItem value="filial">Filial</SelectItem>
                  <SelectItem value="pendente">Pendente</SelectItem>
                </SelectContent>
              </Select>

              <Button variant="outline" className="flex gap-2 bg-white">
                <Filter className="h-4 w-4" />
                Filtros
              </Button>
            </div>
          </div>
          
          <div className="flex space-x-2">
            <Button variant="outline" className="gap-2 bg-white">
              <Download className="h-4 w-4" />
              Exportar
            </Button>
            <Button className="bg-elco-600 hover:bg-elco-700 gap-2">
              <LinkIcon className="h-4 w-4" />
              Vincular Em Massa
            </Button>
          </div>
        </div>
      </div>

      <div className="rounded-xl overflow-hidden border-0 shadow-md">
        <Table>
          <TableHeader className="bg-gray-50">
            <TableRow>
              <TableHead className="w-[100px] font-medium">Pedido #</TableHead>
              <TableHead className="font-medium">Cliente</TableHead>
              <TableHead className="font-medium">Data</TableHead>
              <TableHead className="font-medium">Vinculação</TableHead>
              <TableHead className="font-medium">Responsável</TableHead>
              <TableHead className="text-right font-medium">Ações</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {pedidos.map((pedido) => (
              <TableRow key={pedido.id} className="border-b hover:bg-gray-50/80 transition-colors">
                <TableCell className="font-medium">{pedido.id}</TableCell>
                <TableCell className="font-medium text-gray-900">{pedido.cliente}</TableCell>
                <TableCell>{pedido.data}</TableCell>
                <TableCell>
                  <Badge variant={
                    pedido.vinculacao === 'matriz' 
                      ? 'default' 
                      : pedido.vinculacao === 'filial' 
                        ? 'secondary' 
                        : 'outline'
                  }
                  className={
                    pedido.vinculacao === 'matriz'
                      ? 'bg-elco-500 hover:bg-elco-600 text-xs font-medium px-3'
                      : pedido.vinculacao === 'filial'
                        ? 'bg-blue-500 text-white hover:bg-blue-600 text-xs font-medium px-3'
                        : 'border-amber-300 text-amber-700 hover:bg-amber-50 text-xs font-medium px-3'
                  }>
                    {pedido.vinculacao === 'matriz' 
                      ? 'Matriz' 
                      : pedido.vinculacao === 'filial' 
                        ? 'Filial' 
                        : 'Pendente'
                    }
                  </Badge>
                </TableCell>
                <TableCell>{pedido.responsavel}</TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end space-x-2">
                    <Select>
                      <SelectTrigger className="w-[120px] bg-white">
                        <SelectValue placeholder="Vincular" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="matriz">Matriz</SelectItem>
                        <SelectItem value="filial">Filial</SelectItem>
                      </SelectContent>
                    </Select>
                    <Button variant="ghost" size="icon" onClick={() => handleViewPedido(pedido)}>
                      <Eye className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {selectedPedido && (
        <ViewModal
          isOpen={viewModalOpen}
          onClose={() => setViewModalOpen(false)}
          title={`Pedido #${selectedPedido.id}`}
          data={selectedPedido}
          type="link"
        />
      )}
    </Layout>
  );
};

export default VinculacaoPedidos;
