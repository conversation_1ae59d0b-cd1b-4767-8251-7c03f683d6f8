import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import MainLayout from '@/layouts/MainLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from '@/hooks/use-toast';
import { Mail, ArrowLeft, Plus } from 'lucide-react';
import { api } from '@/lib/api';

const supplierEmailSchema = z.object({
  supplierName: z.string().min(1, 'Nome do fornecedor é obrigatório'),
  email: z.string().email('Invalid email'),
});

type SupplierEmailFormData = z.infer<typeof supplierEmailSchema>;

const CreateSupplierEmail: React.FC = () => {
  const navigate = useNavigate();

  const form = useForm<SupplierEmailFormData>({
    resolver: zodResolver(supplierEmailSchema),
    defaultValues: {
      supplierName: '',
      email: '',
    },
  });

  const onSubmit = async (data: SupplierEmailFormData) => {
    try {
      await api.post('/supplier-emails/create-supplier-email', {
        supplierName: data.supplierName,
        email: data.email,
      });
      toast({
        title: 'Sucesso!',
        description: 'Email cadastrado com sucesso.',
      });
      navigate('/supplier-emails');
    } catch (error) {
      toast({
        title: 'Erro!',
        description: 'Erro ao cadastrar email',
        variant: 'destructive',
      });
    }
  };

  return (
    <MainLayout title="Novo Email">
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate('/supplier-emails')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Voltar
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5 text-elco-600" />
              Cadastrar Novo Email
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="supplierName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Fornecedor</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          placeholder="Digite o nome do fornecedor"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          placeholder="Digite o email do fornecedor"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex gap-2">
                  <Button type="submit" className="flex items-center gap-2">
                    <Plus className="h-4 w-4" />
                    Cadastrar Email
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => navigate('/supplier-emails')}
                  >
                    Cancelar
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};

export default CreateSupplierEmail;
