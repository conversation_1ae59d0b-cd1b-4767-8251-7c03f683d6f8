
import { api } from '@/lib/api';

export async function saveRomaneio(data) {
  const response = await api.post('romaneios', data);
  return response;
}

export async function saveRomaneioDraft(data) {
  const response = await api.post('romaneios/rascunho', data);
  return response;
}

export async function getRomaneios() {
  try {
    const response = await api.get('romaneios');
    return response;
  } catch (error) {
    console.error('Erro ao buscar romaneios:', error);
    // Retorna array vazio em caso de erro para não quebrar a interface
    return { data: [] };
  }
}

export async function deleteRomaneio(id: string) {
  const response = await api.delete(`romaneios/${id}`);
  return response;
}
