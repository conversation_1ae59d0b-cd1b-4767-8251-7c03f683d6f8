import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Transporter } from './entity/transporter.entity';
import { TransporterService } from './transporter.service';
import { TransporterController } from './transporter.controller';

@Module({
  imports: [TypeOrmModule.forFeature([Transporter])],
  providers: [TransporterService],
  controllers: [TransporterController],
})
export class TransporterModule {}
