import React, { useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import MainLayout from '@/layouts/MainLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from '@/hooks/use-toast';
import { MapPin, ArrowLeft, Save } from 'lucide-react';
import { api } from '@/lib/api';

const addressSchema = z.object({
  projectCode: z.string().min(1, 'Project code is required'),
  projectName: z.string().min(1, 'Project name is required'),
  address: z.string().min(5, 'Address must be at least 5 characters'),
});

type AddressFormData = z.infer<typeof addressSchema>;

const EditAddress: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();

  const form = useForm<AddressFormData>({
    resolver: zodResolver(addressSchema),
    defaultValues: {
      projectCode: '',
      projectName: '',
      address: '',
    },
  });

  useEffect(() => {
    if (id) {
      api.get(`/addresses/${id}`)
        .then(res => {
          form.setValue('projectCode', res.data.projectCode);
          form.setValue('projectName', res.data.projectName);
          form.setValue('address', res.data.address);
        })
        .catch(() => {
          toast({
            title: 'Erro!', 
            description: 'Erro ao carregar endereço',
            variant: 'destructive',
          });
          navigate('/addresses');
        });
    }
  }, [id, form, navigate]);

  const onSubmit = async (data: AddressFormData) => {
    try {
      await api.put(`/addresses/${id}`, data);
      toast({
        title: 'Sucesso!',
        description: 'Endereço atualizado com sucesso.',
      });
      navigate('/addresses');
    } catch (error) {
      toast({
        title: 'Erro!',
        description: 'Erro ao atualizar endereço',
        variant: 'destructive',
      });
    }
  };

  return (
    <MainLayout title="Editar Endereço">
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate('/addresses')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Voltar
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5 text-elco-600" />
              Editar Endereço
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="projectName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Project Name</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="Project name"
                            {...field}
                            disabled
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <FormField
                  control={form.control}
                  name="address"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Address</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Enter the full address"
                          rows={3}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className="flex gap-2">
                  <Button type="submit" className="flex items-center gap-2">
                    <Save className="h-4 w-4" />
                    Salvar Alterações
                  </Button>
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => navigate('/addresses')}
                  >
                    Cancelar
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};

export default EditAddress;
