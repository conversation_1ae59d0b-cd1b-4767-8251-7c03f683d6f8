
import { CreateRomaneioOrderDto } from 'src/modules/romaneio-orders/dto/create-romaneio-order.dto';

// Define interfaces simples sem decorators para evitar erros de build
export interface CreateProductPackagingDto {
  orderCode: string;
  productCode: string;
  productName: string;
  packagingId: number;
  secondaryVolume: number;
}

export interface CreateRomaneioDto {
  orders: CreateRomaneioOrderDto[];
  items: CreateProductPackagingDto[];
  transporterId: number;
  driverId: number;
  vehicleId: number;
  sendType: 'matriz' | 'filial';
}

// Exportar classes vazias para manter compatibilidade com imports existentes
export class CreateProductPackagingDto implements CreateProductPackagingDto {}
export class CreateRomaneioDto implements CreateRomaneioDto {}
