import React, { useEffect, useState } from 'react';
import logo from '@/images/logo.png';
import { encode as base64Encode } from 'js-base64';
import { compressToEncodedURIComponent } from 'lz-string';
// Adicionar import para acessar variáveis de ambiente
// @ts-ignore
const TINYURL_API_TOKEN = import.meta.env.VITE_TINYURL_API_TOKEN || import.meta.env.TINYURL_API_TOKEN;

const EtiquetaPrimaria: React.FC = () => {
  const [romaneio, setRomaneio] = useState<any>(null);
  const [conteudoQRCode, setConteudoQRCode] = useState('');
  const [legendaQRCode, setLegendaQRCode] = useState('');

  useEffect(() => {
    const data = localStorage.getItem('romaneioEtiquetaPrimaria');
    if (data) {
      setRomaneio(JSON.parse(data));
    }
  }, []);

  useEffect(() => {
    const encurtarUrl = async (urlLonga: string) => {
      try {
        const response = await fetch('https://api.tinyurl.com/create', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${TINYURL_API_TOKEN}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            url: urlLonga,
            domain: 'tinyurl.com',
          }),
        });
        const data = await response.json();
        return data.data.tiny_url;
      } catch (e) {
        return urlLonga;
      }
    };

    if (romaneio) {
      const { xml, pdfBase64, ...taxOrderWithoutXmlAndPdf } = romaneio.taxOrder || {};
      const idRomaneio = romaneio.idRomaneio || romaneio.id;
      const jsonCompact = JSON.stringify({
        i: idRomaneio,
        t: romaneio.packagingType,
        u: romaneio.userName,
        c: romaneio.carrierName,
        d: romaneio.driverName,
        v: romaneio.vehiclePlace,
        di: romaneio.dateIssue,
        l: romaneio.totalLength,
        w: romaneio.totalWidth,
        h: romaneio.totalHeight,
        we: romaneio.totalWeight,
        vo: romaneio.totalVolume,
        s: romaneio.statusName,
        ...taxOrderWithoutXmlAndPdf
      });
      const jsonCompressed = compressToEncodedURIComponent(jsonCompact);
      const urlLonga = `/visualizar-xml?json=${jsonCompressed}`;
      encurtarUrl(window.location.origin + urlLonga).then((urlCurta) => {
        console.log("🚀 ~ encurtarUrl ~ urlCurta:", urlCurta)
        setConteudoQRCode(urlCurta);
        setLegendaQRCode('Escaneie para visualizar os dados do romaneio');
      });
    }
  }, [romaneio]);

  const handleQrLoad = () => {
    window.print();
  };

  if (!romaneio || !conteudoQRCode) return <div>Carregando...</div>;

  const packagingType = romaneio.packagingType || 'N/A';
  const numeroRomaneio = romaneio.id || 'N/A';
  const numeroDanfe = romaneio.taxOrder?.notInNumero || 'N/A';

  const conteudoCodigoBarras = `RM:${numeroRomaneio};TP:${packagingType};DF:${numeroDanfe}`;

  return (
    <div className="bg-white print:bg-white">
      <div
        id="printArea"
        className="bg-white border-[3px] border-black flex flex-col"
        style={{
          width: '10cm',
          height: '15cm',
          boxSizing: 'border-box',
        }}
      >
        <div className="flex h-[26mm] border-b-[3px] border-black">
          <div className="w-[26mm] border-r-[3px] border-black flex items-center justify-center">
            <img src={logo} alt="Elco" className="object-contain w-[22mm]" />
          </div>
          <div className="flex-1 flex flex-col items-center justify-center text-center px-1">
            <span className="font-bold text-[14px] leading-none">Elco Engenharia Ltda.</span>
            <span className="text-[10px] leading-none mt-[2px]">Etiqueta de Embalagem Primária</span>
          </div>
        </div>
        <div className="flex flex-col items-center justify-center py-2 border-b-[3px] border-black">
          <span className="font-bold text-[14px]">Tipo: {packagingType}</span>
          <span className="font-bold text-[14px] mt-1">Romaneio nº: {numeroRomaneio}</span>
        </div>

        <div className="flex flex-col items-center justify-center gap-y-1 px-2 py-1 border-b-[3px] border-black text-[10px] font-bold text-center">
          <img
            src={`https://barcode.tec-it.com/barcode.ashx?data=${encodeURIComponent(conteudoCodigoBarras)}&code=Code128&translate-esc=false`}
            alt="Romaneio barcode"
            className="max-w-[80mm] h-[18mm] object-contain"
          />
        </div>
        <div className="flex flex-1">
          <div className="flex-1 border-r-[3px] border-black flex flex-col items-center px-2 justify-center">
            <div className="text-[10px] font-bold leading-tight w-full text-left">
              <div>DANFE nº:</div>
              <div>{numeroDanfe}</div>
            </div>
            <img
              src={`https://api.qrserver.com/v1/create-qr-code/?size=140x140&data=${encodeURIComponent(conteudoQRCode)}`}
              alt="QR DANFE"
              className="w-[26mm] h-[26mm] object-contain mt-2"
              onLoad={handleQrLoad}
            />
          </div>
          <div className="w-[30mm] flex items-center justify-center">
            <span className="font-bold text-[18px]">FRÁGIL</span>
          </div>
        </div>
      </div>
      <style>{`
        @media print {
          @page {
            size: 10cm 15cm;
            margin: 0;
          }

          html, body {
            width: 10cm !important;
            height: 15cm !important;
            margin: 0 !important;
            padding: 0 !important;
            overflow: hidden !important;
            -webkit-print-color-adjust: exact;
          }

          #printArea {
            position: fixed;
            top: 0;
            left: 0;
            width: 10cm !important;
            height: 15cm !important;
            page-break-after: avoid;
          }

          * {
            box-sizing: border-box;
          }
        }
      `}</style>
    </div>
  );
};

export default EtiquetaPrimaria;

