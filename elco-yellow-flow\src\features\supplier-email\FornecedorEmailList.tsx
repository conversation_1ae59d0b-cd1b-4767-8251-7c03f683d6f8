import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import MainLayout from '@/layouts/MainLayout';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from '@/components/ui/table';
import { Edit, Trash2, Plus, Mail } from 'lucide-react';
import SmartPagination from '@/components/ui/SmartPagination';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from '@/components/ui/alert-dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from '@/hooks/use-toast';
import { useIsMobile } from '@/hooks/use-mobile';
import { api } from '@/lib/api';

interface FornecedorEmail {
  id: number;
  supplierName: string;
  email: string;
  createdAt: Date;
}

const FornecedorEmailList = () => {
  const [fornecedorEmails, setFornecedorEmails] = useState<FornecedorEmail[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(5);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [emailToDelete, setEmailToDelete] = useState<FornecedorEmail | null>(null);
  const navigate = useNavigate();
  const isMobile = useIsMobile();

  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await api.get('/supplier-emails/list');
        setFornecedorEmails(res.data);
      } catch (e) {
        setFornecedorEmails([]);
      }
    };
    fetchData();
  }, []);

  const handleDelete = async (permanent = false) => {
    if (!emailToDelete) return;
    try {
      if (permanent) {
        await api.delete(`/supplier-emails/hard/${emailToDelete.id}`);
      } else {
        // Soft delete (caso queira manter)
        // await api.delete(`/supplier-emails/${emailToDelete.id}`);
      }
      setFornecedorEmails(prev => prev.filter(e => e.id !== emailToDelete.id));
      toast({
        title: 'Sucesso!',
        description: permanent ? 'Email removido permanentemente.' : 'Email removido com sucesso.',
      });
    } catch (error: any) {
      toast({
        title: 'Erro!',
        description: 'Erro ao remover email',
        variant: 'destructive',
      });
    } finally {
      setDeleteDialogOpen(false);
    }
  };

  const handleEdit = (id: string) => navigate(`/supplier-emails/edit/${id}`);
  const handleCreate = () => navigate(`/supplier-emails/create`);

  const totalPages = Math.ceil(fornecedorEmails.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const currentItems = fornecedorEmails.slice(startIndex, startIndex + itemsPerPage);

  return (
    <MainLayout title="Email do Fornecedor">
      <Card>
        <CardHeader className="flex flex-col sm:flex-row items-start sm:items-center sm:justify-between gap-4">
          <div className="flex items-center gap-2">
            <Mail className="h-6 w-6 text-primary" />
            <CardTitle>Listagem de Emails</CardTitle>
          </div>
          <Button onClick={handleCreate} className="w-full sm:w-auto">
            <Plus className="mr-2 h-4 w-4" />
            Novo Email
          </Button>
        </CardHeader>

        <CardContent>
          <div className="flex justify-end mb-4">
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">Itens por página:</span>
              <Select value={String(itemsPerPage)} onValueChange={(v) => {
                setItemsPerPage(Number(v));
                setCurrentPage(1);
              }}>
                <SelectTrigger className="w-[80px]">
                  <SelectValue placeholder="5" />
                </SelectTrigger>
                <SelectContent>
                  {[5, 10, 20, 50].map(num => (
                    <SelectItem key={num} value={String(num)}>{num}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {isMobile ? (
            <div className="space-y-4">
              {currentItems.length > 0 ? (
                currentItems.map(fornecedorEmail => (
                  <Card key={fornecedorEmail.id} className="overflow-hidden">
                    <CardContent className="p-4">
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <h3 className="font-medium text-lg">{fornecedorEmail.supplierName}</h3>
                          <div className="flex space-x-2">
                            <Button variant="ghost" size="sm" onClick={() => handleEdit(String(fornecedorEmail.id))}>
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-destructive hover:text-destructive hover:bg-destructive/10"
                              onClick={() => {
                                setEmailToDelete(fornecedorEmail);
                                setDeleteDialogOpen(true);
                              }}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Email</p>
                          <p>{fornecedorEmail.email}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              ) : (
                <Card>
                  <CardContent className="p-6 text-center text-muted-foreground">
                    Nenhum email encontrado
                  </CardContent>
                </Card>
              )}
            </div>
          ) : (
            <div className="border rounded-lg overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Fornecedor</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead className="text-right">Ações</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {currentItems.length > 0 ? (
                    currentItems.map(fornecedorEmail => (
                      <TableRow key={fornecedorEmail.id}>
                        <TableCell className="font-medium">{fornecedorEmail.supplierName}</TableCell>
                        <TableCell>{fornecedorEmail.email}</TableCell>
                        <TableCell className="text-right space-x-2">
                          <Button variant="ghost" size="sm" onClick={() => handleEdit(String(fornecedorEmail.id))}>
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-destructive hover:text-destructive hover:bg-destructive/10"
                            onClick={() => {
                              setEmailToDelete(fornecedorEmail);
                              setDeleteDialogOpen(true);
                            }}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={3} className="text-center py-6 text-muted-foreground">
                        Nenhum email encontrado
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          )}

          {fornecedorEmails.length > 0 && (
            <div className="mt-4">
              <SmartPagination
                totalPages={totalPages}
                currentPage={currentPage}
                onPageChange={setCurrentPage}
              />
            </div>
          )}
        </CardContent>
      </Card>

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent className="max-w-[90vw] sm:max-w-lg">
          <AlertDialogHeader>
            <AlertDialogTitle>Excluir Email</AlertDialogTitle>
            <AlertDialogDescription>
              Deseja realmente excluir o email de <strong>{emailToDelete?.supplierName}</strong>?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="flex-col sm:flex-row gap-2 sm:gap-0">
            <AlertDialogCancel className="w-full sm:w-auto">Cancelar</AlertDialogCancel>
            <AlertDialogAction onClick={() => handleDelete(true)} className="bg-destructive text-white w-full sm:w-auto">
              Excluir
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </MainLayout>
  );
};

export default FornecedorEmailList;
