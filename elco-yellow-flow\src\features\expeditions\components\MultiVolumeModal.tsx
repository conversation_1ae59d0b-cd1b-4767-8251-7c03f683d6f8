import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { toast } from '@/components/ui/use-toast';
import { Package, Truck, Boxes, FileText } from 'lucide-react';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import AddPackagingDialog from '@/features/packaging/components/AddPackagingDialog';
import { useMultiVolumeModal, VolumesProps, RomaneioProps, SecondaryPackaging } from '@/hooks/useMultiVolumeModal';
import { useRomaneioOperations } from '@/hooks/useRomaneioOperations';
import GeneralInfoTab from './MultiVolumeModal/GeneralInfoTab';
import PackagingTab from './MultiVolumeModal/PackagingTab';
import OrdersTab from './MultiVolumeModal/OrdersTab';
import ObservationsTab from './MultiVolumeModal/ObservationsTab';

interface MultiVolumeModalProps {
  isOpen: boolean;
  onClose: () => void;
  orderIds: string[];
  orders: any[];
  onSuccess?: () => void;
}

const MultiVolumeModal: React.FC<MultiVolumeModalProps> = ({
  isOpen,
  onClose,
  orderIds,
  orders,
  onSuccess,
}) => {
  const [isAddPackagingOpen, setIsAddPackagingOpen] = useState(false);

  // Custom hooks
  const modalState = useMultiVolumeModal(orders, isOpen);
  const operations = useRomaneioOperations();

  // Destructure state from custom hook
  const {
    volumes,
    setVolumes,
    romaneio,
    setRomaneio,
    isSubmitting,
    setIsSubmitting,
    globalVinculo,
    setGlobalVinculo,
    carrierList,
    driverList,
    vehicleList,
    packagingList,
    setPackagingList,
    activeTab,
    setActiveTab,
    loadingOrders,
    productsByOrder,
    packagingSelections,
    setPackagingSelections,
    productPackagings,
    setProductPackagings,
    packagingOptionsFull,
    setPackagingOptionsFull,
    secondaryPackagings,
    setSecondaryPackagings,
    productWeights,
    setProductWeights,
    errorMessage,
    setErrorMessage,
    invalidSecondaryVolumes,
    setInvalidSecondaryVolumes,
    combinedOrders,
    setCombinedOrders,
    bulkPackagingSelections,
    setBulkPackagingSelections,
    selectedProducts,
    setSelectedProducts,
    appliedFeedback,
    setAppliedFeedback,
    isUnlinkedFromDraft,
    setIsUnlinkedFromDraft,
    hasDraftAvailable,
    setHasDraftAvailable,
    loadProducts,
  } = modalState;

  // Load draft when modal opens
  useEffect(() => {
    if (isOpen && orders.length > 0 && !isUnlinkedFromDraft) {
      operations.tryLoadDraft(
        orders,
        globalVinculo,
        isUnlinkedFromDraft,
        setCombinedOrders,
        setVolumes,
        setRomaneio,
        setSecondaryPackagings,
        setPackagingSelections,
        setProductWeights,
        setHasDraftAvailable,
        loadProducts
      );
    } else if (isOpen && orders.length > 0 && isUnlinkedFromDraft) {
      setCombinedOrders(orders);
      const initialVolumes: VolumesProps[] = orders.map((order) => ({
        separationOrderId: order.id,
        orderId: order.internalCode,
        linking: globalVinculo,
        volume: 0,
        length: 0,
        height: 0,
        width: 0,
        netWeight: 0,
        numberPackaging: Number(order.volume) || 1,
      }));
      setVolumes(initialVolumes);
    }
  }, [isOpen, orders, isUnlinkedFromDraft]);

  // Event handlers
  const handleOrderChange = (orderIndex: number, value: string, field: string) => {
    setVolumes((prev) =>
      prev.map((order, index) =>
        index === orderIndex ? { ...order, [field]: Number(value) } : order
      )
    );
  };

  const handleLengthChange = (orderIndex: number, value: string) => {
    const length = Number(value) || 0;
    const width = volumes[orderIndex]?.width || 0;
    const height = volumes[orderIndex]?.height || 0;
    const volume = (length * width * height) / 1000000;

    setVolumes((prev) =>
      prev.map((order, index) =>
        index === orderIndex ? { ...order, length: Number(value), volume } : order
      )
    );
  };

  const handleWidthChange = (orderIndex: number, value: string) => {
    const length = volumes[orderIndex]?.length || 0;
    const width = Number(value) || 0;
    const height = volumes[orderIndex]?.height || 0;
    const volume = (length * width * height) / 1000000;

    setVolumes((prev) =>
      prev.map((order, index) =>
        index === orderIndex ? { ...order, width: Number(value), volume } : order
      )
    );
  };

  const handleHeightChange = (orderIndex: number, value: string) => {
    const length = volumes[orderIndex]?.length || 0;
    const width = volumes[orderIndex]?.width || 0;
    const height = Number(value) || 0;
    const volume = (length * width * height) / 1000000;

    setVolumes((prev) =>
      prev.map((order, index) =>
        index === orderIndex ? { ...order, height: Number(value), volume } : order
      )
    );
  };

  const handlePackagingChange = (orderCode: string, productCode: string, packagingId: string) => {
    setPackagingSelections(prev => ({
      ...prev,
      [orderCode]: {
        ...prev[orderCode],
        [productCode]: packagingId,
      },
    }));

    const product = productsByOrder[orderCode]?.find(p => p.code === productCode);
    const productName = product?.name ?? productCode;

    setProductPackagings(prev => {
      const cleaned = prev.filter(
        item => !(item.orderCode === orderCode && item.productCode === productCode)
      );
      return [
        ...cleaned,
        { orderCode, productCode, productName, packagingId },
      ];
    });

    setSecondaryPackagings(prevSecondaryPackagings =>
      prevSecondaryPackagings.map(sp => {
        if (sp.orderCode === orderCode && sp.productCode === productCode) {
          return { ...sp, packagingId: packagingId };
        }
        return sp;
      })
    );
  };

  const handleSecondaryVolumeChange = (
    orderCode: string,
    productCode: string,
    value: string,
    orderNumberOfUnitVolumes: number
  ) => {
    const isSecondaryVolumeValid = (numerator: string, orderVolume: number) => {
      if (!numerator) return false;
      const num = Number(numerator);
      if (isNaN(num) || !Number.isInteger(num) || num < 1) return false;
      if (orderVolume === 1 && num > 1) return false;
      if (orderVolume > 1 && num > orderVolume) return false;
      return true;
    };

    if (!isSecondaryVolumeValid(value, orderNumberOfUnitVolumes)) {
      setInvalidSecondaryVolumes(prev => ([...new Set([...prev, `${orderCode}-${productCode}`])]));
    } else {
      setInvalidSecondaryVolumes(prev => prev.filter(v => v !== `${orderCode}-${productCode}`));
    }

    const currentPackagingId = packagingSelections[orderCode]?.[productCode] ?? '';

    setSecondaryPackagings(prev => {
      const filtered = prev.filter(
        sp => !(sp.orderCode === orderCode && sp.productCode === productCode)
      );
      return [
        ...filtered,
        {
          orderCode,
          productCode,
          packagingId: currentPackagingId,
          volumeFormat: `${value}/${orderNumberOfUnitVolumes}`
        }
      ];
    });
  };

  const handleProductWeightChange = (orderCode: string, productCode: string, value: string) => {
    setProductWeights(prev => ({
      ...prev,
      [orderCode]: {
        ...prev[orderCode],
        [productCode]: value,
      },
    }));
  };

  const handleAddPackagingSuccess = (newPackaging: { id: number; name: string }) => {
    setPackagingList(prev => [
      ...prev,
      { value: String(newPackaging.id), label: newPackaging.name }
    ]);

    setPackagingOptionsFull(prev => [...prev, newPackaging]);
    toast.success('Embalagem adicionada com sucesso!');
  };

  const handleUnlinkFromDraft = (checked: boolean) => {
    setIsUnlinkedFromDraft(checked);
    
    if (checked) {
      setRomaneio({
        address: '',
        carrierId: '',
        driverId: '',
        vehicleId: '',
        totalLength: '',
        totalWidth: '',
        totalHeight: '',
        totalWeight: '',
        totalVolume: '',
        mainPackagingId: '',
        secondaryPackagingId: '',
        observations: '',
      });
      
      const initialVolumes: VolumesProps[] = orders.map((order) => ({
        separationOrderId: order.id,
        orderId: order.internalCode,
        linking: globalVinculo,
        volume: 0,
        length: 0,
        height: 0,
        width: 0,
        netWeight: 0,
        numberPackaging: Number(order.volume) || 1,
      }));
      setVolumes(initialVolumes);
      
      setSecondaryPackagings([]);
      setPackagingSelections({});
      setProductWeights({});
      setCombinedOrders(orders);
      
      toast.success('Dados do rascunho removidos. Criando novo romaneio.');
    }
  };

  const handleSubmit = () => {
    operations.handleSubmit(
      romaneio,
      volumes,
      combinedOrders,
      productsByOrder,
      secondaryPackagings,
      packagingSelections,
      productWeights,
      invalidSecondaryVolumes,
      globalVinculo,
      orders,
      setErrorMessage,
      setIsSubmitting,
      onSuccess,
      onClose
    );
  };

  const handleSaveDraft = () => {
    operations.handleSaveDraft(
      romaneio,
      volumes,
      combinedOrders,
      productsByOrder,
      secondaryPackagings,
      packagingSelections,
      productWeights,
      orders,
      isUnlinkedFromDraft,
      setErrorMessage,
      setIsSubmitting,
      onSuccess,
      onClose
    );
  };

  // Derived state
  const missingGeneralInfo = !romaneio.address || !romaneio.carrierId || !romaneio.driverId ||
    !romaneio.vehicleId || !romaneio.mainPackagingId;

  const missingOrderInfo = volumes.some(
    (volume) => !volume.length || !volume.width || !volume.height
  );

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl max-h-[95vh] overflow-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold">Criar Romaneio</DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} className="w-full" onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4 mb-4">
            <TabsTrigger value="general" className={activeTab === 'general' ? 'bg-blue-50' : ''}>
              <div className="flex items-center gap-2">
                <Truck className="h-4 w-4" />
                Informações Gerais
              </div>
            </TabsTrigger>
            <TabsTrigger value="packaging" className={activeTab === 'packaging' ? 'bg-blue-50' : ''}>
              <div className="flex items-center gap-2">
                <Package className="h-4 w-4" />
                Embalagens
              </div>
            </TabsTrigger>
            <TabsTrigger value="orders" className={activeTab === 'orders' ? 'bg-blue-50' : ''}>
              <div className="flex items-center gap-2">
                <Boxes className="h-4 w-4" />
                Pedidos ({combinedOrders.length})
              </div>
            </TabsTrigger>
            <TabsTrigger value="observations" className={activeTab === 'observations' ? 'bg-blue-50' : ''}>
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Observações
              </div>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="general">
            <GeneralInfoTab
              romaneio={romaneio}
              setRomaneio={setRomaneio}
              globalVinculo={globalVinculo}
              setGlobalVinculo={setGlobalVinculo}
              carrierList={carrierList}
              driverList={driverList}
              vehicleList={vehicleList}
              packagingList={packagingList}
              hasDraftAvailable={hasDraftAvailable}
              isUnlinkedFromDraft={isUnlinkedFromDraft}
              handleUnlinkFromDraft={handleUnlinkFromDraft}
              handleSaveDraft={handleSaveDraft}
              setActiveTab={setActiveTab}
              isSubmitting={isSubmitting}
            />
          </TabsContent>

          <TabsContent value="packaging">
            <PackagingTab
              combinedOrders={combinedOrders}
              productsByOrder={productsByOrder}
              loadingOrders={loadingOrders}
              packagingList={packagingList}
              packagingSelections={packagingSelections}
              setPackagingSelections={setPackagingSelections}
              secondaryPackagings={secondaryPackagings}
              setSecondaryPackagings={setSecondaryPackagings}
              productWeights={productWeights}
              setProductWeights={setProductWeights}
              invalidSecondaryVolumes={invalidSecondaryVolumes}
              setInvalidSecondaryVolumes={setInvalidSecondaryVolumes}
              selectedProducts={selectedProducts}
              setSelectedProducts={setSelectedProducts}
              bulkPackagingSelections={bulkPackagingSelections}
              setBulkPackagingSelections={setBulkPackagingSelections}
              appliedFeedback={appliedFeedback}
              setAppliedFeedback={setAppliedFeedback}
              loadProducts={loadProducts}
              handlePackagingChange={handlePackagingChange}
              handleSecondaryVolumeChange={handleSecondaryVolumeChange}
              handleProductWeightChange={handleProductWeightChange}
              duplicateProduct={modalState.duplicateProduct}
              splitProduct={modalState.splitProduct}
              setIsAddPackagingOpen={setIsAddPackagingOpen}
              setActiveTab={setActiveTab}
              handleSaveDraft={handleSaveDraft}
              isSubmitting={isSubmitting}
              isUnlinkedFromDraft={isUnlinkedFromDraft}
            />
          </TabsContent>

          <TabsContent value="orders">
            <OrdersTab
              combinedOrders={combinedOrders}
              volumes={volumes}
              setVolumes={setVolumes}
              handleOrderChange={handleOrderChange}
              handleLengthChange={handleLengthChange}
              handleWidthChange={handleWidthChange}
              handleHeightChange={handleHeightChange}
              setActiveTab={setActiveTab}
              handleSaveDraft={handleSaveDraft}
              isSubmitting={isSubmitting}
              isUnlinkedFromDraft={isUnlinkedFromDraft}
            />
          </TabsContent>

          <TabsContent value="observations">
            <ObservationsTab
              romaneio={romaneio}
              setRomaneio={setRomaneio}
              errorMessage={errorMessage}
              invalidSecondaryVolumes={invalidSecondaryVolumes}
              missingGeneralInfo={missingGeneralInfo}
              missingOrderInfo={missingOrderInfo}
              handleSaveDraft={handleSaveDraft}
              handleSubmit={handleSubmit}
              onClose={onClose}
              isSubmitting={isSubmitting}
              isUnlinkedFromDraft={isUnlinkedFromDraft}
            />
          </TabsContent>
        </Tabs>

        <AddPackagingDialog
          isOpen={isAddPackagingOpen}
          onClose={() => setIsAddPackagingOpen(false)}
          onSuccess={handleAddPackagingSuccess}
        />
      </DialogContent>
    </Dialog>
  );
};

export default MultiVolumeModal; 