
import { useState, useCallback } from 'react';
import { toast } from '@/hooks/use-toast';

interface Module {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
}

interface UserProfile {
  name: string;
  modules: Module[];
}

const DEFAULT_MODULES = [
  {
    id: 'pedidos-expedicao',
    name: 'Pedidos de Expedição',
    description: 'Gerenciamento de pedidos e expedições',
  },
  {
    id: 'romaneios',
    name: '<PERSON><PERSON><PERSON>',
    description: 'Criação e gestão de romaneios',
  },
  {
    id: 'notas-fiscais',
    name: 'Notas Fiscais',
    description: 'Visualização e gestão de notas fiscais',
  },
  {
    id: 'notas-erros',
    name: '<PERSON>erificador de Notas',
    description: 'Gestão de notas fiscais com problemas',
  },
  {
    id: 'usuarios',
    name: 'Usuário<PERSON>',
    description: 'Gestão de usuários do sistema',
  },
  {
    id: 'embalagens',
    name: 'Embalagens',
    description: 'Cadastro e gestão de embalagens',
  },
  {
    id: 'motoristas',
    name: 'Motoristas',
    description: 'Cadastro e gestão de motoristas',
  },
  {
    id: 'transportadoras',
    name: 'Transportadoras',
    description: 'Cadastro e gestão de transportadoras',
  },
  {
    id: 'veiculos',
    name: 'Veículos',
    description: 'Cadastro e gestão de veículos',
  },
  {
    id: 'pesos-produtos',
    name: 'Pesos de Produtos',
    description: 'Gestão de pesos dos produtos',
  },
  {
    id: 'etiquetas',
    name: 'Etiquetas',
    description: 'Geração de etiquetas primárias e secundárias',
  },
];

const INITIAL_PROFILES: UserProfile[] = [
  {
    name: 'Administrador',
    modules: DEFAULT_MODULES.map(module => ({ ...module, enabled: true })),
  },
  {
    name: 'Fiscal',
    modules: DEFAULT_MODULES.map(module => ({
      ...module,
      enabled: ['pedidos-expedicao', 'notas-fiscais', 'notas-erros'].includes(module.id),
    })),
  },
  {
    name: 'Coordenador',
    modules: DEFAULT_MODULES.map(module => ({
      ...module,
      enabled: ['pedidos-expedicao', 'notas-fiscais', 'romaneios', 'usuarios', 'embalagens', 'motoristas', 'transportadoras', 'veiculos', 'pesos-produtos', 'etiquetas'].includes(module.id),
    })),
  },
  {
    name: 'Operador de Obra',
    modules: DEFAULT_MODULES.map(module => ({
      ...module,
      enabled: ['romaneios'].includes(module.id),
    })),
  },
  {
    name: 'Estoquista',
    modules: DEFAULT_MODULES.map(module => ({
      ...module,
      enabled: ['notas-fiscais', 'notas-erros', 'romaneios'].includes(module.id),
    })),
  },
];

export const useModulePermissions = () => {
  const [profiles, setProfiles] = useState<UserProfile[]>(INITIAL_PROFILES);

  const handleModuleToggle = useCallback((profileName: string, moduleId: string, enabled: boolean) => {
    setProfiles(prevProfiles =>
      prevProfiles.map(profile =>
        profile.name === profileName
          ? {
              ...profile,
              modules: profile.modules.map(module =>
                module.id === moduleId ? { ...module, enabled } : module
              ),
            }
          : profile
      )
    );

    toast({
      title: 'Permissão atualizada',
      description: `Módulo ${enabled ? 'ativado' : 'desativado'} para o perfil ${profileName}`,
      variant: 'default',
    });
  }, []);

  const savePermissions = useCallback(() => {
    // Aqui você implementaria a lógica para salvar no backend
    console.log('Salvando permissões:', profiles);
    toast({
      title: 'Permissões salvas',
      description: 'As configurações de módulos foram salvas com sucesso',
      variant: 'default',
    });
  }, [profiles]);

  return {
    profiles,
    handleModuleToggle,
    savePermissions,
  };
};
