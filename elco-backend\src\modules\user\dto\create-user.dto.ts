import { <PERSON><PERSON><PERSON>, <PERSON>NotEmpty, <PERSON>S<PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';

export class CreateUserDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsNotEmpty()
  phone: string;

  @IsEmail()
  email: string;

  @IsString()
  @MinLength(6)
  password: string;

  @IsString()
  @IsNotEmpty()
  role: string; // será a `key` do papel (ex: 'ADMIN')
}
