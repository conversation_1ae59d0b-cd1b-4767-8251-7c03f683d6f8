
import React from 'react';
import MainLayout from '@/layouts/MainLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useModulePermissions } from './hooks/useModulePermissions';
import ProfilePermissions from './components/ProfilePermissions';

const Settings = () => {
  const { profiles, handleModuleToggle, savePermissions } = useModulePermissions();

  return (
    <MainLayout title="Configurações - Controle de Módulos">
      <div className="max-w-6xl mx-auto space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Controle de Módulos por Perfil</CardTitle>
            <CardDescription>
              Selecione um perfil para configurar quais módulos e funcionalidades ele pode acessar no sistema.
            </CardDescription>
          </CardHeader>
        </Card>

        <Card>
          <CardContent className="p-0">
            <Tabs defaultValue={profiles[0]?.name} className="w-full">
              <div className="border-b">
                <TabsList className="h-auto p-0 bg-transparent w-full justify-start rounded-none">
                  {profiles.map((profile) => (
                    <TabsTrigger
                      key={profile.name}
                      value={profile.name}
                      className="rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent px-6 py-4 font-medium"
                    >
                      {profile.name}
                    </TabsTrigger>
                  ))}
                </TabsList>
              </div>

              {profiles.map((profile) => (
                <TabsContent key={profile.name} value={profile.name} className="mt-0">
                  <ProfilePermissions
                    profile={profile}
                    onModuleToggle={handleModuleToggle}
                  />
                </TabsContent>
              ))}
            </Tabs>
          </CardContent>
        </Card>

        <div className="flex justify-end pt-6">
          <Button onClick={savePermissions} size="lg">
            Salvar Configurações
          </Button>
        </div>
      </div>
    </MainLayout>
  );
};

export default Settings;
