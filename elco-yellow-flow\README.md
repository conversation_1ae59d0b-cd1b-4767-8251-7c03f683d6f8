# ELCO Yellow Flow

Sistema de gestão de fluxo logístico desenvolvido com React, TypeScript e Vite.

## 🚀 Tecnologias Utilizadas

- **React** - Biblioteca para construção de interfaces
- **TypeScript** - Linguagem de programação tipada
- **Vite** - Build tool e dev server
- **shadcn/ui** - Componentes de UI reutilizáveis
- **Tailwind CSS** - Framework CSS utilitário
- **React Router** - Roteamento da aplicação

## 📋 Pré-requisitos

- Node.js (versão 18 ou superior)
- npm ou yarn

## 🛠️ Instalação e Configuração

1. **Clone o repositório**
   ```bash
   git clone <URL_DO_REPOSITORIO>
   cd elco-yellow-flow
   ```

2. **Instale as dependências**
   ```bash
   npm install
   ```

3. **Inicie o servidor de desenvolvimento**
   ```bash
   npm run dev
   ```

4. **Acesse a aplicação**
   Abra [http://localhost:5173](http://localhost:5173) no seu navegador.

## 📁 Estrutura do Projeto

```
src/
├── components/          # Componentes reutilizáveis
│   ├── ui/             # Componentes de UI base
│   └── layouts/        # Componentes de layout
├── features/           # Funcionalidades organizadas por domínio
├── hooks/              # Hooks customizados
├── lib/                # Utilitários e configurações
├── pages/              # Páginas da aplicação
├── routes/             # Configuração de rotas
├── services/           # Serviços de API
├── store/              # Gerenciamento de estado
└── utils/              # Funções utilitárias
```

## 🚀 Scripts Disponíveis

- `npm run dev` - Inicia o servidor de desenvolvimento
- `npm run build` - Gera build de produção
- `npm run preview` - Visualiza o build de produção
- `npm run lint` - Executa o linter
- `npm run type-check` - Verifica tipos TypeScript

## 🔧 Configuração de Desenvolvimento

### Variáveis de Ambiente

Crie um arquivo `.env.local` na raiz do projeto:

```env
VITE_API_URL=http://localhost:3000
VITE_APP_NAME=ELCO Yellow Flow
```

## 📦 Deploy

Para fazer o deploy da aplicação:

1. Execute o build de produção:
   ```bash
   npm run build
   ```

2. Os arquivos de produção estarão na pasta `dist/`

3. Faça o upload dos arquivos para seu servidor web

## 🤝 Contribuição

1. Faça um fork do projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo `LICENSE` para mais detalhes.
