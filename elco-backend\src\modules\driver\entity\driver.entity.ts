import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

@Entity('drivers')
export class Driver {
  @PrimaryGeneratedColumn()
  id: string;

  @Column()
  name: string;

  @Column()
  licenseNumber: string;

  @Column()
  phone: string;

  @Column()
  matriculation: string;

  @Column({ type: 'date', nullable: true })
  licenseExpirationDate: Date;

  @Column({ type: 'longblob', nullable: true })
  licenseDocument: Buffer;
}
