

#root {
  width: 100%;
  margin: 0 auto;
}

/* Fix for mobile viewport */
html, body {
  overflow-x: hidden;
  position: relative;
  width: 100%;
  height: 100%;
}

/* Table specific styles for mobile */
.table-container {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

/* Mobile-first padding and spacing */
.mobile-container {
  padding: 0.75rem;
}

@media (min-width: 640px) {
  .mobile-container {
    padding: 1rem;
  }
}

@media (min-width: 768px) {
  .mobile-container {
    padding: 1.5rem;
  }
}

/* Fix for modal on mobile */
.mobile-modal {
  max-width: calc(100vw - 1rem);
  width: 100%;
  margin: 0.5rem;
}

@media (min-width: 640px) {
  .mobile-modal {
    max-width: 28rem;
    margin: 0 auto;
  }
}

@media (min-width: 1024px) {
  .mobile-modal {
    max-width: 80vw;
  }
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}

.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}

.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

/* Mobile breakpoints */
@media (max-width: 640px) {
  .mobile-hidden {
    display: none;
  }
  
  .mobile-full-width {
    width: 100%;
  }
  
  .mobile-text-sm {
    font-size: 0.875rem;
  }
  
  .mobile-py-2 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }
  
  .mobile-px-2 {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
  
  /* Fix for button stacking on mobile */
  .mobile-stack {
    display: flex;
    flex-direction: column;
  }
  
  .mobile-stack > * {
    margin-top: 0.5rem;
    width: 100%;
  }
  
  .mobile-stack > *:first-child {
    margin-top: 0;
  }
  
  /* Fix table display on mobile */
  .mobile-table-row {
    display: flex;
    flex-direction: column;
    padding: 0.75rem;
    border-bottom: 1px solid #e5e7eb;
  }
  
  .mobile-table-row > * {
    padding: 0.25rem 0;
    border: none;
  }
  
  .mobile-table-row > *:before {
    content: attr(data-label);
    font-weight: 500;
    padding-right: 0.5rem;
    color: #6b7280;
  }
}

/* Improved Dialog/Modal for mobile */
@media (max-width: 640px) {
  [data-radix-popper-content-wrapper] {
    position: fixed !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    top: auto !important;
    transform: none !important;
    max-width: 100% !important;
  }
}

/* Ensure buttons stack properly on mobile forms */
@media (max-width: 640px) {
  .form-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .form-buttons > * {
    width: 100%;
  }
}

/* Adjust dialog padding for mobile */
@media (max-width: 640px) {
  .mobile-dialog {
    padding: 1rem;
  }
}

/* Fix for form input spacing on mobile */
@media (max-width: 640px) {
  .mobile-form-group {
    margin-bottom: 1rem;
  }
  
  .mobile-form-group label {
    margin-bottom: 0.25rem;
  }
}

