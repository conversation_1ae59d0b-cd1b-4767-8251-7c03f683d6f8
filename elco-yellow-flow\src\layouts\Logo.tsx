import React from 'react';
import { cn } from '@/lib/utils';
import logoImage from '@/images/logo.png';

interface LogoProps {
  className?: string;
}

const Logo: React.FC<LogoProps> = ({ className }) => {
  return (
    <div className={cn("flex items-center", className)}>
      <img 
        src={logoImage} 
        alt="Elco Logo" 
        className="h-auto w-auto max-h-8 max-w-32 object-contain"
      />
    </div>
  );
};

export default Logo;
