import React, { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { cn } from '@/lib/utils';
import Logo from './Logo';
import {
  ShoppingCart,
  FileCheck,
  AlertTriangle,
  LogOut,
  FileSpreadsheet,
  ChevronLeft,
  ChevronRight,
  Users,
  ClipboardPlus,
  Boxes,
  Truck,
  Building,
  BusFront,
  ChevronDown,
  ChevronUp,
  Weight,
  Mail,
  MapPin,
} from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { toast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/useAuth';
import { AnimatePresence, motion } from 'framer-motion';
import { Button } from '@/components/ui/button';

interface SidebarProps {
  onCollapseChange?: (collapsed: boolean) => void;
}

const menuItems = [
  {
    title: 'Pedidos de Expedição',
    icon: <ShoppingCart className="h-5 w-5" />,
    path: '/pedidos-expedicao',
    type: 'link',
  },
  {
    title: 'Romaneios',
    icon: <FileSpreadsheet className="h-5 w-5" />,
    path: '/romaneios',
    type: 'link',
  },
  {
    title: 'Notas Fiscais',
    icon: <FileCheck className="h-5 w-5" />,
    path: '/notas-fiscais',
    type: 'link',
  },
  {
    title: 'Verificador de Notas',
    icon: <AlertTriangle className="h-5 w-5" />,
    path: '/notas-erros',
    type: 'link',
  },
  {
    title: 'Cadastros',
    icon: <ClipboardPlus className="h-5 w-5" />,
    path: '/cadastros',
    type: 'submenu',
    children: [
      {
        title: 'Usuários',
        icon: <Users className="h-4 w-4" />,
        path: '/users',
      },
      {
        title: 'Embalagens',
        icon: <Boxes className="h-4 w-4" />,
        path: '/packaging',
      },
      {
        title: 'Motoristas',
        icon: <Truck className="h-4 w-4" />,
        path: '/drivers',
      },
      {
        title: 'Transportadores',
        icon: <Building className="h-4 w-4" />,
        path: '/transporters',
      },
      {
        title: 'Veículos',
        icon: <BusFront className="h-4 w-4" />,
        path: '/vehicles',
      },
      {
        title: 'Pesos de Produtos',
        icon: <Weight className="h-4 w-4" />,
        path: '/product-weights',
      },
      {
        title: 'Email do Fornecedor',
        icon: <Mail className="h-4 w-4" />,
        path: '/supplier-emails',
      },
      {
        title: 'Endereços',
        icon: <MapPin className="h-4 w-4" />,
        path: '/addresses',
      },
    ],
  },
];

// Função para filtrar itens do menu baseado no perfil do usuário
const getFilteredMenuItems = (userRole: string) => {
  // Perfis que NÃO podem ver a aba de cadastros
  const restrictedProfiles = ['Fiscal', 'Operador de Obra', 'Estoquista'];
  
  // Operador de Obra só pode ver romaneios
  if (userRole === 'Operador de Obra') {
    return menuItems.filter(item => item.title === 'Romaneios');
  }
  
  if (restrictedProfiles.includes(userRole)) {
    // Remove apenas o item 'Cadastros' para perfis restritos (Fiscal e Estoquista)
    return menuItems.filter(item => item.title !== 'Cadastros');
  }
  
  // Administrador e Coordenador podem ver todos os itens
  return menuItems;
};

const Sidebar: React.FC<SidebarProps> = ({ onCollapseChange }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const { logout, user } = useAuth();
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [openSubmenu, setOpenSubmenu] = useState<string | null>(null);

  // Filtrar itens do menu baseado no perfil do usuário
  const filteredMenuItems = getFilteredMenuItems(user?.role || '');

  useEffect(() => {
    const currentSubmenu = filteredMenuItems.find(
      (item) => item.type === 'submenu' && item.children.some(
        (child) => location.pathname.startsWith(child.path)
      )
    );
    if (currentSubmenu) {
      setOpenSubmenu(currentSubmenu.title);
    } else {
      setOpenSubmenu(null);
    }
  }, [location.pathname, filteredMenuItems]);

  useEffect(() => {
    const topLevelPaths = filteredMenuItems
      .filter(item => item.type === 'link')
      .map(item => item.path);

    if (topLevelPaths.includes(location.pathname)) {
      setMobileMenuOpen(false);
    }
  }, [location.pathname, filteredMenuItems]);

  const toggleSidebar = () => {
    const newCollapsedState = !isCollapsed;
    setIsCollapsed(newCollapsedState);
    onCollapseChange?.(newCollapsedState);
  };

  const toggleMobileMenu = () => setMobileMenuOpen(!mobileMenuOpen);

  const handleLogout = () => {
    localStorage.removeItem('isAuthenticated');
    toast({
      title: 'Sessão encerrada',
      description: 'Você foi desconectado com sucesso',
    });
    navigate('/login');
  };

  const renderMenuItem = (item: any, isChild = false) => {
    const isActive = item.type === 'link' && location.pathname === item.path;
    const isSubmenuOpen = openSubmenu === item.title;
    const isParentActive = isSubmenuOpen || (item.children && item.children.some((child: any) => location.pathname.startsWith(child.path)));

    if (item.type === 'link') {
      return (
        <li key={item.path} className="mb-1">
          <Link
            to={item.path}
            onClick={() => setMobileMenuOpen(false)}
            className={cn(
              'group flex items-center gap-3 rounded-xl px-3 py-3 text-sm font-medium transition-all duration-200 hover:bg-elco-600/10 relative overflow-hidden',
              isActive
                ? 'bg-gradient-to-r from-elco-600 to-elco-700 text-white shadow-lg transform hover:scale-[1.02]'
                : 'text-gray-600 hover:text-elco-700',
              isCollapsed && !isMobile && 'justify-center px-0 w-12 mx-auto',
              isChild && 'text-sm py-2.5 ml-4'
            )}
          >
            <div className={cn(
              'flex items-center justify-center flex-shrink-0',
              isActive ? 'text-white' : 'text-gray-500 group-hover:text-elco-600',
              isChild && 'w-4 h-4'
            )}>
              {item.icon}
            </div>
            {(!isCollapsed || isMobile) && (
              <span className="truncate font-medium">{item.title}</span>
            )}
            {isActive && (
              <div className="absolute inset-0 bg-gradient-to-r from-elco-600/20 to-elco-700/20 rounded-xl" />
            )}
          </Link>
        </li>
      );
    } else if (item.type === 'submenu') {
      return (
        <li key={item.title} className="mb-1">
          <button
            onClick={() => setOpenSubmenu(isSubmenuOpen ? null : item.title)}
            className={cn(
              "group flex items-center w-full gap-3 rounded-xl px-3 py-3 text-sm font-medium transition-all duration-200 hover:bg-elco-600/10 relative overflow-hidden",
              isParentActive
                ? 'bg-elco-50 text-elco-800 shadow-sm'
                : 'text-gray-600 hover:text-elco-700',
              isCollapsed && !isMobile && 'justify-center px-0 w-12 mx-auto'
            )}
          >
            <div className={cn(
              "flex items-center justify-center flex-shrink-0",
              isParentActive ? 'text-elco-700' : 'text-gray-500 group-hover:text-elco-600'
            )}>
              {item.icon}
            </div>
            {(!isCollapsed || isMobile) && (
              <>
                <span className="flex-1 text-left truncate font-medium">{item.title}</span>
                <div className={cn(
                  "transition-transform duration-200",
                  isSubmenuOpen ? 'rotate-180' : 'rotate-0'
                )}>
                  <ChevronDown className="h-4 w-4" />
                </div>
              </>
            )}
          </button>

          {(!isCollapsed || isMobile) && (
            <AnimatePresence initial={false}>
              {isSubmenuOpen && (
                <motion.ul
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.2, ease: 'easeInOut' }}
                  className="mt-1 space-y-1 overflow-hidden pl-4"
                >
                  {item.children.map((child: any) => (
                    <li key={child.path}>
                      <Link
                        to={child.path}
                        className={cn(
                          'flex items-center gap-3 rounded-lg px-3 py-2.5 text-sm font-medium hover:bg-elco-600/10 text-gray-600 transition-all duration-200 relative',
                          location.pathname === child.path && 'bg-gradient-to-r from-elco-600 to-elco-700 text-white shadow-md transform hover:scale-[1.02]'
                        )}
                      >
                        <div className={cn(
                          'flex items-center justify-center',
                          location.pathname === child.path ? 'text-white' : 'text-gray-500'
                        )}>
                          {child.icon}
                        </div>
                        <span className="truncate">{child.title}</span>
                      </Link>
                    </li>
                  ))}
                </motion.ul>
              )}
            </AnimatePresence>
          )}
        </li>
      );
    }
  };

  if (isMobile) {
    return (
      <>
        <Button
          onClick={toggleMobileMenu}
          className="fixed top-4 left-4 z-50 rounded-full p-2 bg-white shadow-lg border-0 hover:shadow-xl transition-all duration-200"
          variant="ghost"
          size="icon"
        >
          <div className={cn(
            "transition-transform duration-200",
            mobileMenuOpen ? 'rotate-90' : 'rotate-0'
          )}>
            {mobileMenuOpen ? <ChevronLeft size={20} /> : <ChevronRight size={20} />}
          </div>
        </Button>

        <AnimatePresence>
          {mobileMenuOpen && (
            <>
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40"
                onClick={() => setMobileMenuOpen(false)}
              />

              <motion.aside
                initial={{ x: '-100%', opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                exit={{ x: '-100%', opacity: 0 }}
                transition={{ type: 'spring', damping: 25, stiffness: 200 }}
                className="fixed left-0 top-0 z-50 flex h-full flex-col bg-white/95 backdrop-blur-xl shadow-2xl w-80 border-r border-gray-100"
              >
                <div className="p-6 border-b border-gray-100">
                  <Logo />
                </div>

                <nav className="flex-1 overflow-auto p-4 space-y-2">
                  <ul className="space-y-1">
                    {filteredMenuItems.map((item) => renderMenuItem(item))}
                  </ul>
                </nav>

                <div className="border-t border-gray-100 p-4">
                  <motion.button
                    onClick={handleLogout}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className="group relative flex items-center gap-3 w-full rounded-xl px-4 py-3 text-sm font-medium overflow-hidden transition-all duration-300 hover:shadow-lg"
                  >
                    {/* Gradient background */}
                    <div className="absolute inset-0 bg-gradient-to-r from-red-500 via-red-600 to-red-700 opacity-90 group-hover:opacity-100 transition-opacity duration-300" />
                    <div className="absolute inset-0 bg-gradient-to-r from-red-600/20 to-red-800/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                    {/* Shine effect */}
                    <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 transform translate-x-[-100%] group-hover:translate-x-[200%] transition-transform duration-700" />
                    </div>

                    <LogOut className="relative h-5 w-5 text-white drop-shadow-sm" />
                    <span className="relative text-white font-semibold drop-shadow-sm">Sair</span>

                    {/* Bottom glow */}
                    <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-0 group-hover:w-3/4 h-0.5 bg-white/40 rounded-full transition-all duration-300" />
                  </motion.button>
                </div>
              </motion.aside>
            </>
          )}
        </AnimatePresence>
      </>
    );
  }

  return (
    <motion.aside
      initial={false}
      animate={{
        width: isCollapsed ? 80 : 280
      }}
      transition={{ duration: 0.3, ease: 'easeInOut' }}
      className="fixed left-0 top-0 z-50 flex h-full flex-col bg-white/95 backdrop-blur-xl shadow-xl border-r border-gray-100"
    >
      <div className="p-4 border-b border-gray-100 flex items-center justify-between min-h-[80px]">
        <motion.div
          animate={{ opacity: isCollapsed ? 0 : 1 }}
          transition={{ duration: 0.2 }}
          className="overflow-hidden"
        >
          {!isCollapsed && <Logo />}
        </motion.div>
        <Button
          onClick={toggleSidebar}
          className="rounded-full p-2 hover:bg-elco-50 hover:text-elco-700 transition-all duration-200 ml-auto"
          variant="ghost"
          size="icon"
        >
          <motion.div
            animate={{ rotate: isCollapsed ? 180 : 0 }}
            transition={{ duration: 0.3 }}
          >
            <ChevronLeft size={18} />
          </motion.div>
        </Button>
      </div>

      <nav className="flex-1 overflow-auto p-4 space-y-2">
        <ul className="space-y-1">
          {filteredMenuItems.map((item) => renderMenuItem(item))}
        </ul>
      </nav>

      <div className="border-t border-gray-100 p-4">
        <motion.button
          onClick={handleLogout}
          whileHover={{ scale: isCollapsed ? 1.05 : 1.02 }}
          whileTap={{ scale: 0.98 }}
          className={cn(
            'group relative flex items-center gap-3 w-full rounded-xl px-4 py-3 text-sm font-medium overflow-hidden transition-all duration-300 hover:shadow-lg',
            isCollapsed && 'justify-center px-2'
          )}
        >
          {/* Gradient background */}
          <div className="absolute inset-0 bg-gradient-to-r from-red-500 via-red-600 to-red-700 opacity-90 group-hover:opacity-100 transition-opacity duration-300" />
          <div className="absolute inset-0 bg-gradient-to-r from-red-600/20 to-red-800/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

          {/* Shine effect */}
          <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 transform translate-x-[-100%] group-hover:translate-x-[200%] transition-transform duration-700" />
          </div>

          <LogOut className="relative h-5 w-5 flex-shrink-0 text-white drop-shadow-sm group-hover:rotate-12 transition-transform duration-300" />
          {!isCollapsed && (
            <span className="relative text-white font-semibold drop-shadow-sm group-hover:translate-x-1 transition-transform duration-300">Sair</span>
          )}

          {/* Bottom glow */}
          <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-0 group-hover:w-3/4 h-0.5 bg-white/40 rounded-full transition-all duration-300" />

          {/* Top glow */}
          <div className="absolute -top-1 left-1/2 transform -translate-x-1/2 w-0 group-hover:w-1/2 h-0.5 bg-white/40 rounded-full transition-all duration-300" />
        </motion.button>
      </div>
    </motion.aside>
  );
};

export default Sidebar;
