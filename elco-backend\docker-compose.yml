services:
  db:
    image: mysql/mysql-server:latest
    container_name: my-mysql
    restart: always
    environment:
      - MYSQL_DATABASE=elco_db
      - MYSQL_ROOT_PASSWORD=root
    ports:
      - '3306:3306'
    volumes:
      - mysql-volume:/var/lib/mysql
volumes:
  mysql-volume:
    driver: local


# docker exec -it my-mysql bash
# mysql -u root -p
# CREATE DATABASE MYSQLTESTE;
# update mysql.user set host='%' where user='root';
# FLUSH PRIVILEGES;
# conectar pelo backend que configura a conexão para o dbeaver