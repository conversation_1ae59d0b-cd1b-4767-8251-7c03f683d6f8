// src/config/env.validation.ts
import * as <PERSON><PERSON> from 'joi';

export const envSchema = Jo<PERSON>.object({
    // JWT
    //   JWT_SECRET: Joi.string().required(),
    //   JWT_EXPIRES_IN: Joi.string().default('1d'),

    // Banco de dados MySQL
    DB_HOST: Joi.string().required(),
    DB_PORT: Joi.number().default(3306),
    DB_USER: Joi.string().required(),
    DB_PASS: Joi.string().required(),
    DB_NAME: Joi.string().required(),

    // Servidor
    //   PORT: Joi.number().default(3000),

    // Outras variáveis opcionais
    //   NODE_ENV: Joi.string().valid('development', 'production', 'test').default('development'),
});
