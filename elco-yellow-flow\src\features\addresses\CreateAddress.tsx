import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import MainLayout from '@/layouts/MainLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from '@/hooks/use-toast';
import { MapPin, ArrowLeft, Plus } from 'lucide-react';
import { api } from '@/lib/api';

const addressSchema = z.object({
  projectCode: z.string().min(1, 'Supplier is required'),
  address: z.string().min(5, 'Address must be at least 5 characters'),
  projectName: z.string().min(1, 'Project name is required'),
});

type AddressFormData = z.infer<typeof addressSchema>;

type Supplier = {
  projectCode: number;
  projectDescription: string;
};

const CreateAddress: React.FC = () => {
  const navigate = useNavigate();

  const form = useForm<AddressFormData>({
    resolver: zodResolver(addressSchema),
    defaultValues: {
      projectCode: '',
      address: '',
      projectName: '',
    },
  });

  const [supplierOptions, setSupplierOptions] = React.useState<Supplier[]>([]);
  const [loadingSuppliers, setLoadingSuppliers] = React.useState(false);
  const [supplierInput, setSupplierInput] = React.useState('');
  const [selectedSupplier, setSelectedSupplier] = React.useState<{ projectCode: number; projectDescription: string } | null>(null);

  const fetchSuppliers = async (search: string) => {
    setLoadingSuppliers(true);
    try {
      const res = await api.get(`/supplier-emails/projects?search=${encodeURIComponent(search)}`);
      setSupplierOptions(res.data);
    } catch (e) {
      setSupplierOptions([]);
    } finally {
      setLoadingSuppliers(false);
    }
  };

  const onSupplierInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSupplierInput(value);
    if (value.length >= 4) {
      fetchSuppliers(value);
    } else {
      setSupplierOptions([]);
    }
  };

  const onSupplierSelect = (projectCode: number, projectDescription: string) => {
    form.setValue('projectCode', String(projectCode));
    form.setValue('projectName', projectDescription);
    setSupplierInput(projectDescription);
    setSelectedSupplier({ projectCode, projectDescription });
    setSupplierOptions([]);
  };

  const onSubmit = async (data: AddressFormData) => {
    try {
      await api.post('/addresses', data);
      toast({
        title: 'Sucesso!',
        description: 'Endereço cadastrado com sucesso.',
      });
      navigate('/addresses');
    } catch (error) {
      toast({
        title: 'Erro!',
        description: 'Erro ao cadastrar endereço',
        variant: 'destructive',
      });
    }
  };

  return (
    <MainLayout title="Novo Endereço">
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate('/addresses')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Voltar
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5 text-elco-600" />
              Cadastrar Novo Endereço
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="projectCode"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Fornecedor</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Input
                              type="text"
                              placeholder="Digite pelo menos 4 letras para buscar"
                              value={supplierInput}
                              onChange={onSupplierInputChange}
                              autoComplete="off"
                            />
                            {loadingSuppliers && (
                              <div className="absolute right-2 top-2 text-xs text-gray-400">Carregando...</div>
                            )}
                            {supplierOptions.length > 0 && (
                              <ul className="absolute z-10 bg-white border w-full mt-1 max-h-40 overflow-auto shadow-lg rounded">
                                {supplierOptions.map((supplier) => (
                                  <li
                                    key={supplier.projectCode}
                                    className="px-3 py-2 hover:bg-gray-100 cursor-pointer"
                                    onClick={() => onSupplierSelect(supplier.projectCode, supplier.projectDescription)}
                                  >
                                    {supplier.projectDescription}
                                  </li>
                                ))}
                              </ul>
                            )}
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="address"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Endereço</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Digite o endereço completo"
                          rows={3}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex gap-2">
                  <Button type="submit" className="flex items-center gap-2">
                    <Plus className="h-4 w-4" />
                    Cadastrar Endereço
                  </Button>
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => navigate('/addresses')}
                  >
                    Cancelar
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};

export default CreateAddress;
