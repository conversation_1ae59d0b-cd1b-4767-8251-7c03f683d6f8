
import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useNavigate, useParams } from 'react-router-dom';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import MainLayout from '@/layouts/MainLayout';
import {
    Card, CardHeader, CardTitle, CardDescription, CardContent,
} from '@/components/ui/card';
import {
    Form, FormField, FormItem, FormLabel, FormControl, FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Edit, ArrowLeft, Truck, Building, MapPin, QrCode } from 'lucide-react';
import { api } from '@/lib/api';
import { toast } from '@/components/ui/use-toast';

const formSchema = z.object({
    name: z.string().min(3, 'Nome obrigatório'),
    cnpj: z.string().regex(/^\d{2}\.\d{3}\.\d{3}\/\d{4}-\d{2}$/, 'CNPJ inválido'),
    phone: z.string().min(10, 'Telefone obrigatório'),
    department: z.string().min(2, 'Departamento obrigatório'),
    zipCode: z.string().regex(/^\d{5}-\d{3}$/, 'CEP inválido'),
    city: z.string().min(2, 'Cidade obrigatória'),
    state: z.string().length(2, 'Estado inválido'),
    erpCode: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

interface TransporterData {
    name: string;
    cnpj: string;
    phone: string;
    department?: string;
    zipCode?: string;
    city?: string;
    state?: string;
    erpCode?: string;
}

const EditTransporter = () => {
    const { id } = useParams();
    const navigate = useNavigate();
    const [isLoading, setIsLoading] = useState(true);
    const [isLoadingCep, setIsLoadingCep] = useState(false);

    const form = useForm<FormValues>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            name: '',
            cnpj: '',
            phone: '',
            department: '',
            zipCode: '',
            city: '',
            state: '',
            erpCode: '',
        },
    });

    useEffect(() => {
        const fetchData = async () => {
            try {
                const { data } = await api.get<TransporterData>(`/transporters/view/${id}`);
                form.reset({
                    name: data.name,
                    cnpj: data.cnpj,
                    phone: data.phone,
                    department: data.department || '',
                    zipCode: data.zipCode || '',
                    city: data.city || '',
                    state: data.state || '',
                    erpCode: data.erpCode || '',
                });
            } catch {
                toast.error('Erro ao carregar transportadora');
                navigate('/transporters');
            } finally {
                setIsLoading(false);
            }
        };

        if (id) fetchData();
    }, [id, form, navigate]);

    const onSubmit = async (data: FormValues) => {
        try {
            await api.put(`/transporters/update/${id}`, data);
            toast.success('Transportadora atualizada com sucesso!');
            navigate('/transporters');
        } catch {
            toast.error('Erro ao atualizar transportadora');
        }
    };

    const maskPhone = (value: string) => {
        return value
            .replace(/\D/g, '')
            .replace(/^(\d{2})(\d)/, '($1) $2')
            .replace(/(\d{5})(\d{1,4})/, '$1-$2')
            .replace(/(-\d{4})\d+?$/, '$1');
    };

    const maskCnpj = (value: string) => {
        return value
            .replace(/\D/g, '')
            .replace(/^(\d{2})(\d)/, '$1.$2')
            .replace(/^(\d{2})\.(\d{3})(\d)/, '$1.$2.$3')
            .replace(/\.(\d{3})(\d)/, '.$1/$2')
            .replace(/(\d{4})(\d)/, '$1-$2')
            .slice(0, 18);
    };

    const maskCep = (value: string) => {
        return value
            .replace(/\D/g, '')
            .replace(/^(\d{5})(\d)/, '$1-$2')
            .slice(0, 9);
    };

    const fetchAddressByCep = async (cep: string) => {
        if (!cep || cep.length !== 9) return;
        
        setIsLoadingCep(true);
        try {
            const cleanCep = cep.replace('-', '');
            const response = await fetch(`https://viacep.com.br/ws/${cleanCep}/json/`);
            const data = await response.json();
            
            if (!data.erro) {
                form.setValue('city', data.localidade);
                form.setValue('state', data.uf);
                toast.success('Endereço encontrado!');
            } else {
                toast.error('CEP não encontrado');
            }
        } catch (error) {
            toast.error('Erro ao buscar CEP');
        } finally {
            setIsLoadingCep(false);
        }
    };

    return (
        <MainLayout title="Editar Transportadora">
            <div className="max-w-3xl mx-auto">
                <Button variant="ghost" className="mb-4" onClick={() => navigate('/transporters')}>
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Voltar
                </Button>
                <Card>
                    <CardHeader>
                        <div className="flex items-center gap-2">
                            <Edit className="h-6 w-6 text-primary" />
                            <CardTitle>Editar Transportadora</CardTitle>
                        </div>
                        <CardDescription>Atualize os dados da transportadora</CardDescription>
                    </CardHeader>
                    <CardContent>
                        {isLoading ? (
                            <div className="flex justify-center py-8">
                                <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary" />
                            </div>
                        ) : (
                            <Form {...form}>
                                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                                    <FormField
                                        control={form.control}
                                        name="name"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Nome</FormLabel>
                                                <FormControl><Input {...field} /></FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                    
                                    <FormField
                                        control={form.control}
                                        name="department"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel className="flex items-center gap-2">
                                                    <Building className="h-4 w-4" />
                                                    Departamento
                                                </FormLabel>
                                                <FormControl>
                                                    <Input
                                                        placeholder="Ex: Logística"
                                                        {...field}
                                                    />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                    
                                    <FormField
                                        control={form.control}
                                        name="cnpj"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>CNPJ</FormLabel>
                                                <FormControl>
                                                    <Input
                                                        placeholder="00.000.000/0000-00"
                                                        value={field.value}
                                                        onChange={(e) => field.onChange(maskCnpj(e.target.value))}
                                                    />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                    
                                    <FormField
                                        control={form.control}
                                        name="phone"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Telefone</FormLabel>
                                                <FormControl>
                                                    <Input
                                                        placeholder="(00) 00000-0000"
                                                        value={maskPhone(field.value)}
                                                        onChange={(e) => {
                                                            field.onChange(maskPhone(e.target.value));
                                                        }}
                                                    />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                    
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <FormField
                                            control={form.control}
                                            name="zipCode"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel className="flex items-center gap-2">
                                                        <MapPin className="h-4 w-4" />
                                                        CEP
                                                    </FormLabel>
                                                    <div className="flex space-x-2">
                                                        <FormControl>
                                                            <Input
                                                                placeholder="00000-000"
                                                                value={field.value}
                                                                onChange={(e) => {
                                                                    const maskedValue = maskCep(e.target.value);
                                                                    field.onChange(maskedValue);
                                                                }}
                                                                onBlur={() => fetchAddressByCep(field.value)}
                                                            />
                                                        </FormControl>
                                                        <Button 
                                                            type="button" 
                                                            variant="outline"
                                                            disabled={isLoadingCep || field.value.length !== 9}
                                                            onClick={() => fetchAddressByCep(field.value)}
                                                        >
                                                            {isLoadingCep ? (
                                                                <div className="animate-spin h-4 w-4 border-2 border-primary rounded-full border-t-transparent" />
                                                            ) : 'Buscar'}
                                                        </Button>
                                                    </div>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                        
                                        <FormField
                                            control={form.control}
                                            name="erpCode"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel className="flex items-center gap-2">
                                                        <QrCode className="h-4 w-4" />
                                                        Código ERP Externo
                                                    </FormLabel>
                                                    <FormControl>
                                                        <Input
                                                            placeholder="Código de sistema externo"
                                                            {...field}
                                                        />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                    </div>
                                    
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <FormField
                                            control={form.control}
                                            name="city"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>Cidade</FormLabel>
                                                    <FormControl>
                                                        <Input {...field} readOnly={isLoadingCep} />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                        
                                        <FormField
                                            control={form.control}
                                            name="state"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>Estado</FormLabel>
                                                    <FormControl>
                                                        <Input {...field} readOnly={isLoadingCep} maxLength={2} />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                    </div>

                                    <div className="flex justify-end space-x-4">
                                        <Button type="submit">
                                            <Edit className="mr-2 h-4 w-4" />
                                            Salvar Alterações
                                        </Button>
                                    </div>
                                </form>
                            </Form>
                        )}
                    </CardContent>
                </Card>
            </div>
        </MainLayout>
    );
};

export default EditTransporter;
