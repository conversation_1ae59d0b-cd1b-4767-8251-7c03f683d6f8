
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';

interface Module {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
}

interface ModulePermissionCardProps {
  profileName: string;
  modules: Module[];
  onModuleToggle: (profileName: string, moduleId: string, enabled: boolean) => void;
}

const ModulePermissionCard: React.FC<ModulePermissionCardProps> = ({
  profileName,
  modules,
  onModuleToggle,
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{profileName}</CardTitle>
        <CardDescription>
          Configure quais módulos este perfil pode acessar
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {modules.map((module) => (
          <div key={module.id} className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor={`${profileName}-${module.id}`} className="text-base">
                {module.name}
              </Label>
              <p className="text-sm text-muted-foreground">
                {module.description}
              </p>
            </div>
            <Switch
              id={`${profileName}-${module.id}`}
              checked={module.enabled}
              onCheckedChange={(checked) => 
                onModuleToggle(profileName, module.id, checked)
              }
            />
          </div>
        ))}
      </CardContent>
    </Card>
  );
};

export default ModulePermissionCard;
