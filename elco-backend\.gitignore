# Compiled output
/dist
/build
/.turbo

# Node modules
/node_modules

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
pnpm-debug.log*
lerna-debug.log*

# OS
.DS_Store
Thumbs.db

# Test coverage
/coverage
/.nyc_output

# Env files
.env
.env.local
.env.*.local

# Temp files
.tmp
.temp
.cache
*.bak
*.swp

# TypeScript cache
*.tsbuildinfo

# IDEs and editors
.idea/
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.sublime-workspace

# System files
*.pid
*.pid.lock
pids
*.seed

# Diagnostic reports
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# Lock files (opcional — remova se quiser versionar lockfile!)
package-lock.json
yarn.lock
pnpm-lock.yaml
