import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { ProtectedRoute } from './ProtectedRoute';
import { useAuth } from '@/hooks/useAuth';

// Import pages from feature folders
import Login from '@/features/auth/Login';
import ForgotPassword from '@/features/auth/ForgotPassword';
import NotasFiscais from '@/features/shipping-notes/NotasFiscais';
import NotasErros from '@/features/shipping-notes/NotasErros';
import Romaneios from '@/features/manifestos/Romaneios';
import Profile from '@/features/profile/Profile';
import Settings from '@/features/settings/Settings';
import Contact from '@/features/contact/Contact';
import CreateUser from '@/features/users/CreateUser';
import EditUser from '@/features/users/EditUser';
import UsersList from '@/features/users/UsersList';
import NotFound from '@/pages/NotFound';
import PedidosExpedicao from '@/pages/PedidosExpedicao';
import PackagingList from '@/features/packaging/PackagingList';
import CreatePackaging from '@/features/packaging/CreatePackaging';
import EditPackaging from '@/features/packaging/EditPackaging';
import DriverCreate from '@/features/driver/DriverCreate';
import DriverEdit from '@/features/driver/DriverEdit';
import DriverList from '@/features/driver/DriverList';
import EditTransporter from '@/features/transporter/EditTransporter';
import CreateTransporter from '@/features/transporter/CreateTransporter';
import TransporterList from '@/features/transporter/TransporterList';
import VehicleList from '@/features/vehicle/VehicleList';
import CreateVehicle from '@/features/vehicle/CreateVehicle';
import EditVehicle from '@/features/vehicle/EditVehicle';
import EtiquetaPrimaria from '@/pages/EtiquetaPrimaria';
import EtiquetaSecundaria from '@/pages/EtiquetaSecundaria';
import ProductWeights from '@/features/products/ProductWeights';
import AddressList from '@/features/addresses/AddressList';
import CreateAddress from '@/features/addresses/CreateAddress';
import EditAddress from '@/features/addresses/EditAddress';
import FornecedorEmailList from '@/features/supplier-email/FornecedorEmailList';
import CreateFornecedorEmail from '@/features/supplier-email/CreateFornecedorEmail';
import EditFornecedorEmail from '@/features/supplier-email/EditFornecedorEmail';
import VisualizarXml from '@/pages/VisualizarXml';

// Componente para redirecionar baseado no perfil do usuário
const HomeRedirect: React.FC = () => {
  const { user } = useAuth();
  
  // Se o usuário é Operador de Obra, redirecionar para romaneios
  if (user?.role === 'Operador de Obra') {
    return <Navigate to="/romaneios" replace />;
  }
  
  // Para outros perfis, redirecionar para pedidos de expedição
  return <Navigate to="/pedidos-expedicao" replace />;
};

export const AppRoutes: React.FC = () => {
  return (
    <Routes>
      <Route path="/login" element={<Login />} />
      <Route path="/forgot-password" element={<ForgotPassword />} />
      <Route path="/contato" element={<Contact />} />
      <Route path="/" element={<ProtectedRoute><HomeRedirect /></ProtectedRoute>} />

      {/* Rotas principais - acessíveis a todos os perfis autenticados */}
      <Route path="/pedidos-expedicao" element={<ProtectedRoute><PedidosExpedicao /></ProtectedRoute>} />
      <Route path="/notas-fiscais" element={<ProtectedRoute><NotasFiscais /></ProtectedRoute>} />
      <Route path="/notas-erros" element={<ProtectedRoute><NotasErros /></ProtectedRoute>} />
      <Route path="/romaneios" element={<ProtectedRoute><Romaneios /></ProtectedRoute>} />
      <Route path="/romaneios/pedido/:pedidoId" element={<ProtectedRoute><Romaneios /></ProtectedRoute>} />
      <Route path="/profile" element={<ProtectedRoute><Profile /></ProtectedRoute>} />
      <Route path="/settings" element={<ProtectedRoute><Settings /></ProtectedRoute>} />

      {/* Rotas de cadastros - restritas a Administrador e Coordenador */}
      <Route path="/users" element={<ProtectedRoute><UsersList /></ProtectedRoute>} />
      <Route path="/users/create" element={<ProtectedRoute><CreateUser /></ProtectedRoute>} />
      <Route path="/users/edit/:userId" element={<ProtectedRoute><EditUser /></ProtectedRoute>} />

      <Route path="/packaging" element={<ProtectedRoute><PackagingList /></ProtectedRoute>} />
      <Route path="/packaging/create" element={<ProtectedRoute><CreatePackaging /></ProtectedRoute>} />
      <Route path="/packaging/edit/:id" element={<ProtectedRoute><EditPackaging /></ProtectedRoute>} />

      <Route path="/drivers" element={<ProtectedRoute><DriverList /></ProtectedRoute>} />
      <Route path="/drivers/create" element={<ProtectedRoute><DriverCreate /></ProtectedRoute>} />
      <Route path="/drivers/edit/:id" element={<ProtectedRoute><DriverEdit /></ProtectedRoute>} />

      <Route path="/transporters" element={<ProtectedRoute><TransporterList /></ProtectedRoute>} />
      <Route path="/transporters/create" element={<ProtectedRoute><CreateTransporter /></ProtectedRoute>} />
      <Route path="/transporters/edit/:id" element={<ProtectedRoute><EditTransporter /></ProtectedRoute>} />

      <Route path="/vehicles" element={<ProtectedRoute><VehicleList /></ProtectedRoute>} />
      <Route path="/vehicles/create" element={<ProtectedRoute><CreateVehicle /></ProtectedRoute>} />
      <Route path="/vehicles/edit/:id" element={<ProtectedRoute><EditVehicle /></ProtectedRoute>} />

      <Route path="/product-weights" element={<ProtectedRoute><ProductWeights /></ProtectedRoute>} />

      <Route path="/addresses" element={<ProtectedRoute><AddressList /></ProtectedRoute>} />
      <Route path="/addresses/create" element={<ProtectedRoute><CreateAddress /></ProtectedRoute>} />
      <Route path="/addresses/edit/:id" element={<ProtectedRoute><EditAddress /></ProtectedRoute>} />

      <Route path="/supplier-emails" element={<ProtectedRoute><FornecedorEmailList /></ProtectedRoute>} />
      <Route path="/supplier-emails/create" element={<ProtectedRoute><CreateFornecedorEmail /></ProtectedRoute>} />
      <Route path="/supplier-emails/edit/:id" element={<ProtectedRoute><EditFornecedorEmail /></ProtectedRoute>} />

      <Route path="/etiqueta-primaria" element={<EtiquetaPrimaria />} />
      <Route path="/etiqueta-secundaria" element={<EtiquetaSecundaria />} />
      <Route path="/visualizar-xml" element={<ProtectedRoute><VisualizarXml /></ProtectedRoute>} />

      <Route path="*" element={<NotFound />} />
    </Routes>
  );
};
