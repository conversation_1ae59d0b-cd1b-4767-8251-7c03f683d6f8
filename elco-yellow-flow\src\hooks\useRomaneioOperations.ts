import { toast } from '@/components/ui/use-toast';
import { api } from '@/lib/api';
import { saveRomaneio, saveRomaneioDraft } from '@/services/romaneio-service';
import { RomaneioProps, VolumesProps, SecondaryPackaging } from './useMultiVolumeModal';

export const useRomaneioOperations = () => {
  
  const validarVolumesPorPedido = (orders: any[], productsByOrder: Record<string, any[]>, secondaryPackagings: SecondaryPackaging[]) => {
    for (const order of orders) {
      const totalVolumes = Number(order.volume) || 1;
      const orderCode = order.internalCode;

      const produtos = productsByOrder[orderCode] || [];

      const volumesMarcados = new Set();
      let hasProductsInAnyVolume = false;
      for (const produto of produtos) {
        const sec = secondaryPackagings.find(
          sp => sp.orderCode === orderCode && sp.productCode === produto.code
        );
        if (sec) {
          const num = Number(sec.volumeFormat?.split('/')[0]);
          if (num && !isNaN(num) && num >= 1 && num <= totalVolumes) {
            volumesMarcados.add(num);
            hasProductsInAnyVolume = true;
          } else {
            console.log(`Numerador inválido, fora da faixa ou não encontrado em secondaryPackaging para produto ${produto.code}.`, sec);
          }
        } else {
          console.log(`Nenhum secondaryPackaging encontrado para produto ${produto.code}.`);
        }
      }

      if (produtos.length > 0 && !hasProductsInAnyVolume) {
        console.log(`Falha na validação do pedido ${orderCode}: Nenhum produto possui um volume secundário válido marcado.`);
        return false;
      }

      for (let i = 1; i <= totalVolumes; i++) {
        if (!volumesMarcados.has(i)) {
          console.log(`Falha na validação do pedido ${orderCode}: Volume ${i} está faltando nos volumes marcados por produtos.`);
          if (produtos.length > 0) {
            return false;
          }
        }
      }
      if (produtos.length === 0) {
        console.log(`Pedido ${orderCode} não tem produtos, validação de volume por produto não aplicável.`);
      }
    }
    return true;
  };

  const handleSubmit = async (
    romaneio: RomaneioProps,
    volumes: VolumesProps[],
    combinedOrders: any[],
    productsByOrder: Record<string, any[]>,
    secondaryPackagings: SecondaryPackaging[],
    packagingSelections: Record<string, Record<string, string>>,
    productWeights: Record<string, Record<string, string>>,
    invalidSecondaryVolumes: string[],
    globalVinculo: 'matriz' | 'filial',
    orders: any[],
    setErrorMessage: (message: string | null) => void,
    setIsSubmitting: (loading: boolean) => void,
    onSuccess?: () => void,
    onClose?: () => void
  ) => {
    setErrorMessage(null);
    
    if (invalidSecondaryVolumes.length > 0) {
      setErrorMessage('Existem volumes informados incorretamente. Corrija antes de continuar.');
      return;
    }
    
    if (!romaneio.address || !romaneio.carrierId || !romaneio.driverId || !romaneio.vehicleId || !romaneio.mainPackagingId) {
      setErrorMessage('Preencha todos os campos obrigatórios do romaneio.');
      return;
    }

    const hasEmptyDimensions = volumes.some(
      (volume) => !volume.length || !volume.width || !volume.height
    );

    if (hasEmptyDimensions) {
      setErrorMessage('Preencha todas as dimensões para todos os pedidos.');
      return;
    }

    // Validar se todos os produtos têm embalagens selecionadas
    const missingPackagingDetails = [];
    for (const order of combinedOrders) {
      const products = productsByOrder[order.internalCode] || [];
      for (const product of products) {
        const selected = packagingSelections[order.internalCode]?.[product.code];
        if (!selected) {
          missingPackagingDetails.push({
            orderCode: order.internalCode,
            productCode: product.code,
            productName: product.name
          });
        }
      }
    }

    if (missingPackagingDetails.length > 0) {
      const missingList = missingPackagingDetails
        .map(item => `${item.productName} (${item.orderCode})`)
        .join(', ');
      setErrorMessage(`Selecione uma embalagem para os seguintes produtos: ${missingList}`);
      return;
    }

    if (!validarVolumesPorPedido(combinedOrders, productsByOrder, secondaryPackagings)) {
      setErrorMessage('Cada volume (ex: 1/2, 2/2, ...) deve ter pelo menos um produto associado em cada pedido.');
      return;
    }

    setIsSubmitting(true);
    try {
      const completedSecondaryPackagings = combinedOrders.flatMap(order => {
        const orderCode = order.internalCode;
        const volumeEntry = volumes.find(v => v.separationOrderId === order.id);
        const orderNumberOfUnitVolumes = Number(volumeEntry?.numberPackaging) || 1;

        const products = productsByOrder[orderCode] || [];

        return products.map(product => {
          const existing = secondaryPackagings.find(
            sp => sp.orderCode === orderCode && sp.productCode === product.code
          );

          const volumeParts = existing?.volumeFormat?.split('/');
          const numerator = volumeParts?.[0] || '1';

          const packagingId = existing
            ? existing.packagingId
            : packagingSelections[orderCode]?.[product.code] ?? '';
          const productWeight = productWeights[orderCode]?.[product.code] || '0';

          return {
            orderCode,
            productCode: product.code,
            productName: product.name,
            productQuantity: product.quantity,
            packagingId,
            secondaryVolume: numerator,
            totalSecondaryVolumes: orderNumberOfUnitVolumes.toString(),
            productWeight: Number(productWeight),
          };
        });
      });

      const externalCode = orders?.[0]?.externalCode;
      await saveRomaneio({
        ...romaneio,
        romaneioOrders: volumes.map(vol => ({
          ...vol,
          volume: vol.volume,
        })),
        productPackagings: completedSecondaryPackagings,
        isDraft: false,
        externalCode,
        linking: globalVinculo,
      });

      toast.success('Romaneio criado com sucesso!');
      if (onSuccess) onSuccess();
      if (onClose) onClose();
    } catch (error) {
      console.error(error);
      setErrorMessage('Falha ao criar o Romaneio!');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSaveDraft = async (
    romaneio: RomaneioProps,
    volumes: VolumesProps[],
    combinedOrders: any[],
    productsByOrder: Record<string, any[]>,
    secondaryPackagings: SecondaryPackaging[],
    packagingSelections: Record<string, Record<string, string>>,
    productWeights: Record<string, Record<string, string>>,
    orders: any[],
    isUnlinkedFromDraft: boolean,
    setErrorMessage: (message: string | null) => void,
    setIsSubmitting: (loading: boolean) => void,
    onSuccess?: () => void,
    onClose?: () => void
  ) => {
    if (isUnlinkedFromDraft) {
      toast.error('Não é possível salvar rascunho quando desvinculado. Desative o modo "Novo Romaneio" para salvar rascunho.');
      return;
    }

    setIsSubmitting(true);
    setErrorMessage(null);

    try {
      const completedSecondaryPackagings = combinedOrders.flatMap(order => {
        const orderCode = order.internalCode;
        const volumeEntry = volumes.find(v => v.separationOrderId === order.id);
        const orderNumberOfUnitVolumes = Number(volumeEntry?.numberPackaging) || 1;

        const products = productsByOrder[orderCode] || [];

        return products.map(product => {
          const existing = secondaryPackagings.find(
            sp => sp.orderCode === orderCode && sp.productCode === product.code
          );

          const volumeParts = existing?.volumeFormat?.split('/');
          const numerator = volumeParts?.[0] || '1';

          const packagingId = existing
            ? existing.packagingId
            : packagingSelections[orderCode]?.[product.code] ?? '';

          const productWeight = productWeights[orderCode]?.[product.code] || '0';

          return {
            orderCode,
            productCode: product.code,
            packagingId,
            secondaryVolume: numerator,
            totalSecondaryVolumes: orderNumberOfUnitVolumes.toString(),
            productWeight: Number(productWeight),
          };
        });
      });

      const externalCode = orders?.[0]?.externalCode;
      await saveRomaneioDraft({
        ...romaneio,
        romaneioOrders: volumes.map(vol => {
          const originalOrder = combinedOrders.find(o => o.id === vol.separationOrderId);
          return {
            ...vol,
            volume: Number(originalOrder?.volume) || 0,
          };
        }),
        productPackagings: completedSecondaryPackagings,
        isDraft: true,
        externalCode,
      });

      toast.success('Rascunho salvo com sucesso!');
      if (onSuccess) onSuccess();
      if (onClose) onClose();
    } catch (error) {
      console.error(error);
      setErrorMessage('Falha ao salvar o rascunho do Romaneio!');
    } finally {
      setIsSubmitting(false);
    }
  };

  const tryLoadDraft = async (
    orders: any[],
    globalVinculo: 'matriz' | 'filial',
    isUnlinkedFromDraft: boolean,
    setCombinedOrders: (orders: any[]) => void,
    setVolumes: (volumes: VolumesProps[]) => void,
    setRomaneio: (romaneio: RomaneioProps) => void,
    setSecondaryPackagings: (packagings: SecondaryPackaging[]) => void,
    setPackagingSelections: (selections: Record<string, Record<string, string>>) => void,
    setProductWeights: (weights: Record<string, Record<string, string>>) => void,
    setHasDraftAvailable: (available: boolean) => void,
    loadProducts: (order: any) => Promise<void>
  ) => {
    const externalCode = orders?.[0]?.externalCode;
    if (!externalCode) {
      setCombinedOrders(orders);
      const initialVolumes: VolumesProps[] = orders.map((order) => ({
        separationOrderId: order.id,
        orderId: order.internalCode,
        linking: globalVinculo,
        volume: Number(order.volume) || 0,
        length: 0,
        height: 0,
        width: 0,
        netWeight: 0,
        numberPackaging: 0,
      }));
      setVolumes(initialVolumes);
      return;
    }

    try {
      const response = await api.get(`/romaneios/rascunho/${externalCode}`);
      if (response.data?.data) {
        const draft = response.data.data;
        setHasDraftAvailable(true);

        const draftRomaneioOrders = draft.romaneioOrders || [];
        const draftProducts = draft.productPackagings || [];

        const allOrdersMap = new Map<string, any>();
        orders.forEach(order => allOrdersMap.set(order.internalCode, order));
        draftRomaneioOrders.forEach((draftOrder: any) => {
          const originalOrder = orders.find(o => o.id === draftOrder.separationOrderId);
          if (originalOrder) {
            allOrdersMap.set(draftOrder.orderId, {
              ...originalOrder,
              ...draftOrder,
              internalCode: draftOrder.orderId || originalOrder.internalCode,
              id: draftOrder.separationOrderId || originalOrder.id,
            });
          } else {
            console.warn(`Draft order ${draftOrder.orderId} not found in initial orders prop.`);
            allOrdersMap.set(draftOrder.orderId, {
              ...draftOrder,
              internalCode: draftOrder.orderId,
              id: draftOrder.separationOrderId,
              volume: Number(draftOrder.totalSecondaryVolumes) || 1,
            });
          }
        });

        const mergedOrders = Array.from(allOrdersMap.values());
        setCombinedOrders(mergedOrders);

        const initialVolumes = mergedOrders.map((order) => {
          const draftVolume = draftRomaneioOrders.find((dv: any) => dv.separationOrderId === order.id);
          return {
            separationOrderId: order.id,
            orderId: order.internalCode,
            linking: globalVinculo,
            volume: Number(draftVolume?.volume) || Number(order.volume) || 0,
            length: Number(draftVolume?.length) || 0,
            width: Number(draftVolume?.width) || 0,
            height: Number(draftVolume?.height) || 0,
            netWeight: Number(draftVolume?.netWeight) || 0,
            numberPackaging: Number(order.volume) || Number(draftVolume?.totalSecondaryVolumes) || 1,
          };
        });
        setVolumes(initialVolumes);

        const { romaneioOrders: _, productPackagings: __, ...restOfDraft } = draft;
        setRomaneio(restOfDraft);

        setSecondaryPackagings(
          draftProducts.map((p: any) => ({
            orderCode: p.orderCode,
            productCode: p.productCode,
            packagingId: p.packagingId,
            volumeFormat: `${p.secondaryVolume}/${p.totalSecondaryVolumes}`,
          }))
        );

        const newSelections: Record<string, Record<string, string>> = {};
        draftProducts.forEach((p: any) => {
          if (!newSelections[p.orderCode]) newSelections[p.orderCode] = {};
          newSelections[p.orderCode][p.productCode] = p.packagingId;
        });
        setPackagingSelections(newSelections);

        const draftProductWeights: Record<string, Record<string, string>> = {};
        draftProducts.forEach((p: any) => {
          if (!draftProductWeights[p.orderCode]) draftProductWeights[p.orderCode] = {};
          draftProductWeights[p.orderCode][p.productCode] = String(p.productWeight || '0');
        });
        setProductWeights(draftProductWeights);

        for (const order of mergedOrders) {
          await loadProducts(order);
        }

      } else {
        setHasDraftAvailable(false);
        setCombinedOrders(orders);
        const initialVolumes: VolumesProps[] = orders.map((order) => ({
          separationOrderId: order.id,
          orderId: order.internalCode,
          linking: globalVinculo,
          volume: 0,
          length: 0,
          height: 0,
          width: 0,
          netWeight: 0,
          numberPackaging: Number(order.volume) || 1,
        }));
        setVolumes(initialVolumes);
      }
    } catch (err) {
      console.warn('Nenhum rascunho encontrado ou erro ao recuperar', err);
      setHasDraftAvailable(false);
      setCombinedOrders(orders);
      const initialVolumes: VolumesProps[] = orders.map((order) => ({
        separationOrderId: order.id,
        orderId: order.internalCode,
        linking: globalVinculo,
        volume: 0,
        length: 0,
        height: 0,
        width: 0,
        netWeight: 0,
        numberPackaging: Number(order.volume) || 1,
      }));
      setVolumes(initialVolumes);
    }
  };

  return {
    handleSubmit,
    handleSaveDraft,
    tryLoadDraft,
    validarVolumesPorPedido,
  };
}; 