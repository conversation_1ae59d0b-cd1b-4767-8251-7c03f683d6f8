import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import MainLayout from '@/layouts/MainLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from '@/components/ui/select';
import { Card, CardHeader, CardTitle, CardContent, CardDescription } from '@/components/ui/card';
import { toast } from '@/components/ui/use-toast';
import { api } from '@/lib/api';
import { PackagePlus } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useEffect } from 'react';

const embalagemTipos = [
  'Sol<PERSON>',
  'Ber<PERSON>o',
  '<PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>',
  'Engradado',
  'Estr. Met',
  'Outros',
] as const;

const formSchema = z.object({
  name: z.string().min(2, 'Nome obrigatório'),
  weight: z.coerce.number().min(0.1, 'Peso mínimo é 0.1kg'),
  height: z.coerce.number().min(0.1, 'Altura mínima é 0.1cm'),
  length: z.coerce.number().min(0.1, 'Comprimento mínimo é 0.1cm'),
  width: z.coerce.number().min(0.1, 'Largura mínima é 0.1cm'),
  volume: z.coerce.number().min(0.0001, 'Volume mínimo é 0.01cm³'),
  type: z.enum(embalagemTipos, { errorMap: () => ({ message: 'Selecione um tipo de embalagem' }) }),
});

type FormValues = z.infer<typeof formSchema>;

const CreatePackaging = () => {
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      weight: 0,
      height: 0,
      length: 0,
      width: 0,
      volume: 0,
      type: embalagemTipos[0],
    },
  });
  const navigate = useNavigate();

  // Calcula o volume automaticamente quando as dimensões mudam
  useEffect(() => {
    const length = form.watch('length');
    const width = form.watch('width');
    const height = form.watch('height');
    
    if (length > 0 && width > 0 && height > 0) {
      const calculatedVolume = (length * width * height) / 10000000;
      form.setValue('volume', parseFloat(calculatedVolume.toFixed(6)));
    }
  }, [form.watch('length'), form.watch('width'), form.watch('height'), form]);

  const onSubmit = async (data: FormValues) => {
    try {
      await api.post('/packaging/create', data);
      toast.success('Embalagem criada com sucesso!');
      form.reset();
      navigate('/packaging');
    } catch (error) {
      toast.error('Erro ao criar embalagem');
    }
  };

  return (
    <MainLayout title="Nova Embalagem">
      <div className="max-w-3xl mx-auto">
        <Card>
          <CardHeader>
            <div className="flex items-center gap-2">
              <PackagePlus className="h-6 w-6 text-primary" />
              <CardTitle>Cadastrar Embalagem</CardTitle>
            </div>
            <CardDescription>Preencha os dados da embalagem</CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nome</FormLabel>
                      <FormControl>
                        <Input placeholder="Digite o nome da embalagem" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tipo de Embalagem</FormLabel>
                      <FormControl>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione o tipo" />
                          </SelectTrigger>
                          <SelectContent>
                            {embalagemTipos.map((tipo) => (
                              <SelectItem key={tipo} value={tipo}>
                                {tipo}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="weight"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Peso (kg)</FormLabel>
                      <FormControl>
                        <Input type="number" step="0.01" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="height"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Altura (cm)</FormLabel>
                      <FormControl>
                        <Input type="number" step="0.01" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="length"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Comprimento (cm)</FormLabel>
                      <FormControl>
                        <Input type="number" step="0.01" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="width"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Largura (cm)</FormLabel>
                      <FormControl>
                        <Input type="number" step="0.01" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="volume"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Volume (m³) - Calculado automaticamente</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          step="0.000001" 
                          readOnly 
                          className="bg-muted" 
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Button type="submit" className="w-full">
                  <PackagePlus className="mr-2 h-4 w-4" />
                  Criar Embalagem
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};

export default CreatePackaging;
