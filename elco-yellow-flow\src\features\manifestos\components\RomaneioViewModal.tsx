import React, { useEffect, useMemo, useState } from 'react';
import { X, Download, AlertTriangle, Truck, Package, FileSpreadsheet, Search, User, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from '@/components/ui/table';
import { toast } from '@/hooks/use-toast';
import { format } from 'date-fns';
import { Input } from '@/components/ui/input';
import SmartPagination from '@/components/ui/SmartPagination';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { exportarRomaneioExcel } from '@/lib/utils';
import { useIsMobile } from '@/hooks/use-mobile';
import { useNavigate } from 'react-router-dom';
import { api } from '@/lib/api';

interface RomaneioViewModalProps {
  isOpen: boolean;
  onClose: () => void;
  romaneio: any;
}

// Função utilitária para formatar data de verificação
const formatVerificationDate = (verifiedDate: string, verifiedTime: string): string => {
  try {
    console.log('Formatando data:', { verifiedDate, verifiedTime });
    
    // Se temos data e hora separadas
    if (verifiedDate && verifiedTime) {
      const dateString = `${verifiedDate} ${verifiedTime}`;
      console.log('String combinada:', dateString);
      
      const date = new Date(dateString);
      console.log('Data criada:', date);
      
      if (!isNaN(date.getTime())) {
        return format(date, 'dd/MM/yyyy HH:mm');
      }
    }
    
    // Se temos apenas a data
    if (verifiedDate) {
      const date = new Date(verifiedDate);
      if (!isNaN(date.getTime())) {
        return format(date, 'dd/MM/yyyy');
      }
    }
    
    return 'Data inválida';
  } catch (error) {
    console.error('Erro ao formatar data:', error);
    return 'Data inválida';
  }
};

const RomaneioViewModal: React.FC<RomaneioViewModalProps> = ({
  isOpen,
  onClose,
  romaneio,
}) => {
  console.log("🚀 ~ romaneio:", romaneio)
  console.log("🚀 ~ produtos com verificação:", romaneio.products?.filter(p => p.verified)?.map(p => ({
    id: p.id,
    verified: p.verified,
    verifiedBy: p.verifiedBy,
    verifiedDate: p.verifiedDate,
    verifiedTime: p.verifiedTime
  })))
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredProducts, setFilteredProducts] = useState<any[]>([]);
  const [pesoCalculado, setPesoCalculado] = useState<{ pesoLiquido: number; pesoBruto: number } | null>(null);
  const isMobile = useIsMobile();
  const itemsPerPage = isMobile ? 3 : 5;
  const navigate = useNavigate();

  // Buscar peso calculado pelo backend
  useEffect(() => {
    const buscarPeso = async () => {
      try {
        const response = await api.get(`/romaneios/${romaneio.id}/peso`);
        setPesoCalculado(response.data);
      } catch (error) {
        console.error('Erro ao buscar peso do romaneio:', error);
        setPesoCalculado(null);
      }
    };

    if (romaneio?.id) {
      buscarPeso();
    }
  }, [romaneio?.id]);

  const totals = useMemo(() => {
    let totalLength = 0, totalWidth = 0, totalHeight = 0, totalVolume = 0;
    const groups: Record<number, Set<number>> = {};
    
    // Agrupar produtos por volume e calcular dimensões
    (romaneio.products || []).forEach((product: any) => {
      const volumeParts = product.volume?.split('/') || [];
      const num = parseInt(volumeParts[0], 10) || 1;
      if (!groups[num]) groups[num] = new Set();
      // Verificar se o produto tem embalagem antes de acessar o ID
      if (product.packaging?.id) {
        groups[num].add(product.packaging.id);
      }
    });
    
    Object.values(groups).forEach(packageSet => {
      let L = 0, W = 0, H = 0;
      packageSet.forEach(packageId => {
        const product = romaneio.products.find((p: any) => p.packaging?.id === packageId);
        if (product?.packaging) {
          L += product.packaging.length || 0;
          W += product.packaging.width || 0;
          H += product.packaging.height || 0;
        }
      });
      const vol = (L * W * H) / 1e7;
      totalLength += L;
      totalWidth += W;
      totalHeight += H;
      totalVolume += vol;
    });

    // Usar peso calculado pelo backend se disponível, senão calcular localmente como fallback
    let totalWeight = 0;
    
    if (pesoCalculado) {
      totalWeight = pesoCalculado.pesoBruto;
    } else {
      // Fallback: cálculo local (lógica anterior)
      const productsByOrder: Record<string, any[]> = {};
      (romaneio.products || []).forEach((product: any) => {
        const orderCode = product.orderCode || 'unknown';
        if (!productsByOrder[orderCode]) productsByOrder[orderCode] = [];
        productsByOrder[orderCode].push(product);
      });

      Object.values(productsByOrder).forEach((productsInOrder: any[]) => {
        let orderWeight = 0;
        
        // Filtrar apenas produtos com embalagem válida
        const productsWithPackaging = productsInOrder.filter(p => p.packaging?.id);
        const packagingIds = new Set(productsWithPackaging.map(p => p.packaging.id));
        
        packagingIds.forEach(packagingId => {
          const product = productsInOrder.find(p => p.packaging?.id === packagingId);
          if (product?.packaging?.weight) {
            orderWeight += product.packaging.weight;
          }
        });
        
        productsInOrder.forEach(product => {
          orderWeight += (product.weight ?? 0);
        });
        
        totalWeight += orderWeight;
      });

      if (romaneio.mainPackaging?.weight) {
        totalWeight += romaneio.mainPackaging.weight;
      }
    }

    return { totalLength, totalWidth, totalHeight, totalWeight, totalVolume };
  }, [romaneio.products, romaneio.mainPackaging, pesoCalculado]);

  const handleDownloadExcel = () => {
    exportarRomaneioExcel(romaneio);
    toast({ title: `Excel do romaneio #${romaneio.id} baixado com sucesso`, variant: 'success' });
  };

  const handleImprimirEtiquetaPrimaria = () => {
    localStorage.setItem('romaneioEtiquetaPrimaria', JSON.stringify(romaneio));
    window.open('/etiqueta-primaria', '_blank', 'noopener,noreferrer');
  };

  const handleImprimirEtiquetaSecundaria = (produto: any) => {
    console.log("🚀 ~ handleImprimirEtiquetaSecundaria ~ produto:", produto)
    console.log("🚀 ~ handleImprimirEtiquetaSecundaria ~ romaneio:", romaneio)
    const params = new URLSearchParams({
      destinatario: romaneio.romaneioOrders?.[0]?.separationOrders?.[0]?.externalCode || '',
      remetente: produto.depositor || '',
      transportadora: romaneio.carrierName || '',
      notaFiscal: romaneio.taxOrder || produto.taxNumber || '',
      volume: produto.volume || '',
      dimensoes: `${produto.packaging?.length || ''}x${produto.packaging?.width || ''}x${produto.packaging?.height || ''}`,
      peso: (produto.weight ?? 0) + (produto.packaging?.weight ?? 0) + '',
      romaneio: romaneio.id?.toString() || '',
      codigoBarras: romaneio.id?.toString() || '',
      dataEmissao: romaneio.dateIssue ? format(new Date(romaneio.dateIssue), 'dd/MM/yy') : '',
      romaneioData: JSON.stringify(romaneio)
    });
    
    window.open(
      `/etiqueta-secundaria?${params.toString()}`,
      '_blank',
      'noopener,noreferrer'
    );
  };

  // Filtering and pagination
  useEffect(() => {
    const filtered = (romaneio.products || []).filter((product: any) =>
      (product.name?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
      (product.depositor?.toLowerCase() || '').includes(searchTerm.toLowerCase())
    );
    setFilteredProducts(filtered);
    setCurrentPage(1);
  }, [searchTerm, romaneio.products]);

  const paginatedProducts = useMemo(() => {
    const start = (currentPage - 1) * itemsPerPage;
    return filteredProducts.slice(start, start + itemsPerPage);
  }, [filteredProducts, currentPage, itemsPerPage]);

  const totalPages = Math.ceil(filteredProducts.length / itemsPerPage);

  return (
    <div className={`fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-2 sm:p-4 ${!isOpen ? 'hidden' : ''}`}>
      <div className="bg-white rounded-xl shadow-xl w-full max-w-4xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="p-3 sm:p-6 flex items-center justify-between border-b border-gray-100">
          <div className="flex items-center gap-2 sm:gap-3">
            <div className="bg-primary/10 rounded-full p-2">
              <FileSpreadsheet className="h-5 w-5 sm:h-6 sm:w-6 text-primary" />
            </div>
            <div>
              <h2 className="text-lg sm:text-xl font-semibold flex items-center gap-2">
                Romaneio #{romaneio.id}
                {romaneio.verifiedObservations?.includes('Verificação automática') && (
                  <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">
                    Auto Verificado
                  </span>
                )}
              </h2>
              <p className="text-xs sm:text-sm text-gray-500">
                {romaneio.dateIssue ? format(romaneio.dateIssue, 'dd/MM/yyyy') : 'Data não disponível'}
              </p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 sm:h-9 sm:w-9 p-0 rounded-full hover:bg-gray-100"
            onClick={onClose}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-3 sm:p-6 flex-1 overflow-auto space-y-4 sm:space-y-6">
          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3 mb-2 sm:mb-4">
            <div className="flex items-center gap-2">
              <div className="bg-gray-100 text-gray-700 rounded-lg px-3 py-1.5 text-lg font-medium">
                {romaneio.id}
              </div>
            </div>

            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                className="gap-2"
                onClick={handleDownloadExcel}
              >
                <Download className="h-4 w-4" />
                <span className="sm:inline hidden">Baixar Excel</span>
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="gap-2"
                onClick={handleImprimirEtiquetaPrimaria}
              >
                <span className="sm:inline hidden">Imprimir Etiqueta Primária</span>
              </Button>
            </div>
          </div>

          {/* Observations Warning Card */}
          {romaneio.observations && (
            <Card className="border-[#F97316] border-2 bg-orange-50 shadow">
              <CardHeader className="pb-2">
                <CardTitle className="text-base font-medium flex items-center gap-2 text-[#F97316]">
                  <AlertTriangle className="h-5 w-5" />
                  Observações Importantes
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-[#F97316] whitespace-pre-line text-sm sm:text-base">{romaneio.observations}</p>
              </CardContent>
            </Card>
          )}

          {/* Verification Observations Card */}
          {romaneio.verifiedObservations && (
            <Card className="border-blue-200 border-2 bg-blue-50 shadow">
              <CardHeader className="pb-2">
                <CardTitle className="text-base font-medium flex items-center gap-2 text-blue-700">
                  <AlertTriangle className="h-5 w-5" />
                  Observações da Verificação
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-blue-700 whitespace-pre-line text-sm sm:text-base">{romaneio.verifiedObservations}</p>
              </CardContent>
            </Card>
          )}

          {/* Transporter Information */}
          <Card className="border border-gray-200">
            <CardHeader className="pb-0">
              <CardTitle className="text-base font-medium text-gray-700">Informações de Transporte</CardTitle>
            </CardHeader>
            <CardContent className="pt-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                <div className="space-y-1">
                  <p className="text-sm font-medium text-gray-500">Transportador</p>
                  <div className="flex items-center gap-2">
                    <Truck className="h-4 w-4 text-gray-400" />
                    <p className="font-medium">{romaneio.carrierName || 'Não informado'}</p>
                  </div>
                </div>

                <div className="space-y-1">
                  <p className="text-sm font-medium text-gray-500">Motorista</p>
                  <p className="font-medium">{romaneio.driverName || 'Não informado'}</p>
                </div>

                <div className="space-y-1">
                  <p className="text-sm font-medium text-gray-500">Placa</p>
                  <p className="font-medium">{romaneio.vehiclePlace || 'Não informada'}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Dimensions Card */}
          <Card className="border border-gray-200">
            <CardHeader className="pb-0">
              <CardTitle className="text-base font-medium text-gray-700">Dimensões Totais</CardTitle>
            </CardHeader>
            <CardContent className="pt-4">
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                <div className="space-y-1">
                  <p className="text-xs font-medium text-gray-500">Comprimento (cm)</p>
                  <p className="font-medium text-base sm:text-lg">{totals.totalLength || '0'}</p>
                </div>

                <div className="space-y-1">
                  <p className="text-xs font-medium text-gray-500">Largura (cm)</p>
                  <p className="font-medium text-base sm:text-lg">{totals.totalWidth || '0'}</p>
                </div>

                <div className="space-y-1">
                  <p className="text-xs font-medium text-gray-500">Altura (cm)</p>
                  <p className="font-medium text-base sm:text-lg">{totals.totalHeight || '0'}</p>
                </div>

                <div className="space-y-1">
                  <p className="text-xs font-medium text-gray-500">Peso Total (kg)</p>
                  <p className="font-medium text-base sm:text-lg">{totals.totalWeight ? totals.totalWeight.toFixed(2) : '0'}</p>
                </div>

                <div className="space-y-1">
                  <p className="text-xs font-medium text-gray-500">Volume Total (m³)</p>
                  <p className="font-medium text-base sm:text-lg">{(totals.totalVolume).toFixed(3) || '0'}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Products Table */}
          <Card className="border border-gray-200">
            <CardHeader className="pb-0 pt-4">
              <CardTitle className="text-base font-medium text-gray-700 flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3">
                <span>Itens do Romaneio</span>
                <div className="relative w-full sm:w-64">
                  <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="text"
                    placeholder="Buscar itens..."
                    className="pl-9 text-sm"
                    value={searchTerm}
                    onChange={e => setSearchTerm(e.target.value)}
                  />
                </div>
              </CardTitle>
              <div className="text-sm text-gray-500 pt-2">
                {filteredProducts.length} itens encontrados
              </div>
            </CardHeader>
            <CardContent className="pt-4">
              <div className="border rounded-lg overflow-hidden">
                {isMobile ? (
                  // Mobile card view for products
                  <div className="divide-y">
                    {paginatedProducts.length > 0 ? (
                      paginatedProducts.map((product: any) => (
                        <div key={product.id} className="p-3 space-y-3">
                          <div className="flex justify-between items-center">
                            <div className="font-medium">{product.idExternal || 'N/A'}</div>
                            {product.verified && romaneio.verifiedObservations ? (
                              <div className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                                Verificado
                                {product.verifiedBy === 'Sistema (Auto Verificação)' && (
                                  <span className="ml-1 text-xs text-green-600">(Auto)</span>
                                )}
                              </div>
                            ) : (
                              <div className="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs font-medium">
                                Aguardando verificação
                              </div>
                            )}
                          </div>

                          <div>
                            <p className="text-sm text-gray-900">{product.name || 'Nome não disponível'}</p>
                            <p className="text-xs text-gray-500">Depositante: {product.depositor || 'N/A'}</p>
                          </div>

                          {/* Informações de verificação no mobile */}
                          {product.verified && product.verifiedBy && product.verifiedDate && product.verifiedTime && romaneio.verifiedObservations && (
                            <div className="bg-green-50 p-2 rounded text-xs space-y-1">
                              <div className="flex items-center gap-1 text-green-700">
                                <User className="h-3 w-3" />
                                <span>Verificado por: {product.verifiedBy}</span>
                              </div>
                              <div className="flex items-center gap-1 text-green-700">
                                <Clock className="h-3 w-3" />
                                <span>
                                  Em: {formatVerificationDate(product.verifiedDate, product.verifiedTime)}
                                </span>
                              </div>
                            </div>
                          )}

                          <div className="grid grid-cols-3 gap-2 text-sm">
                            <div>
                              <p className="text-xs text-gray-500">Qtd</p>
                              <p>{product.quantity}</p>
                            </div>
                            <div>
                              <p className="text-xs text-gray-500">Volume</p>
                              <p>{product.volume}</p>
                            </div>
                            <div>
                              <p className="text-xs text-gray-500">Peso</p>
                              <p>{product.weight ?? 0} kg</p>
                            </div>
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            className="mt-2"
                            onClick={() => handleImprimirEtiquetaSecundaria(product)}
                          >
                            Imprimir Etiqueta Secundária
                          </Button>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-6 text-gray-500">
                        Nenhum produto encontrado
                      </div>
                    )}
                  </div>
                ) : (
                  // Desktop table view for products
                  <Table>
                    <TableHeader className="bg-gray-50">
                      <TableRow>
                        <TableHead>Código</TableHead>
                        <TableHead>Nome do Produto</TableHead>
                        <TableHead>Quantidade</TableHead>
                        <TableHead>Depositante</TableHead>
                        <TableHead>Volume</TableHead>
                        <TableHead>Peso (kg)</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Detalhes da Verificação</TableHead>
                        <TableHead>Etiqueta Secundária</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {paginatedProducts.length > 0 ? (
                        paginatedProducts.map((product: any) => (
                          <TableRow key={product.id}>
                            <TableCell className="font-medium">{product.idExternal || 'N/A'}</TableCell>
                            <TableCell>{product.name || 'Nome não disponível'}</TableCell>
                            <TableCell>{product.quantity || 0}</TableCell>
                            <TableCell>{product.depositor || 'N/A'}</TableCell>
                            <TableCell>{product.volume || 'N/A'}</TableCell>
                            <TableCell>{product.weight ?? 0}</TableCell>
                            <TableCell>
                              {product.verified ? (
                                <div className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium inline-flex items-center">
                                  Verificado
                                  {product.verifiedBy === 'Sistema (Auto Verificação)' && (
                                    <span className="ml-1 text-xs text-green-600">(Auto)</span>
                                  )}
                                </div>
                              ) : (
                                <div className="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs font-medium inline-flex items-center">
                                  Aguardando verificação
                                </div>
                              )}
                            </TableCell>
                            <TableCell className="min-w-[200px]">
                              {product.verified && product.verifiedBy && product.verifiedDate && product.verifiedTime ? (
                                <div className="text-xs space-y-1">
                                  <div className="flex items-center gap-1 text-green-700">
                                    <User className="h-3 w-3" />
                                    <span>{product.verifiedBy}</span>
                                  </div>
                                  <div className="flex items-center gap-1 text-green-700">
                                    <Clock className="h-3 w-3" />
                                    <span>
                                      {formatVerificationDate(product.verifiedDate, product.verifiedTime)}
                                    </span>
                                  </div>
                                </div>
                              ) : (
                                <span className="text-gray-400 text-xs">Aguardando verificação</span>
                              )}
                            </TableCell>
                            <TableCell>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleImprimirEtiquetaSecundaria(product)}
                              >
                                Imprimir Etiqueta Secundária
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={8} className="text-center py-6 text-gray-500">
                            Nenhum produto encontrado
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                )}
              </div>

              {/* Pagination */}
              {filteredProducts.length > itemsPerPage && (
                <div className="flex justify-center mt-4">
                  <SmartPagination
                    totalPages={totalPages}
                    currentPage={currentPage}
                    onPageChange={setCurrentPage}
                  />
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Footer */}
        <div className="p-3 sm:p-6 border-t border-gray-100">
          <div className="flex flex-col sm:flex-row sm:justify-end gap-2 sm:gap-3">
            <Button variant="outline" className="w-full sm:w-auto" onClick={onClose}>
              Fechar
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RomaneioViewModal;
