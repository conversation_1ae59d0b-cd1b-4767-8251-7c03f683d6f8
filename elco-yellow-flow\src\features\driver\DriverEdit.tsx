import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useNavigate, useParams } from 'react-router-dom';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { api } from '@/lib/api';
import MainLayout from '@/layouts/MainLayout';
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from '@/components/ui/card';
import {
    Form,
    FormField,
    FormItem,
    FormLabel,
    FormControl,
    FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Edit, ArrowLeft, Phone, BadgeCheck, User, CalendarIcon } from 'lucide-react';

import { format, parse } from 'date-fns';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { toast } from '@/components/ui/use-toast';

const driverSchema = z.object({
    name: z.string().min(2, 'Nome obrigatório'),
    licenseNumber: z.string().min(5, 'CNH obrigatória'),
    phone: z.string().min(10, 'Telefone obrigatório'),
    matriculation: z.string().min(2, 'Matrícula obrigatória').optional(),
    licenseExpirationDate: z.date({
        required_error: "Data de validade da CNH é obrigatória",
    }).optional(),
});

type DriverForm = z.infer<typeof driverSchema>;

const DriverEdit = () => {
    const { id } = useParams();
    const navigate = useNavigate();
    const [isLoading, setIsLoading] = useState(true);
    const [dateInputValue, setDateInputValue] = useState<string>("");

    const form = useForm<DriverForm>({
        resolver: zodResolver(driverSchema),
        defaultValues: {
            name: '',
            licenseNumber: '',
            phone: '',
            matriculation: '',
        },
    });

    // Função para aplicar a máscara de data DD/MM/AAAA
    const maskDate = (value: string) => {
        let v = value.replace(/\D/g, '').slice(0, 8);
        if (v.length >= 5) return `${v.slice(0, 2)}/${v.slice(2, 4)}/${v.slice(4)}`;
        if (v.length >= 3) return `${v.slice(0, 2)}/${v.slice(2)}`;
        if (v.length >= 1) return `${v}`;
        return '';
    };

    useEffect(() => {
        const fetchDriver = async () => {
            try {
                const { data } = await api.get(`/drivers/view/${id}`);
                const rawExpirationDate = data.licenseExpirationDate;
                console.log("🚀 ~ fetchDriver ~ data:", data)

                form.reset({
                    ...data,
                    licenseExpirationDate: rawExpirationDate ? new Date(rawExpirationDate) : undefined
                });

                const formDate = form.getValues('licenseExpirationDate');
                if (formDate) {
                    // Ao carregar, formatar a data do formulário com a máscara
                    setDateInputValue(format(formDate, "dd/MM/yyyy"));
                } else {
                    setDateInputValue("");
                }

            } catch (error) {
                toast.error('Erro ao carregar motorista');
                console.error("Erro ao carregar motorista para edição:", error);
                navigate('/drivers');
            } finally {
                setIsLoading(false);
            }
        };

        if (id) fetchDriver();
    }, [id, form, navigate, setDateInputValue]);

    const onSubmit = async (data: DriverForm) => {
        try {
            const dataToSend = {
                ...data,
                licenseExpirationDate: data.licenseExpirationDate
                    ? format(data.licenseExpirationDate, 'yyyy-MM-dd')
                    : undefined,
            };

            await api.put(`/drivers/update/${id}`, dataToSend);
            toast.success('Motorista atualizado com sucesso!');
            navigate('/drivers');
        } catch (error) {
            toast.error('Erro ao atualizar motorista');
            console.error(error);
        }
    };

    const maskPhone = (value: string) => {
        return value
            .replace(/\D/g, '')
            .replace(/^(\d{2})(\d)/, '($1) $2')
            .replace(/(\d{5})(\d{1,4})/, '$1-$2')
            .replace(/(-\d{4})\d+?$/, '$1');
    };

    const maskCnh = (value: string) => {
        return value.replace(/\D/g, '').slice(0, 11);
    };
    
    const handleDateInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        // Aplicar a máscara ao valor digitado antes de setar o estado local
        const maskedValue = maskDate(value);
        setDateInputValue(maskedValue);

        // Tentar fazer o parse apenas se o valor mascarado tiver o comprimento esperado
        if (maskedValue.length === 10) {
             try {
                const parsedDate = parse(maskedValue, 'dd/MM/yyyy', new Date());
                if (!isNaN(parsedDate.getTime())) {
                    form.setValue('licenseExpirationDate', parsedDate);
                } else {
                    // Se o parse falhar para uma data completa, setar o valor do form como undefined
                    form.setValue('licenseExpirationDate', undefined);
                }
            } catch (error) {
                 // Se ocorrer um erro durante o parse, setar o valor do form como undefined
                 form.setValue('licenseExpirationDate', undefined);
            }
        } else {
             // Se o valor mascarado não tem 10 caracteres, garantir que a data no form é undefined
             form.setValue('licenseExpirationDate', undefined);
        }
    };

    const handleBack = () => navigate('/drivers');

    return (
        <MainLayout title="Editar Motorista">
            <div className="max-w-3xl mx-auto">
                <Button variant="ghost" className="mb-4" onClick={handleBack}>
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Voltar para Lista
                </Button>

                <Card>
                    <CardHeader>
                        <div className="flex items-center gap-2">
                            <Edit className="h-6 w-6 text-primary" />
                            <CardTitle>Editar Motorista</CardTitle>
                        </div>
                        <CardDescription>Atualize os dados do motorista</CardDescription>
                    </CardHeader>
                    <CardContent>
                        {isLoading ? (
                            <div className="flex justify-center py-8">
                                <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary" />
                            </div>
                        ) : (
                            <Form {...form}>
                                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                                    <FormField
                                        control={form.control}
                                        name="name"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel className="flex items-center gap-2">
                                                    <User className="h-4 w-4" />
                                                    Nome Completo
                                                </FormLabel>
                                                <FormControl>
                                                    <Input placeholder="Digite o nome completo" {...field} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                    
                                    <FormField
                                        control={form.control}
                                        name="matriculation"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel className="flex items-center gap-2">
                                                    <BadgeCheck className="h-4 w-4" />
                                                    Matrícula
                                                </FormLabel>
                                                <FormControl>
                                                    <Input placeholder="Matrícula do motorista" {...field} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="licenseNumber"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel className="flex items-center gap-2">
                                                    <BadgeCheck className="h-4 w-4" />
                                                    CNH
                                                </FormLabel>
                                                <FormControl>
                                                    <Input
                                                        placeholder="Digite os 11 números da CNH"
                                                        value={field.value}
                                                        onChange={(e) => field.onChange(maskCnh(e.target.value))}
                                                        maxLength={11}
                                                    />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                    
                                    <FormField
                                        control={form.control}
                                        name="licenseExpirationDate"
                                        render={({ field }) => (
                                            <FormItem className="flex flex-col">
                                                <FormLabel className="flex items-center gap-2">
                                                    <CalendarIcon className="h-4 w-4" />
                                                    Data de Validade da CNH
                                                </FormLabel>
                                                <div className="flex gap-2 items-center">
                                                    <Input
                                                        placeholder="DD/MM/AAAA"
                                                        value={dateInputValue}
                                                        onChange={handleDateInputChange}
                                                        className="w-full"
                                                    />
                                                    <Popover>
                                                        <PopoverTrigger asChild>
                                                            <Button
                                                                variant={"outline"}
                                                                size="icon"
                                                                type="button"
                                                            >
                                                                <CalendarIcon className="h-4 w-4" />
                                                            </Button>
                                                        </PopoverTrigger>
                                                        <PopoverContent className="w-auto p-0" align="end">
                                                            <Calendar
                                                                mode="single"
                                                                selected={field.value}
                                                                onSelect={(date) => {
                                                                    field.onChange(date);
                                                                    if (date) {
                                                                        setDateInputValue(format(date, "dd/MM/yyyy"));
                                                                    }
                                                                }}
                                                                disabled={(date) => date < new Date()}
                                                                initialFocus
                                                                fromYear={new Date().getFullYear()}
                                                                toYear={new Date().getFullYear() + 10}
                                                                captionLayout="dropdown-buttons"
                                                                className="p-3 pointer-events-auto"
                                                            />
                                                        </PopoverContent>
                                                    </Popover>
                                                </div>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="phone"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel className="flex items-center gap-2">
                                                    <Phone className="h-4 w-4" />
                                                    Telefone
                                                </FormLabel>
                                                <FormControl>
                                                    <Input
                                                        placeholder="(00) 00000-0000"
                                                        value={maskPhone(field.value)}
                                                        onChange={(e) => {
                                                            field.onChange(maskPhone(e.target.value));
                                                        }}
                                                    />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <div className="pt-4 flex justify-end space-x-4">
                                        <Button type="button" variant="outline" onClick={handleBack}>
                                            Cancelar
                                        </Button>
                                        <Button type="submit">
                                            <Edit className="mr-2 h-4 w-4" />
                                            Salvar Alterações
                                        </Button>
                                    </div>
                                </form>
                            </Form>
                        )}
                    </CardContent>
                </Card>
            </div>
        </MainLayout>
    );
};

export default DriverEdit;
