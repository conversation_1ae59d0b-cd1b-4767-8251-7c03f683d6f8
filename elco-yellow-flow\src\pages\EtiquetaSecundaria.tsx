import React from 'react';
import logo from '@/images/logo.png';
import { useLocation } from 'react-router-dom';
import { compressToEncodedURIComponent } from 'lz-string';
// @ts-ignore
const TINYURL_API_TOKEN = import.meta.env.VITE_TINYURL_API_TOKEN || import.meta.env.TINYURL_API_TOKEN;

function useQuery() {
  return new URLSearchParams(useLocation().search);
}

const EtiquetaSecundaria: React.FC = () => {
  const [conteudoQRCode, setConteudoQRCode] = React.useState('');
  const [legendaQRCode, setLegendaQRCode] = React.useState('');

  const handleImprimir = () => {
    window.print();
  };

  const query = useQuery();
  console.log("🚀 ~ query:", query)
  const destinatario = query.get('destinatario') || '';
  const remetente = query.get('remetente') || '';
  const transportadora = query.get('transportadora') || '';
  const notaFiscal = query.get('notaFiscal') || '';
  const volume = query.get('volume') || '';
  const dimensoes = query.get('dimensoes') || '';
  const peso = query.get('peso') || '';
  const romaneioId = query.get('romaneio') || '';
  const romaneioData = query.get('romaneioData') || '';
  const romaneio = JSON.parse(romaneioData);

  const hoje = new Date();
  const dataEmissao = hoje.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit', year: '2-digit' });

  // Código de barras: apenas os dados principais da etiqueta
  const codigoBarras = [
    romaneioId,
    notaFiscal,
    destinatario,
    remetente,
    transportadora,
    volume,
    dimensoes,
    peso
  ].join(';');

  React.useEffect(() => {
    const encurtarUrl = async (urlLonga: string) => {
      try {
        const response = await fetch('https://api.tinyurl.com/create', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${TINYURL_API_TOKEN}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            url: urlLonga,
            domain: 'tinyurl.com',
          }),
        });
        const data = await response.json();
        return data.data.tiny_url;
      } catch (e) {
        return urlLonga;
      }
    };

    if (romaneio) {
      const jsonCompact = JSON.stringify(romaneio);
      const jsonCompressed = compressToEncodedURIComponent(jsonCompact);
      const urlLonga = `/visualizar-xml?json=${jsonCompressed}`;
      encurtarUrl(window.location.origin + urlLonga).then((urlCurta) => {
        console.log("🚀 ~ encurtarUrl ~ urlCurta:", urlCurta)
        setConteudoQRCode(urlCurta);
        setLegendaQRCode('Escaneie para visualizar os dados do romaneio');
      });
    }
  }, [romaneioData]);

  const handleQrLoad = () => {
    window.print();
  };

  return (
    <div className="bg-white print:bg-white">
      <button
        onClick={handleImprimir}
        className="bg-black text-white px-6 py-3 rounded hover:bg-gray-800 print:hidden m-4"
      >
        Imprimir Etiqueta
      </button>

      <div
        id="printArea"
        className="bg-white border-[3px] border-black mx-auto"
        style={{
          width: '10cm',
          height: '15cm',
          boxSizing: 'border-box',
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        {/* Cabeçalho */}
        <div className="relative h-[26mm] border-b-[3px] border-black">
          <div className="absolute left-2 top-1/2 transform -translate-y-1/2">
            <img src={logo} alt="Elco" style={{ width: 50, height: 40, objectFit: 'contain' }} />
          </div>
          <div className="absolute inset-x-0 top-1/2 transform -translate-y-1/2 text-center leading-tight">
            <div className="font-bold text-[13px]">Etiqueta de Embarque</div>
            <div className="text-[10px]">Data Emissão: {dataEmissao}</div>
          </div>
          <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
            {conteudoQRCode && (
              <img
                src={`https://api.qrserver.com/v1/create-qr-code/?size=40x40&data=${encodeURIComponent(conteudoQRCode)}`}
                alt="QR"
                style={{ width: 40, height: 40, objectFit: 'contain' }}
                onLoad={handleQrLoad}
              />
            )}
          </div>
        </div>

        {/* Seções preenchíveis */}
        <div className="border-b-[3px] border-black">
          <div className="text-center font-bold text-xs py-1 bg-gray-100 border-black">Destinatário</div>
          <div className="border-t-[1px] border-black mb-1 h-8 bg-white flex items-center justify-center text-xs">{destinatario}</div>
        </div>
        <div className="border-b-[3px] border-black">
          <div className="text-center font-bold text-xs py-1 bg-gray-100 border-black">Remetente</div>
          <div className="border-t-[1px] border-black mb-1 h-8 bg-white flex items-center justify-center text-xs">{remetente}</div>
        </div>
        <div className="border-b-[3px] border-black">
          <div className="text-center font-bold text-xs py-1 bg-gray-100 border-black">Transportadora</div>
          <div className="border-t-[1px] border-black mb-1 h-8 bg-white flex items-center justify-center text-xs">{transportadora}</div>
        </div>

        {/* Nota fiscal + Volume */}
        <div className="flex border-b-[3px] border-black text-center">
          <div className="flex-1 border-r-[3px] border-black py-1 text-xs">
            <div>Nota Fiscal n.:</div>
            <div className="text-3xl font-bold">{notaFiscal}</div>
          </div>
          <div className="flex-1 py-1 text-xs">
            <div>Volume n.:</div>
            <div className="text-3xl font-bold">{volume}</div>
          </div>
        </div>

        {/* Dados do Volume */}
        <div className="border-t border-b-[3px] border-black text-center text-xs font-bold py-1 bg-gray-100">
          Dados do Volume
        </div>
        <div className="flex text-xs">
          <div className="flex-1 border-b border-black px-1 py-0.5">Dimensões / cm:</div>
          <div className="flex-1 border-b border-black px-1 py-0.5">{dimensoes}</div>
        </div>
        <div className="flex text-xs">
          <div className="flex-1 border-b border-black px-1 py-0.5">Peso / Kg:</div>
          <div className="flex-1 border-b border-black px-1 py-0.5">{peso}</div>
        </div>
        <div className="border-b border-black px-1 py-0.5 text-xs flex flex-col items-center justify-center">
          <span>Romaneio n.:</span>
          <span className="font-bold text-lg">{romaneioId}</span>
        </div>

        {/* Código de barras */}
        <div className="flex flex-col items-center justify-center mt-2 mb-2 gap-2">
          <img
            src={`https://barcode.tec-it.com/barcode.ashx?data=${encodeURIComponent(codigoBarras)}&code=Code128&translate-esc=false&dpi=300`}
            alt="Código de Barras"
            style={{
              display: 'block',
              margin: '0 auto',
              maxWidth: '85mm',
              width: '100%',
              height: '25mm',
              objectFit: 'contain',
              background: '#fff',
              padding: '1mm 0'
            }}
          />
        </div>
      </div>

      {/* Estilos de impressão */}
      <style>{`
        @media print {
          @page {
            size: 10cm 15cm;
            margin: 0;
          }

          html, body {
            width: 10cm !important;
            height: 15cm !important;
            margin: 0 !important;
            padding: 0 !important;
            overflow: hidden !important;
            -webkit-print-color-adjust: exact;
          }

          #printArea {
            position: fixed;
            top: 0;
            left: 0;
            width: 10cm !important;
            height: 15cm !important;
            page-break-after: avoid;
          }

          .print\\:hidden {
            display: none !important;
          }

          * {
            box-sizing: border-box;
          }
        }
      `}</style>
    </div>
  );
};

export default EtiquetaSecundaria;
