
import React from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import toast from 'react-hot-toast';
import { api } from '@/lib/api';

interface VincularModalProps {
  isOpen: boolean;
  onClose: () => void;
  pedido: any;
  onConfirm: (tipo: 'matriz' | 'filial') => void;
}

const VincularModal: React.FC<VincularModalProps> = ({
  isOpen,
  onClose,
  pedido,
  onConfirm,
}) => {
  if (!pedido) return null;

  const confirmarVinculo = async () => {
    try {
      await api.patch(`/separation-orders/vincular/${pedido.internalCode}`, {
        tipo: 'MATRIZ', // Agora sempre vincula à MATRIZ
      });
      toast.success(`Pedido #${pedido.internalCode} vinculado à matriz`);
      onConfirm('matriz');
    } catch (err) {
      toast.error('Erro ao vincular pedido');
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-full max-w-md md:max-w-lg bg-white border-0 shadow-lg rounded-xl p-4 md:p-6 mx-4">
        <DialogHeader>
          <DialogTitle className="text-lg sm:text-xl font-bold text-elco-800">
            Vincular Pedido #{pedido.internalCode}
          </DialogTitle>
          <DialogDescription className="text-muted-foreground">
            Confirme a vinculação do pedido à matriz.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 sm:gap-6 py-3 sm:py-4">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                Depositante
              </p>
              <p className="font-medium">{pedido.depositorName}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                Data do Pedido
              </p>
              <p className="font-medium">
                {new Date(pedido.orderDate).toLocaleDateString()}
              </p>
            </div>
          </div>
        </div>

        <DialogFooter className="flex flex-col sm:flex-row gap-3 mt-4">
          <Button variant="outline" onClick={onClose} className="w-full sm:w-auto">
            Cancelar
          </Button>
          <Button
            onClick={() => {
              confirmarVinculo();
              onClose();
            }}
            className="w-full sm:w-auto"
          >
            Vincular à Matriz
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default VincularModal;
