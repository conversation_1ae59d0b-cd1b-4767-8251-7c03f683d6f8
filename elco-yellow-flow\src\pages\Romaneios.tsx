import React, { useEffect, useState } from 'react';
import Layout from '@/layouts/Layout';
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Eye,
  FileSpreadsheet,
  Download,
  Search,
  Trash2,
  ClipboardCheck,
  Clock,
  AlertCircle,
  FileText,
  CheckCircle,
} from 'lucide-react';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from '@/hooks/use-toast';
import RomaneioViewModal from '@/features/manifestos/components/RomaneioViewModal';
import RomaneioVerificationModal from '@/features/manifestos/components/RomaneioVerificationModal';
import { getRomaneios, deleteRomaneio } from '@/services/romaneio-service';
import { format } from 'date-fns';
import { exportarRomaneioExcel } from '@/lib/utils';
import { useIsMobile } from '@/hooks/use-mobile';
import SmartPagination from '@/components/ui/SmartPagination';

const Romaneios = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRomaneio, setSelectedRomaneio] = useState<any>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isVerificationModalOpen, setIsVerificationModalOpen] = useState(false);
  const [romaneiosList, setRomaneiosList] = useState([]);
  const [filteredRomaneios, setFilteredRomaneios] = useState([]);
  const { pedidoId } = useParams();
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  const getStatusBadgeClasses = (statusId: number) => {
    switch (statusId) {
      case 0:
        return 'bg-gray-100 text-gray-800'; // Aguardando Notas
      case 1:
        return 'bg-yellow-100 text-yellow-800'; // Pendente
      case 2:
        return 'bg-blue-100 text-blue-800'; // Nota Emitida
      case 3:
        return 'bg-green-100 text-green-800'; // Entregue
      case 4:
        return 'bg-purple-100 text-purple-800'; // Verificado Automaticamente
      case 5:
        return 'bg-indigo-100 text-indigo-800'; // Verificado Manualmente
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusName = (statusId: number) => {
    switch (statusId) {
      case 0:
        return 'Aguardando Notas';
      case 1:
        return 'Pendente';
      case 2:
        return 'Nota Emitida';
      case 3:
        return 'Entregue';
      case 4:
        return 'Verificado Automaticamente';
      case 5:
        return 'Verificado Manualmente';
      default:
        return 'Status Desconhecido';
    }
  };

  const getStatusIcon = (statusId: number) => {
    switch (statusId) {
      case 0:
        return <Clock size={12} className="mr-1" />;
      case 1:
        return <FileText size={12} className="mr-1" />;
      case 2:
        return <CheckCircle size={12} className="mr-1" />;
      case 3:
        return <CheckCircle size={12} className="mr-1" />;
      case 4:
        return <CheckCircle size={12} className="mr-1" />;
      case 5:
        return <CheckCircle size={12} className="mr-1" />;
      default:
        return null;
    }
  };
  

  const fetchData = async () => {
    try {
      const response = await getRomaneios();
      if (response.data && Array.isArray(response.data)) {
        setRomaneiosList(response.data);
      } else {
        console.warn('Resposta inválida do servidor:', response);
        setRomaneiosList([]);
        toast({
          title: 'Aviso',
          description: 'Não foi possível carregar os romaneios. Tente novamente.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Erro ao buscar romaneios:', error);
      setRomaneiosList([]);
      toast({
        title: 'Erro',
        description: 'Erro ao carregar os romaneios. Verifique sua conexão.',
        variant: 'destructive',
      });
    }
  };
  
  useEffect(() => {
    fetchData();
  }, []);

  const romaneios = pedidoId
    ? romaneiosList.filter((rom) => rom.id === pedidoId)
    : romaneiosList;

  useEffect(() => {
    const filtered = romaneios.filter((romaneio) => {
      const searchLower = searchTerm.toLowerCase();
      return (
        romaneio.id.toString().toLowerCase().includes(searchLower) ||
        (romaneio.name && romaneio.name.toLowerCase().includes(searchLower))
      );
    });
    setFilteredRomaneios(filtered);
    setCurrentPage(1);
  }, [searchTerm, romaneios]);

  const handleViewRomaneio = async (romaneio: any) => {
    setSelectedRomaneio(romaneio);
    setIsViewModalOpen(true);
  };

  const handleVerifyRomaneio = async (romaneio: any) => {
    setSelectedRomaneio(romaneio);
    setIsVerificationModalOpen(true);
  };

  const handleDownload = (romaneio: any) => {
    exportarRomaneioExcel(romaneio);
    toast({
      title: 'Sucesso',
      description: `Excel do romaneio #${romaneio.id} baixado com sucesso`,
      variant: 'success',
    });
  };

  const handleDelete = async (romaneioId: string) => {
    if (window.confirm('Tem certeza que deseja deletar este romaneio?')) {
      try {
        await deleteRomaneio(romaneioId);
        toast({
          title: 'Sucesso',
          description: `Romaneio #${romaneioId} deletado com sucesso`,
          variant: 'success',
        });
        fetchData();
      } catch (error) {
        console.error('Erro ao deletar romaneio:', error);
        toast({
          title: 'Erro',
          description: 'Erro ao deletar o romaneio',
          variant: 'destructive',
        });
      }
    }
  };



  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const paginatedRomaneios = filteredRomaneios.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredRomaneios.length / itemsPerPage) || 1;

  const renderMobileCard = (romaneio: any) => (
    <div key={romaneio.id} className="bg-white p-4 rounded-lg shadow-sm mb-4 border">
      <div className="flex justify-between items-center mb-2">
        <div className="flex items-center gap-2">
          <FileSpreadsheet size={16} className="text-primary" />
          <span className="font-medium">#{romaneio.id}</span>
        </div>
        <span
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeClasses(romaneio.statusId)}`}
        >
          {getStatusIcon(romaneio.statusId)}
          {getStatusName(romaneio.statusId)}
        </span>
      </div>
      
      <div className="grid grid-cols-2 gap-2 text-sm mb-3">
        <div>
          <span className="text-gray-500">Data:</span> {romaneio.dateIssue && format(romaneio.dateIssue, 'dd/MM/yyyy')}
        </div>
        <div>
          <span className="text-gray-500">Itens:</span> {romaneio.products ? `${romaneio.products.length}` : '0'}
        </div>
      </div>
      
      <div className="flex justify-end gap-2">
        <Button
          variant="outline"
          size="sm"
          className="h-8 w-8 p-0 rounded-full"
          onClick={() => handleViewRomaneio(romaneio)}
          title="Visualizar"
        >
          <Eye className="h-4 w-4" />
        </Button>

        {romaneio.statusId === 2 && (
          <Button
            variant="outline"
            size="sm"
            className="h-8 w-8 p-0 rounded-full text-blue-600"
            onClick={() => handleVerifyRomaneio(romaneio)}
            title="Verificar Romaneio"
          >
            <ClipboardCheck className="h-4 w-4" />
          </Button>
        )}

        <Button
          variant="outline"
          size="sm"
          className="h-8 w-8 p-0 rounded-full text-green-600"
          onClick={() => handleDownload(romaneio)}
          title="Baixar Excel"
        >
          <Download className="h-4 w-4" />
        </Button>

        <Button
          variant="outline"
          size="sm"
          className="h-8 w-8 p-0 rounded-full text-red-600 hover:text-red-700 hover:bg-red-50"
          onClick={() => handleDelete(romaneio.id)}
          title="Deletar"
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );

  return (
    <Layout
      title={
        pedidoId ? `Romaneios do Pedido #${pedidoId}` : 'Todos os Romaneios'
      }
    >
      {pedidoId && (
        <Button
          variant="outline"
          onClick={() => navigate('/pedidos-expedicao')}
          className="mb-4 md:mb-6"
        >
          ← Voltar para Pedidos
        </Button>
      )}

      <Card className="mb-4 md:mb-6">
        <CardHeader className="pb-2 md:pb-3">
          <CardTitle className="text-lg md:text-xl">Status das Notas Fiscais</CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="list-disc pl-4 md:pl-6 space-y-1 md:space-y-2 text-sm md:text-base">
            <li>
              <strong>Aguardando Notas:</strong> Romaneio criado, aguardando geração da nota fiscal
            </li>
            <li>
              <strong>Nota Pendente:</strong> Nota fiscal em processo de geração
            </li>
            <li>
              <strong>Nota Emitida:</strong> Nota fiscal gerada e pronta para expedição
            </li>
            <li>
              <strong>Nota com Erro:</strong> Erro na geração da nota fiscal, requer atenção
            </li>
          </ul>
        </CardContent>
      </Card>

      <div className="mb-4 md:mb-8 rounded-xl bg-white border shadow-sm p-3 md:p-5">
        <div className="flex flex-col space-y-3 md:flex-row md:items-center md:justify-between md:space-y-0">
          <div className="relative w-full max-w-sm">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder="Buscar por número do romaneio..."
              className="pl-9"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>
      </div>

      {isMobile ? (
        <div className="space-y-4">
          {paginatedRomaneios.length > 0 ? (
            paginatedRomaneios.map((romaneio) => renderMobileCard(romaneio))
          ) : (
            <div className="text-center py-8 bg-white rounded-lg border">
              Nenhum romaneio encontrado
            </div>
          )}
          <div className="flex justify-center mt-4">
            <SmartPagination
              totalPages={totalPages}
              currentPage={currentPage}
              onPageChange={setCurrentPage}
            />
          </div>
        </div>
      ) : (
        <div className="rounded-xl overflow-hidden border shadow-sm">
          <div className="table-container">
            <Table>
              <TableHeader className="bg-gray-50">
                <TableRow>
                  <TableHead className="py-3 font-semibold text-gray-700">
                    ID
                  </TableHead>
                  <TableHead className="py-3 font-semibold text-gray-700">
                    Nome
                  </TableHead>
                  <TableHead className="py-3 font-semibold text-gray-700">
                    Pedido
                  </TableHead>
                  <TableHead className="py-3 font-semibold text-gray-700">
                    Data
                  </TableHead>
                  <TableHead className="py-3 font-semibold text-gray-700">
                    Status
                  </TableHead>
                  <TableHead className="py-3 font-semibold text-gray-700">
                    Produtos
                  </TableHead>
                  <TableHead className="py-3 text-right font-semibold text-gray-700">
                    Ações
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedRomaneios.length > 0 ? (
                  paginatedRomaneios.map((romaneio) => (
                    <TableRow
                      key={romaneio.id}
                      className="hover:bg-gray-50/80 transition-colors"
                    >
                      <TableCell className="font-medium">{romaneio.id}</TableCell>
                      <TableCell className="font-medium text-gray-900">
                        <div className="flex items-center gap-2">
                          <FileSpreadsheet size={16} className="text-primary" />
                          {romaneio.id}
                        </div>
                      </TableCell>
                      <TableCell>#{romaneio.id}</TableCell>
                      <TableCell>
                        {romaneio.dateIssue && format(romaneio.dateIssue, 'dd/MM/yyyy')}
                      </TableCell>
                      <TableCell>
                        <span
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeClasses(romaneio.statusId)}`}
                        >
                          {getStatusIcon(romaneio.statusId)}
                          {getStatusName(romaneio.statusId)}
                        </span>
                      </TableCell>
                      <TableCell>
                        {romaneio.products ? `${romaneio.products.length} itens` : '0 itens'}
                      </TableCell>
                      <TableCell className="text-right space-x-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0 rounded-full"
                          onClick={() => handleViewRomaneio(romaneio)}
                          title="Visualizar"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>

                        {romaneio.statusId === 2 && (
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 rounded-full text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                            onClick={() => handleVerifyRomaneio(romaneio)}
                            title="Verificar Romaneio"
                          >
                            <ClipboardCheck className="h-4 w-4" />
                          </Button>
                        )}

                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0 rounded-full text-green-600 hover:text-green-700 hover:bg-green-50"
                          onClick={() => handleDownload(romaneio)}
                          title="Baixar Excel"
                        >
                          <Download className="h-4 w-4" />
                        </Button>

                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0 rounded-full text-red-600 hover:text-red-700 hover:bg-red-50"
                          onClick={() => handleDelete(romaneio.id)}
                          title="Deletar"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} className="h-24 text-center">
                      Nenhum romaneio encontrado
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
            <div className="flex justify-center my-4">
              <SmartPagination
                totalPages={totalPages}
                currentPage={currentPage}
                onPageChange={setCurrentPage}
              />
            </div>
          </div>
        </div>
      )}

      {selectedRomaneio && (
        <>
          <RomaneioViewModal
            isOpen={isViewModalOpen}
            onClose={() => setIsViewModalOpen(false)}
            romaneio={selectedRomaneio}
          />
          <RomaneioVerificationModal
            isOpen={isVerificationModalOpen}
            onClose={() => setIsVerificationModalOpen(false)}
            romaneio={selectedRomaneio}
            onVerificationComplete={fetchData}
          />
        </>
      )}
    </Layout>
  );
};

export default Romaneios;
