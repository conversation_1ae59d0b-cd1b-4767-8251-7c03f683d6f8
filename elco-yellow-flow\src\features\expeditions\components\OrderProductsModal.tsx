import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from '@/components/ui/table';
import { api } from '@/lib/api';
import { Loader2 } from 'lucide-react'; 
import EngineeringLoader from '@/components/ui/EngineeringLoader';
import { useIsMobile } from '@/hooks/use-mobile';
import { Card } from '@/components/ui/card';

interface Product {
  code: string;
  name: string;
  category: string;
  withdrawalMethod: string;
  chargeType: string;
}

interface OrderProductsModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  data: any;
  type?: 'order' | 'invoice' | 'error' | 'link' | string;
}

const OrderProductsModal: React.FC<OrderProductsModalProps> = ({ isOpen, onClose, title, data, type }) => {
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const isMobile = useIsMobile();

  useEffect(() => {
    const fetchProducts = async () => {
      setIsLoading(true);
      try {
        const res = await api.get(`/separation-orders/external-products/${data.selectedOrder}`);
        setProducts(res.data);
      } catch (err) {
        console.error('Erro ao buscar produtos:', err);
      } finally {
        setIsLoading(false);
      }
    };

    if (isOpen && data?.selectedOrder) {
      fetchProducts();
    }
  }, [isOpen, data]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-full max-w-[95vw] sm:max-w-6xl max-h-[90vh] bg-white border-0 shadow-lg rounded-xl overflow-hidden flex flex-col p-3 md:p-6">
        <DialogHeader className="pb-2">
          <DialogTitle className="text-lg sm:text-xl font-bold text-elco-800">{title}</DialogTitle>
          <DialogDescription className="text-muted-foreground">
            {type === 'invoice' && 'Detalhes da nota fiscal'}
            {type === 'error' && 'Detalhes do erro na nota'}
            {type === 'link' && 'Detalhes da vinculação'}
            {type === 'order' || !type ? 'Produtos vinculados ao pedido' : ''}
          </DialogDescription>
        </DialogHeader>

        {isLoading ? (
          <div className="flex items-center justify-center flex-1">
            <EngineeringLoader />
          </div>
        ) : products.length > 0 ? (
          <div className="mt-2 border rounded-lg overflow-auto flex-1">
            {isMobile ? (
              // Mobile card view for products
              <div className="divide-y">
                {products.map((product, index) => (
                  <Card key={index} className="p-3 border-none rounded-none">
                    <div className="space-y-3">
                      <div>
                        <span className="text-sm font-medium text-gray-500">Código:</span>
                        <span className="block font-medium">{product.code}</span>
                      </div>
                      <div>
                        <span className="text-sm font-medium text-gray-500">Nome:</span>
                        <span className="block break-words">{product.name}</span>
                      </div>
                      <div className="grid grid-cols-1 gap-2">
                        <div>
                          <span className="text-sm font-medium text-gray-500">Categoria:</span>
                          <span className="block">{product.category}</span>
                        </div>
                        <div>
                          <span className="text-sm font-medium text-gray-500">Método de Retirada:</span>
                          <span className="block">{product.withdrawalMethod}</span>
                        </div>
                        <div>
                          <span className="text-sm font-medium text-gray-500">Tipo de Cobrança:</span>
                          <span className="block">{product.chargeType.replace(/_/g, " ")}</span>
                        </div>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            ) : (
              // Desktop table view
              <Table>
                <TableHeader className="sticky top-0 bg-white z-10">
                  <TableRow>
                    <TableHead>Código Interno</TableHead>
                    <TableHead>Nome</TableHead>
                    <TableHead>Categoria</TableHead>
                    <TableHead>Método de Retirada</TableHead>
                    <TableHead>Tipo de Cobrança</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {products.map((product, index) => (
                    <TableRow key={index}>
                      <TableCell>{product.code}</TableCell>
                      <TableCell>{product.name}</TableCell>
                      <TableCell>{product.category}</TableCell>
                      <TableCell>{product.withdrawalMethod}</TableCell>
                      <TableCell>{product.chargeType.replace(/_/g, " ")}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </div>
        ) : (
          <p className="text-sm text-muted-foreground mt-4 p-3">Nenhum produto encontrado para este pedido.</p>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default OrderProductsModal; 