
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 45 100% 50%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 45 100% 96%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 45 100% 50%;

    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  .elco-gradient {
    @apply bg-gradient-to-r from-elco-500 to-elco-600;
  }
  
  .sidebar-item {
    @apply flex items-center gap-3 rounded-lg px-3 py-2 text-sm transition-all hover:bg-accent;
  }
  
  .sidebar-item.active {
    @apply bg-elco-100 text-elco-800 font-medium;
  }
  
  .status-badge {
    @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium;
  }
  
  .status-pending {
    @apply bg-amber-100 text-amber-800;
  }
  
  .status-complete {
    @apply bg-green-100 text-green-800;
  }
  
  .status-error {
    @apply bg-red-100 text-red-800;
  }
}
