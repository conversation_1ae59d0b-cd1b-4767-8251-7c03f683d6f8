import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';

interface NotaPDFModalProps {
  isOpen: boolean;
  onClose: () => void;
  notaId: string;
}

const NotaPDFModal: React.FC<NotaPDFModalProps> = ({ isOpen, onClose, notaId }) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-4xl w-full h-[80vh] bg-white p-4">
        <DialogHeader>
          <DialogTitle className="text-lg font-bold">Nota Fiscal {notaId}</DialogTitle>
        </DialogHeader>
        <div className="w-full h-full pt-4">
          <iframe
            src={`/pdfs/${notaId}.pdf`} // ajuste conforme sua rota real
            className="w-full h-full border rounded"
            title={`Nota Fiscal ${notaId}`}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default NotaPDFModal;
