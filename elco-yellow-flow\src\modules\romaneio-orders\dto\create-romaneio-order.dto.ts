
// Define a interface simples sem decorators para evitar erros de build
export interface CreateRomaneioOrderDto {
  orderId: string;
  orderCode: string;
  sequence: number;
  clientName: string;
  depositanteName: string;
  totalLength: number;
  totalWidth: number;
  totalHeight: number;
  totalWeight: number;
  totalVolume: number;
}

// Exportar uma classe vazia para manter compatibilidade com imports existentes
export class CreateRomaneioOrderDto implements CreateRomaneioOrderDto {}
