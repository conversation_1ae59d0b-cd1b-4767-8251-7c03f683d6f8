import { useEffect, useRef, useState, useCallback, useMemo } from 'react';
import Layout from '@/layouts/Layout';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from '@/components/ui/table';
import StatusBadge from '@/components/ui/StatusBadge';
import {
  Eye,
  Search,
  CheckCircle,
  Clock,
  FileSpreadsheet,
  Loader2,
  Trash2,
} from 'lucide-react';
import ViewModal from '@/features/expeditions/components/ViewModal';
import MultiVolumeModal from '@/features/expeditions/components/MultiVolumeModal';
import StatCard from '@/components/ui/StatCard';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from '@/components/ui/use-toast';
import VincularModal from '@/features/expeditions/components/VincularModal';
import { useNavigate } from 'react-router-dom';
import { api } from '@/lib/api';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useAuth } from '@/hooks/useAuth';
import SmartPagination from '@/components/ui/SmartPagination';
import Combobox from '@/components/ui/Combobox';
import OrderProductsModal from '@/features/expeditions/components/OrderProductsModal';

const PedidosExpedicao = () => {
  const [orders, setOrders] = useState<any[]>([]);
  const [filteredOrders, setFilteredOrders] = useState<any[]>([]);
  const [viewModalOpen, setViewModalOpen] = useState(false);
  const [volumeModalOpen, setVolumeModalOpen] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<any>(null);
  const [observation, setObservation] = useState<string | null>(null);
  const [selectedOrders, setSelectedOrders] = useState<string[]>([]);
  const [vincularModalOpen, setVincularModalOpen] = useState(false);
  const [orderToLink, setOrderToLink] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(5);
  const [volumeInputs, setVolumeInputs] = useState<{ [key: string]: string }>({});
  const volumeTimeouts = useRef<Record<string, NodeJS.Timeout>>({});
  const [loadingVolumes, setLoadingVolumes] = useState<Record<string, boolean>>({});
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [depositorFilter, setDepositorFilter] = useState('');
  const [clientFilter, setClientFilter] = useState('');
  const navigate = useNavigate();
  const [orderProductsModalOpen, setOrderProductsModalOpen] = useState(false);

  const { user } = useAuth();
  const userRole = user?.role || '';

  const fetchOrders = useCallback(async () => {
    setIsLoading(true);
    try {
      const res = await api.get('/separation-orders');
      setOrders(res.data);
      setFilteredOrders(res.data);
      const initialVolumeInputs: { [key: string]: string } = {};
      res.data.forEach((order: any) => {
        initialVolumeInputs[order.internalCode] = order.volume || '';
      });
      setVolumeInputs(initialVolumeInputs);
    } catch (err) {
      toast.error('Erro ao carregar pedidos');
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchOrders();
  }, [fetchOrders]);

  useEffect(() => {
    let result = [...orders];

    if (searchQuery) {
      const lowerSearchQuery = searchQuery.toLowerCase();
      result = result.filter(order =>
        order.internalCode.toLowerCase().includes(lowerSearchQuery) ||
        order.externalCode?.toLowerCase().includes(lowerSearchQuery) ||
        order.depositorName?.toLowerCase().includes(lowerSearchQuery)
      );
    }

    if (statusFilter) {
      result = result.filter(order => order.status === statusFilter);
    }

    if (depositorFilter) {
      result = result.filter(order => order.depositorName === depositorFilter);
    }

    if (clientFilter) {
      result = result.filter(order => {
        const externalCode = order.externalCode?.trim();
        const filterCode = clientFilter.trim();
        if (!externalCode) return false;
        
        // Extrair código principal do pedido e do filtro para comparação
        const orderCodePart = externalCode.split(' - ')[0]?.trim().toUpperCase();
        const filterCodePart = filterCode.split(' - ')[0]?.trim().toUpperCase();
        
        return orderCodePart === filterCodePart;
      });
    }

    setFilteredOrders(result);
    setCurrentPage(1);
  }, [searchQuery, statusFilter, depositorFilter, clientFilter]);

  useEffect(() => {
    let result = [...orders];

    if (searchQuery) {
      const lowerSearchQuery = searchQuery.toLowerCase();
      result = result.filter(order =>
        order.internalCode.toLowerCase().includes(lowerSearchQuery) ||
        order.externalCode?.toLowerCase().includes(lowerSearchQuery) ||
        order.depositorName?.toLowerCase().includes(lowerSearchQuery)
      );
    }

    if (statusFilter) {
      result = result.filter(order => order.status === statusFilter);
    }

    if (depositorFilter) {
      result = result.filter(order => order.depositorName === depositorFilter);
    }

    if (clientFilter) {
      result = result.filter(order => {
        const externalCode = order.externalCode?.trim();
        const filterCode = clientFilter.trim();
        if (!externalCode) return false;
        
        // Extrair código principal do pedido e do filtro para comparação
        const orderCodePart = externalCode.split(' - ')[0]?.trim().toUpperCase();
        const filterCodePart = filterCode.split(' - ')[0]?.trim().toUpperCase();
        
        return orderCodePart === filterCodePart;
      });
    }

    setFilteredOrders(result);
  }, [orders]);

  const hasLinkedOrders = useMemo(() => filteredOrders.some(order => order.status === 'LINKED'), [filteredOrders]);

  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentOrders = filteredOrders.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredOrders.length / itemsPerPage);

  const handleSelectOrder = useCallback((id: string) => {
    const orderToSelect = orders.find(order => order.internalCode === id);
    const firstSelectedOrder = orders.find(order => selectedOrders.includes(order.internalCode));

    if (!orderToSelect) return;

    // Se não houver pedidos selecionados, permite selecionar
    if (selectedOrders.length === 0) {
      setSelectedOrders([id]);
      return;
    }

    // Verifica se o externalCode é o mesmo dos pedidos já selecionados (comparar apenas a parte do código)
    const getCodePart = (externalCode: string | undefined) => externalCode?.trim().split(' - ')[0]?.trim().toUpperCase();
    if (getCodePart(orderToSelect.externalCode) !== getCodePart(firstSelectedOrder?.externalCode)) {
      toast.error('Você só pode selecionar pedidos da mesma obra');
      return;
    }

    setSelectedOrders(prev => {
      const isSelected = prev.includes(id);
      if (isSelected) {
        return prev.filter((orderId) => orderId !== id);
      } else {
        if (prev.length < 100) {
          return [...prev, id];
        } else {
          toast.error('Você pode selecionar no máximo 100 pedidos.');
          return prev;
        }
      }
    });
  }, [orders, selectedOrders]);

  const handleSelectAll = useCallback(() => {
    const linkedOrders = filteredOrders.filter((p) => p.status === 'LINKED');
    
    if (linkedOrders.length === 0) return;

    const firstSelectedOrder = linkedOrders[0];
    const ordersWithSameExternalCode = linkedOrders.filter(
      order => {
        const getCodePart = (externalCode: string | undefined) => externalCode?.trim().split(' - ')[0]?.trim().toUpperCase();
        return getCodePart(order.externalCode) === getCodePart(firstSelectedOrder.externalCode);
      }
    );

    const currentlySelectedLinkedOrders = selectedOrders.filter(id => 
      ordersWithSameExternalCode.some(order => order.internalCode === id)
    );

    if (currentlySelectedLinkedOrders.length === ordersWithSameExternalCode.length) {
      setSelectedOrders(prev => prev.filter(id => 
        !ordersWithSameExternalCode.some(order => order.internalCode === id)
      ));
    } else {
      const ordersToSelect = ordersWithSameExternalCode
        .map(order => order.internalCode)
        .filter(id => !selectedOrders.includes(id))
        .slice(0, 100 - selectedOrders.length);

      if (ordersToSelect.length > 0) {
        setSelectedOrders(prev => [...prev, ...ordersToSelect]);
      }
    }
  }, [filteredOrders, selectedOrders]);

  const handleViewOrder = useCallback((order: any) => {
    setSelectedOrder(order);
    setOrderProductsModalOpen(true);
  }, []);

  const handleOpenVolumeModal = useCallback(() => {
    if (selectedOrders.length === 0) {
      toast.error('Selecione pelo menos um pedido para criar romaneio');
      return;
    }
    setVolumeModalOpen(true);
  }, [selectedOrders]);

  const handleVolumeChange = useCallback((orderId: string, rawValue: string) => {
    setVolumeInputs(prev => ({
      ...prev,
      [orderId]: rawValue,
    }));
  }, []);

  const saveVolume = useCallback(async (internalCode: string, orderId: number) => {
    const raw = volumeInputs[internalCode];
    if (raw === '' || raw === undefined) {
      setLoadingVolumes(prev => ({ ...prev, [internalCode]: true }));
      try {
        const res = await api.patch('/separation-orders/order-volume', {
          orderId,
          volume: null,
        });
        if (res.status === 200) {
          toast.success(`Volume removido para ${orderId}`);
          await fetchOrders();
        } else {
          toast.error('Falha ao remover volume');
        }
      } catch {
        toast.error('Erro ao remover volume');
      } finally {
        setLoadingVolumes(prev => ({ ...prev, [internalCode]: false }));
      }
      return;
    }
    const value = parseFloat(raw);
    if (isNaN(value) || value < 0) {
      toast.error('Informe um volume válido');
      return;
    }

    setLoadingVolumes(prev => ({ ...prev, [internalCode]: true }));
    try {
      const res = await api.patch('/separation-orders/order-volume', {
        orderId,
        volume: value,
      });
      if (res.status === 200) {
        toast.success(`Volume salvo para ${orderId}`);
        await fetchOrders();
      } else {
        toast.error('Falha ao salvar volume');
      }
    } catch {
      toast.error('Erro ao salvar volume');
    } finally {
      setLoadingVolumes(prev => ({ ...prev, [internalCode]: false }));
    }
  }, [volumeInputs, fetchOrders]);

  const confirmarVinculo = useCallback((tipo: 'matriz' | 'filial') => {
    toast.success(`Pedido #${orderToLink?.internalCode} vinculado à ${tipo}`);
  }, [orderToLink]);

  const completedCount = useMemo(() => orders.filter((p) => p.status === 'ROMANIO').length, [orders]);
  const linkedCount = useMemo(() => orders.filter((p) => p.status === 'LINKED').length, [orders]);
  const pendingCount = useMemo(() => orders.filter((p) => p.status === 'PENDING').length, [orders]);
  const errorCount = useMemo(() => orders.filter((p) => p.status === 'ERROR').length, [orders]);

  const totalOrdersCount = useMemo(() => orders.length, [orders]);

  const completedPercentage = useMemo(() => {
    if (totalOrdersCount === 0) return 0;
    return Math.round((completedCount / totalOrdersCount) * 100);
  }, [completedCount, totalOrdersCount]);

  const linkedPercentage = useMemo(() => {
    if (totalOrdersCount === 0) return 0;
    return Math.round((linkedCount / totalOrdersCount) * 100);
  }, [linkedCount, totalOrdersCount]);

  const pendingPercentage = useMemo(() => {
    if (totalOrdersCount === 0) return 0;
    return Math.round((pendingCount / totalOrdersCount) * 100);
  }, [pendingCount, totalOrdersCount]);

  const uniqueStatuses = useMemo(() => [...new Set(orders.map(order => order.status))], [orders]);
  const uniqueDepositors = useMemo(() => [...new Set(orders.map(order => order.depositorName))], [orders]);
  const uniqueClients = useMemo(() => {
    const clientsMap = new Map<string, string>();
    
    orders.forEach(order => {
      const externalCode = order.externalCode?.trim();
      if (!externalCode) return;
      
      // Extrair o código principal para agrupamento
      const codePart = externalCode.split(' - ')[0]?.trim().toUpperCase();
      
      // Se ainda não temos esse código, ou se o nome atual é mais completo, atualizar
      if (!clientsMap.has(codePart) || externalCode.length > (clientsMap.get(codePart)?.length || 0)) {
        clientsMap.set(codePart, externalCode);
      }
    });
    
    return Array.from(clientsMap.values()).sort();
  }, [orders]);

  const handleRomaneioSuccess = useCallback(() => {
    fetchOrders();
  }, [fetchOrders]);

  return (
    <Layout title="Pedidos de Expedição">
      <div className="mb-8 grid grid-cols-1 gap-6 md:grid-cols-3 ">
        <StatCard
          title="Aguardando Notas"
          value={completedCount}
          variant="green"
          progress={completedPercentage}
        />
        <StatCard
          title="Pedidos Vinculados"
          value={linkedCount}
          variant="blue"
          progress={linkedPercentage}
        />
        <StatCard
          title="Pedidos Pendentes"
          value={pendingCount}
          variant="amber"
          progress={pendingPercentage}
        />
      </div>

      <div className="mb-8 rounded-xl bg-white border shadow-sm p-5">
        <div className="flex flex-col md:flex-row justify-between gap-4">
          <div className="flex flex-wrap items-center gap-4 flex-grow">
            <div className="relative w-full md:w-auto">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                type="text"
                placeholder="Buscar pedidos..."
                className="pl-9 w-full"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Combobox
              options={uniqueStatuses}
              value={statusFilter}
              onChange={setStatusFilter}
              placeholder="Filtrar por Status..."
              label=""
              renderOption={(status) => {
                if (status === 'ROMANIO') return 'Aguardando Notas';
                if (status === 'LINKED') return 'Vinculado';
                if (status === 'PENDING') return 'Pendente';
                return status;
              }}
              className="w-full md:w-auto"
            />
            <Combobox
              options={uniqueDepositors}
              value={depositorFilter}
              onChange={setDepositorFilter}
              placeholder="Filtrar por Depositante..."
              label=""
              className="w-full md:w-auto"
            />
            <Combobox
              options={uniqueClients}
              value={clientFilter}
              onChange={setClientFilter}
              placeholder="Filtrar por Cliente..."
              label=""
              className="w-full md:w-auto"
            />
            {(searchQuery || statusFilter || depositorFilter || clientFilter) && (
              <button
                type="button"
                onClick={() => {
                  setSearchQuery('');
                  setStatusFilter('');
                  setDepositorFilter('');
                  setClientFilter('');
                }}
                className="flex items-center justify-center h-10 w-10 rounded-full bg-gray-100 hover:bg-red-100 text-gray-500 hover:text-red-600 transition shadow-sm border border-transparent"
                title="Limpar filtros"
              >
                <Trash2 className="h-5 w-5" />
              </button>
            )}
          </div>

          <div className="flex space-x-2 w-full md:w-auto md:ml-auto justify-end">
            {selectedOrders.length > 0 && (
              <Button
                onClick={handleOpenVolumeModal}
                className="gap-2 bg-green-600 hover:bg-green-700 text-white"
              >
                <FileSpreadsheet className="h-4 w-4" /> Criar Romaneio (
                {selectedOrders.length})
              </Button>
            )}
            {/* <Button variant="outline" className="gap-2">
              <Download className="h-4 w-4" /> Exportar
            </Button> */}
          </div>
        </div>
      </div>

      <div className="rounded-xl overflow-hidden border shadow-sm">
        <div className="flex justify-end p-2 bg-gray-50 border-b">
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-500">Itens por página:</span>
            <Select
              value={String(itemsPerPage)}
              onValueChange={(value) => {
                setItemsPerPage(Number(value));
                setCurrentPage(1);
              }}
            >
              <SelectTrigger className="w-[80px]">
                <SelectValue placeholder="5" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="5">5</SelectItem>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="20">20</SelectItem>
                <SelectItem value="50">50</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Table>
          <TableHeader className="bg-gray-50">
            <TableRow>
              <TableHead className="w-12">
                {hasLinkedOrders && (
                  <Checkbox
                    checked={
                      selectedOrders.filter(id => filteredOrders.some(order => order.internalCode === id && order.status === 'LINKED')).length ===
                      filteredOrders.filter((p) => p.status === 'LINKED').length &&
                      filteredOrders.filter((p) => p.status === 'LINKED').length > 0
                    }
                    onCheckedChange={handleSelectAll}
                  />
                )}
              </TableHead>
              <TableHead>Pedido</TableHead>
              <TableHead>Depositante</TableHead>
              <TableHead>Data</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Cliente</TableHead>
              <TableHead>Volume</TableHead>
              <TableHead>Observação</TableHead>
              <TableHead className="text-right">Ações</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={9} className="text-center py-8">
                  <div className="flex items-center justify-center">
                    <Loader2 className="h-6 w-6 mr-2 animate-spin" /> Carregando...
                  </div>
                </TableCell>
              </TableRow>
            ) : currentOrders.length === 0 ? (
              <TableRow>
                <TableCell colSpan={9} className="text-center py-8">
                  Nenhum pedido encontrado com os filtros aplicados.
                </TableCell>
              </TableRow>
            ) : (
              currentOrders.map((order) => (
                <TableRow key={order.internalCode}>
                  <TableCell>
                    {(order.status === 'LINKED') && (
                      <Checkbox
                        checked={selectedOrders.includes(order.internalCode)}
                        onCheckedChange={() =>
                          handleSelectOrder(order.internalCode)
                        }
                      />
                    )}
                  </TableCell>
                  <TableCell>{order.internalCode}</TableCell>
                  <TableCell>{order.depositorName}</TableCell>
                  <TableCell>
                    {new Date(order.orderDate).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    <StatusBadge status={order.status} />
                  </TableCell>
                  <TableCell>{order.externalCode}</TableCell>
                  <TableCell className="relative">
                    <div className="flex items-center space-x-2 max-w-[60px]">
                      <Input
                        type="text"
                        size={5}
                        value={volumeInputs[order.internalCode] || ''}
                        onChange={e =>
                          handleVolumeChange(order.internalCode, e.target.value)
                        }
                        onKeyDown={e => {
                          if (e.key === 'Enter') saveVolume(order.internalCode, order.id);
                        }}
                        onBlur={() => saveVolume(order.internalCode, order.id)}
                        placeholder="Vol."
                        className="w-full h-8 text-sm"
                        disabled={order.status === 'ROMANIO'}
                      />
                      {loadingVolumes[order.internalCode] && (
                        <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <span
                      onClick={() => setObservation(order.observation)}
                      className="cursor-pointer text-ellipsis whitespace-nowrap overflow-hidden block max-w-[200px] hover:underline"
                      title="Clique para ver mais"
                    >
                      {order.observation}
                    </span>
                  </TableCell>
                  <TableCell className="text-right space-x-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleViewOrder(order.internalCode)}
                      title="Visualizar"
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
        <div className="p-4 border-t">
          <SmartPagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={setCurrentPage}
          />
        </div>
      </div>

      {selectedOrder && (
        <OrderProductsModal
          isOpen={orderProductsModalOpen}
          onClose={() => setOrderProductsModalOpen(false)}
          title={`Pedido #${selectedOrder}`}
          data={{ selectedOrder }} type={'order'}        />
      )}

      <MultiVolumeModal
        isOpen={volumeModalOpen}
        onClose={() => {
          setVolumeModalOpen(false);
          setSelectedOrders([]);
        }}
        orderIds={selectedOrders}
        orders={orders.filter((p) => selectedOrders.includes(p.internalCode)).map(order => ({
          ...order,
          volume: volumeInputs[order.internalCode] || ''
        }))}
        onSuccess={handleRomaneioSuccess}
      />

      <VincularModal
        isOpen={vincularModalOpen}
        onClose={() => setVincularModalOpen(false)}
        pedido={orderToLink}
        onConfirm={confirmarVinculo}
      />

      <Dialog open={!!observation} onOpenChange={() => setObservation(null)}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle>Observação completa</DialogTitle>
          </DialogHeader>
          <p className="whitespace-pre-wrap">{observation}</p>
        </DialogContent>
      </Dialog>
    </Layout>
  );
};

export default PedidosExpedicao;
