import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useNavigate } from 'react-router-dom';
import MainLayout from '@/layouts/MainLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { toast } from '@/components/ui/use-toast';
import { api } from '@/lib/api';
import { Truck, BadgePlus, BadgeInfo, ParkingCircle, FileText, ArrowLeft, Upload } from 'lucide-react';

const formSchema = z.object({
  plate: z.string().min(6, 'Placa obrigatória'),
  model: z.string().min(2, 'Modelo obrigatório'),
  brand: z.string().min(2, 'Marca obrigatória'),
  vehicleDocument: z.instanceof(File).optional(),
});

type FormValues = z.infer<typeof formSchema>;

const CreateVehicle = () => {
  const navigate = useNavigate();
  const [documentFile, setDocumentFile] = useState<File | null>(null);
  const [documentPreview, setDocumentPreview] = useState<string | null>(null);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      plate: '',
      model: '',
      brand: '',
    },
  });

  const formatPlate = (value: string) => {
    // Convert letters to uppercase while preserving numbers and special characters
    return value.replace(/[a-zA-Z]/g, (letter) => letter.toUpperCase());
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        toast.error('Arquivo muito grande. Tamanho máximo: 5MB');
        return;
      }
      
      setDocumentFile(file);
      form.setValue('vehicleDocument', file);
      
      // Create preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setDocumentPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const onSubmit = async (data: FormValues) => {
    try {
      const formData = new FormData();
      formData.append('plate', data.plate);
      formData.append('model', data.model);
      formData.append('brand', data.brand);
      
      if (data.vehicleDocument) {
        formData.append('vehicleDocument', data.vehicleDocument);
      }
      
      await api.post('/vehicles/create', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      toast.success('Veículo cadastrado com sucesso!');
      form.reset();
      navigate('/vehicles');
    } catch (error) {
      toast.error('Erro ao cadastrar veículo');
    }
  };

  return (
    <MainLayout title="Cadastrar Veículo">
      <div className="max-w-3xl mx-auto">
        <Button variant="ghost" className="mb-4" onClick={() => navigate('/vehicles')}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Voltar
        </Button>
        <Card>
          <CardHeader>
            <div className="flex items-center gap-2">
              <Truck className="h-6 w-6 text-primary" />
              <CardTitle>Novo Veículo</CardTitle>
            </div>
            <CardDescription>Preencha os dados do veículo</CardDescription>
          </CardHeader>

          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">

                <FormField
                  control={form.control}
                  name="plate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <ParkingCircle className="h-4 w-4" />
                        Placa
                      </FormLabel>
                      <FormControl>
                        <Input 
                          placeholder="ABC-1234" 
                          value={field.value}
                          onChange={(e) => field.onChange(formatPlate(e.target.value))}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="model"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <BadgeInfo className="h-4 w-4" />
                        Modelo
                      </FormLabel>
                      <FormControl>
                        <Input placeholder="Ex: Sprinter 415" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="brand"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <BadgePlus className="h-4 w-4" />
                        Marca
                      </FormLabel>
                      <FormControl>
                        <Input placeholder="Ex: Mercedes-Benz" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="vehicleDocument"
                  render={() => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <FileText className="h-4 w-4" />
                        Documento do Veículo (CRLV)
                      </FormLabel>
                      <FormControl>
                        <div className="flex flex-col gap-4">
                          <Input
                            type="file"
                            accept="image/*,.pdf"
                            onChange={handleFileChange}
                            className="cursor-pointer"
                          />
                          {documentPreview && (
                            <div className="relative mt-2 max-w-sm">
                              {documentFile?.type.includes('image') ? (
                                <img 
                                  src={documentPreview} 
                                  alt="Preview do documento" 
                                  className="rounded-md border max-h-48 object-contain"
                                />
                              ) : (
                                <div className="p-4 border rounded-md flex items-center justify-center bg-gray-50">
                                  <p className="text-sm text-gray-500">
                                    {documentFile?.name} ({(documentFile?.size / 1024 / 1024).toFixed(2)}MB)
                                  </p>
                                </div>
                              )}
                              <Button
                                type="button"
                                variant="destructive"
                                size="sm"
                                className="absolute -top-2 -right-2"
                                onClick={() => {
                                  setDocumentFile(null);
                                  setDocumentPreview(null);
                                  form.setValue('vehicleDocument', undefined);
                                }}
                              >
                                &times;
                              </Button>
                            </div>
                          )}
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="pt-4 flex justify-end space-x-4">
                  <Button type="button" variant="outline" onClick={() => navigate('/vehicles')}>
                    Cancelar
                  </Button>
                  <Button type="submit" className="w-auto">
                    <Truck className="mr-2 h-4 w-4" />
                    Cadastrar Veículo
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};

export default CreateVehicle;
