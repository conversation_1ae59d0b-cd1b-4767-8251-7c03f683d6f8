# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Dependency directories
node_modules/

# Build output
dist/
dist-ssr/
build/
.next/
out/

# Environment files
.env
.env.*.local
.env.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea/
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS-specific
Thumbs.db
ehthumbs.db
Icon?
Desktop.ini

# Testing
coverage/
*.lcov
jest-html-reporters-*

# TypeScript
*.tsbuildinfo

# Logs output from playwright/test runners
test-results/

# Parcel/Vite cache
.cache/
.vite/

# Bun
bun.lockb

# Optional lock files
package-lock.json
yarn.lock
pnpm-lock.yaml

# Optional - Remove if needed
*.tgz
*.zip
