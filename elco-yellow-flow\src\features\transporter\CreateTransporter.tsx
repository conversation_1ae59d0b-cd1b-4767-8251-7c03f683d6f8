
import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import MainLayout from '@/layouts/MainLayout';
import {
    Card, CardHeader, CardTitle, CardDescription, CardContent,
} from '@/components/ui/card';
import {
    Form, FormField, FormItem, FormLabel, FormControl, FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Truck, Building, MapPin, QrCode } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { api } from '@/lib/api';
import { toast } from '@/components/ui/use-toast';

const formSchema = z.object({
    name: z.string().min(3, 'Nome obrigatório'),
    cnpj: z.string().regex(/^\d{2}\.\d{3}\.\d{3}\/\d{4}-\d{2}$/, 'CNPJ inválido'),
    phone: z.string().min(10, 'Telefone obrigatório'),
    department: z.string().min(2, 'Departamento obrigatório'),
    zipCode: z.string().regex(/^\d{5}-\d{3}$/, 'CEP inválido').optional(),
    city: z.string().min(2, 'Cidade obrigatória'),
    state: z.string().length(2, 'Estado inválido'),
    erpCode: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

const CreateTransporter = () => {
    const navigate = useNavigate();
    const [isLoadingCep, setIsLoadingCep] = useState(false);

    const form = useForm<FormValues>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            name: '',
            cnpj: '',
            phone: '',
            department: '',
            zipCode: '',
            city: '',
            state: '',
            erpCode: '',
        },
    });

    const onSubmit = async (data: FormValues) => {
        try {
            await api.post('/transporters/create', data);
            toast.success('Transportadora cadastrada com sucesso!');
            navigate('/transporters');
        } catch {
            toast.error('Erro ao cadastrar transportadora');
        }
    };

    const maskPhone = (value: string) => {
        return value
            .replace(/\D/g, '')
            .replace(/^(\d{2})(\d)/, '($1) $2')
            .replace(/(\d{5})(\d{1,4})/, '$1-$2')
            .replace(/(-\d{4})\d+?$/, '$1');
    };

    const maskCnpj = (value: string) => {
        return value
            .replace(/\D/g, '')
            .replace(/^(\d{2})(\d)/, '$1.$2')
            .replace(/^(\d{2})\.(\d{3})(\d)/, '$1.$2.$3')
            .replace(/\.(\d{3})(\d)/, '.$1/$2')
            .replace(/(\d{4})(\d)/, '$1-$2')
            .slice(0, 18);
    };

    const maskCep = (value: string) => {
        return value
            .replace(/\D/g, '')
            .replace(/^(\d{5})(\d)/, '$1-$2')
            .slice(0, 9);
    };

    const fetchAddressByCep = async (cep: string) => {
        const cleanCep = cep.replace(/\D/g, '');

        if (cleanCep.length !== 8) {
            toast.error('CEP inválido');
            return;
        }

        setIsLoadingCep(true);

        try {
            const response = await fetch(`https://viacep.com.br/ws/${cleanCep}/json/`);
            const data = await response.json();

            if (!data.erro) {
                form.setValue('city', data.localidade);
                form.setValue('state', data.uf);
                toast.success('Endereço encontrado!');
            } else {
                form.setValue('city', '');
                form.setValue('state', '');
                form.setError('zipCode', { message: 'CEP não encontrado' });
                toast.error('CEP não encontrado');
            }
        } catch {
            toast.error('Erro ao buscar endereço');
        } finally {
            setIsLoadingCep(false);
        }
    };

    return (
        <MainLayout title="Cadastrar Transportadora">
            <div className="max-w-3xl mx-auto">
                <Button variant="ghost" className="mb-4" onClick={() => navigate('/transporters')}>
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Voltar
                </Button>
                <Card>
                    <CardHeader>
                        <div className="flex items-center gap-2">
                            <Truck className="h-6 w-6 text-primary" />
                            <CardTitle>Nova Transportadora</CardTitle>
                        </div>
                        <CardDescription>Informe os dados da transportadora</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <Form {...form}>
                            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                                <FormField
                                    control={form.control}
                                    name="name"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Nome</FormLabel>
                                            <FormControl><Input {...field} /></FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="department"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="flex items-center gap-2">
                                                <Building className="h-4 w-4" />
                                                Departamento
                                            </FormLabel>
                                            <FormControl>
                                                <Input
                                                    placeholder="Ex: Logística"
                                                    {...field}
                                                />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="cnpj"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>CNPJ</FormLabel>
                                            <FormControl>
                                                <Input
                                                    placeholder="00.000.000/0000-00"
                                                    value={field.value}
                                                    onChange={(e) => field.onChange(maskCnpj(e.target.value))}
                                                />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="phone"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Telefone</FormLabel>
                                            <FormControl>
                                                <Input
                                                    placeholder="(00) 00000-0000"
                                                    value={maskPhone(field.value)}
                                                    onChange={(e) => {
                                                        field.onChange(maskPhone(e.target.value));
                                                    }}
                                                />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <FormField
                                        control={form.control}
                                        name="zipCode"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel className="flex items-center gap-2">
                                                    <MapPin className="h-4 w-4" />
                                                    CEP
                                                </FormLabel>
                                                <div className="flex space-x-2">
                                                    <FormControl>
                                                        <Input
                                                            placeholder="00000-000"
                                                            value={field.value}
                                                            onChange={(e) => {
                                                                const maskedValue = maskCep(e.target.value);
                                                                field.onChange(maskedValue);
                                                            }}
                                                            onBlur={() => {
                                                                if (field.value.length === 9) fetchAddressByCep(field.value);
                                                            }}
                                                        />
                                                    </FormControl>
                                                    <Button
                                                        type="button"
                                                        variant="outline"
                                                        disabled={isLoadingCep || field.value.length !== 9}
                                                        onClick={() => fetchAddressByCep(field.value)}
                                                    >
                                                        {isLoadingCep ? (
                                                            <div className="animate-spin h-4 w-4 border-2 border-primary rounded-full border-t-transparent" />
                                                        ) : 'Buscar'}
                                                    </Button>
                                                </div>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="erpCode"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel className="flex items-center gap-2">
                                                    <QrCode className="h-4 w-4" />
                                                    Código ERP Externo
                                                </FormLabel>
                                                <FormControl>
                                                    <Input
                                                        placeholder="Código de sistema externo"
                                                        {...field}
                                                    />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <FormField
                                        control={form.control}
                                        name="city"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Cidade</FormLabel>
                                                <FormControl>
                                                    <Input {...field} readOnly={isLoadingCep} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="state"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Estado</FormLabel>
                                                <FormControl>
                                                    <Input {...field} readOnly={isLoadingCep} maxLength={2} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                </div>

                                <div className="flex justify-end space-x-4">
                                    <Button type="submit">
                                        <Truck className="mr-2 h-4 w-4" />
                                        Cadastrar
                                    </Button>
                                </div>
                            </form>
                        </Form>
                    </CardContent>
                </Card>
            </div>
        </MainLayout>
    );
};

export default CreateTransporter;
