// Arquivo principal do store para estado global
// Aqui você pode exportar stores do Zustand, Redux ou Context API

// Exemplo de estrutura para Zustand:
// export { useAuthStore } from './auth';
// export { useUserStore } from './user';
// export { useNotificationStore } from './notification';

// Por enquanto, deixamos uma estrutura base
export type GlobalState = {
  // Definir tipos do estado global aqui
};

export const useGlobalStore = () => {
  // Implementar store global aqui
  return {};
}; 