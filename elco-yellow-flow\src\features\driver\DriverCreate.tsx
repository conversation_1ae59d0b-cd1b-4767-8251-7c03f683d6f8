
import React, { useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useNavigate } from 'react-router-dom';
import MainLayout from '@/layouts/MainLayout';
import {
    Card,
    CardHeader,
    CardTitle,
    CardDescription,
    CardContent,
} from '@/components/ui/card';
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { UserPlus, ArrowLeft, Phone, BadgeCheck, IdCard, CalendarIcon, Upload } from 'lucide-react';
import { api } from '@/lib/api';
import { toast } from '@/components/ui/use-toast';
import { format, parse } from 'date-fns';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { cn } from '@/lib/utils';

const formSchema = z.object({
    name: z.string().min(3, 'Nome obrigatório'),
    licenseNumber: z.string().min(5, 'CNH obrigatória'),
    phone: z.string().min(10, 'Telefone obrigatório'),
    matriculation: z.string().min(2, 'Matrícula obrigatória'),
    licenseExpirationDate: z.date({
        required_error: "Data de validade da CNH é obrigatória",
    }),
    licenseDocument: z.instanceof(File).optional(),
});

type FormValues = z.infer<typeof formSchema>;

const CreateDriver = () => {
    const navigate = useNavigate();
    const [licenseFile, setLicenseFile] = useState<File | null>(null);
    const [licensePreview, setLicensePreview] = useState<string | null>(null);
    const [dateInputValue, setDateInputValue] = useState<string>("");
    const fileInputRef = useRef<HTMLInputElement | null>(null);

    const form = useForm<FormValues>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            name: '',
            licenseNumber: '',
            phone: '',
            matriculation: '',
        },
    });

    const maskPhone = (value: string) => {
        return value
            .replace(/\D/g, '')
            .replace(/^(\d{2})(\d)/, '($1) $2')
            .replace(/(\d{5})(\d{1,4})/, '$1-$2')
            .replace(/(-\d{4})\d+?$/, '$1');
    };

    const maskCnh = (value: string) => {
        return value.replace(/\D/g, '').slice(0, 11);
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files.length > 0) {
            const file = e.target.files[0];
            if (file.size > 5 * 1024 * 1024) { // 5MB limit
                toast.error('Arquivo muito grande. Tamanho máximo: 5MB');
                return;
            }

            setLicenseFile(file);
            form.setValue('licenseDocument', file);

            // Create preview
            const reader = new FileReader();
            reader.onloadend = () => {
                setLicensePreview(reader.result as string);
            };
            reader.readAsDataURL(file);
        }
    };

    const maskDate = (value: string) => {
        let v = value.replace(/\D/g, '').slice(0, 8);
        if (v.length >= 5) return `${v.slice(0, 2)}/${v.slice(2, 4)}/${v.slice(4)}`;
        if (v.length >= 3) return `${v.slice(0, 2)}/${v.slice(2)}`;
        if (v.length >= 1) return `${v}`;
        return '';
    };

    const handleDateInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const maskedValue = maskDate(e.target.value);
        setDateInputValue(maskedValue);

        if (maskedValue.length === 10) {
            const parsed = parse(maskedValue, 'dd/MM/yyyy', new Date());
            if (!isNaN(parsed.getTime())) {
                form.setValue('licenseExpirationDate', parsed);
            }
        }
    };

    const onSubmit = async (data: FormValues) => {
        try {
            const formData = new FormData();
            formData.append('name', data.name);
            formData.append('licenseNumber', data.licenseNumber);
            formData.append('phone', data.phone);
            formData.append('matriculation', data.matriculation);
            formData.append('licenseExpirationDate', data.licenseExpirationDate.toISOString());

            if (data.licenseDocument) {
                formData.append('licenseDocument', data.licenseDocument);
            }

            await api.post('/drivers/create', formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });
            toast.success('Motorista cadastrado com sucesso!');
            navigate('/drivers');
        } catch (error) {
            toast.error('Erro ao cadastrar motorista');
        }
    };

    const handleBack = () => navigate('/drivers');

    return (
        <MainLayout title="Cadastrar Motorista">
            <div className="max-w-3xl mx-auto">
                <Button variant="ghost" className="mb-4" onClick={handleBack}>
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Voltar para Lista
                </Button>

                <Card>
                    <CardHeader>
                        <div className="flex items-center gap-2">
                            <UserPlus className="h-6 w-6 text-primary" />
                            <CardTitle>Novo Motorista</CardTitle>
                        </div>
                        <CardDescription>Preencha os dados do motorista</CardDescription>
                    </CardHeader>

                    <CardContent>
                        <Form {...form}>
                            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                                <FormField
                                    control={form.control}
                                    name="name"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="flex items-center gap-2">
                                                <UserPlus className="h-4 w-4" />
                                                Nome Completo
                                            </FormLabel>
                                            <FormControl>
                                                <Input placeholder="Ex: João da Silva" {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="matriculation"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="flex items-center gap-2">
                                                <BadgeCheck className="h-4 w-4" />
                                                Matrícula
                                            </FormLabel>
                                            <FormControl>
                                                <Input placeholder="Matrícula do motorista" {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="phone"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="flex items-center gap-2">
                                                <Phone className="h-4 w-4" />
                                                Telefone
                                            </FormLabel>
                                            <FormControl>
                                                <Input
                                                    placeholder="(00) 00000-0000"
                                                    value={maskPhone(field.value)}
                                                    onChange={(e) => {
                                                        field.onChange(maskPhone(e.target.value));
                                                    }}
                                                />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="licenseNumber"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="flex items-center gap-2">
                                                <IdCard className="h-4 w-4" />
                                                Número da CNH
                                            </FormLabel>
                                            <FormControl>
                                                <Input
                                                    placeholder="Somente números (11 dígitos)"
                                                    value={field.value}
                                                    onChange={(e) => field.onChange(maskCnh(e.target.value))}
                                                    maxLength={11}
                                                />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="licenseExpirationDate"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="flex items-center gap-2">
                                                <CalendarIcon className="h-4 w-4" />
                                                Data de Validade da CNH
                                            </FormLabel>
                                            <FormControl>
                                                <Input
                                                    placeholder="DD/MM/AAAA"
                                                    value={dateInputValue || (field.value ? format(field.value, "dd/MM/yyyy") : "")}
                                                    onChange={handleDateInputChange}
                                                />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="licenseDocument"
                                    render={() => (
                                        <FormItem>
                                            <FormLabel className="flex items-center gap-2">
                                                <Upload className="h-4 w-4" />
                                                Documento CNH
                                            </FormLabel>
                                            <FormControl>
                                                <div className="flex flex-col gap-4">
                                                    <Input
                                                        ref={fileInputRef}
                                                        type="file"
                                                        accept="image/*,.pdf"
                                                        onChange={handleFileChange}
                                                        className="cursor-pointer"
                                                    />
                                                    {licensePreview && (
                                                        <div className="relative mt-2 max-w-sm">
                                                            {licenseFile?.type.includes('image') ? (
                                                                <img
                                                                    src={licensePreview}
                                                                    alt="Preview da CNH"
                                                                    className="rounded-md border max-h-48 object-contain"
                                                                />
                                                            ) : (
                                                                <div className="p-4 border rounded-md flex items-center justify-center bg-gray-50">
                                                                    <p className="text-sm text-gray-500">
                                                                        {licenseFile?.name} ({(licenseFile?.size / 1024 / 1024).toFixed(2)}MB)
                                                                    </p>
                                                                </div>
                                                            )}
                                                            <Button
                                                                type="button"
                                                                variant="destructive"
                                                                size="sm"
                                                                className="absolute -top-2 -right-2"
                                                                onClick={() => {
                                                                    setLicenseFile(null);
                                                                    setLicensePreview(null);
                                                                    form.setValue('licenseDocument', undefined);
                                                                    if (fileInputRef.current) {
                                                                        fileInputRef.current.value = '';
                                                                    }
                                                                }}
                                                            >
                                                                &times;
                                                            </Button>
                                                        </div>
                                                    )}
                                                </div>
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <div className="pt-4 flex justify-end space-x-4">
                                    <Button type="button" variant="outline" onClick={handleBack}>
                                        Cancelar
                                    </Button>
                                    <Button type="submit">
                                        <UserPlus className="mr-2 h-4 w-4" />
                                        Cadastrar
                                    </Button>
                                </div>
                            </form>
                        </Form>
                    </CardContent>
                </Card>
            </div>
        </MainLayout>
    );
};

export default CreateDriver;
