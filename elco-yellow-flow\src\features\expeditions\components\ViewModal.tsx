import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface ViewModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  data: any;
  type: 'invoice' | 'order' | 'romaneio' | 'link';
}

const ViewModal: React.FC<ViewModalProps> = ({ isOpen, onClose, title, data, type }) => {
  console.log("🚀 ~ data:", data)
  if (!data) return null;

  const formatDate = (date: string) => {
    return format(new Date(date), 'dd/MM/yyyy HH:mm', { locale: ptBR });
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  const renderInvoiceContent = () => {
    return (
      <div className="space-y-6">
        {/* Informações da Nota */}
        <div className="grid grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
          <div>
            <h3 className="text-sm font-medium text-gray-500">Informações da Nota</h3>
            <dl className="mt-2 space-y-1">
              <div>
                <dt className="text-xs text-gray-500">Número da Nota</dt>
                <dd className="text-sm font-medium">{data.notInNumero}</dd>
              </div>
              <div>
                <dt className="text-xs text-gray-500">Data de Emissão</dt>
                <dd className="text-sm font-medium">{formatDate(data.notDtEmissao)}</dd>
              </div>
              <div>
                <dt className="text-xs text-gray-500">Chave de Acesso</dt>
                <dd className="text-sm font-medium">{data.notStChaveacesso}</dd>
              </div>
            </dl>
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-500">Informações do Destinatário</h3>
            <dl className="mt-2 space-y-1">
              <div>
                <dt className="text-xs text-gray-500">CNPJ</dt>
                <dd className="text-sm font-medium">{data.notStCgc}</dd>
              </div>
              <div>
                <dt className="text-xs text-gray-500">Inscrição Estadual</dt>
                <dd className="text-sm font-medium">{data.notStIncrestadual}</dd>
              </div>
              <div>
                <dt className="text-xs text-gray-500">Município/UF</dt>
                <dd className="text-sm font-medium">{data.notStMunicipio}/{data.notStUf}</dd>
              </div>
            </dl>
          </div>
        </div>

        {/* Itens da Nota */}
        <div>
          <h3 className="text-sm font-medium text-gray-500 mb-2">Itens da Nota</h3>
          <div className="border rounded-lg overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Descrição</TableHead>
                  <TableHead className="text-right">Quantidade</TableHead>
                  <TableHead className="text-right">Valor Unitário</TableHead>
                  <TableHead className="text-right">Valor Total</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.items.map((item: any) => (
                  <TableRow key={item.id}>
                    <TableCell>{item.itnStDescricao}</TableCell>
                    <TableCell className="text-right">{item.itnReQuantidade}</TableCell>
                    <TableCell className="text-right">{formatCurrency(item.itnReValorunitario)}</TableCell>
                    <TableCell className="text-right">{formatCurrency(item.itnReValortotal)}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>

        {/* Erros (se houver) */}
        {data.errors && data.errors.length > 0 && (
          <div>
            <h3 className="text-sm font-medium text-gray-500 mb-2">Erros</h3>
            <div className="space-y-2">
              {data.errors.map((error: any) => (
                <div key={error.id} className="p-3 bg-red-50 border border-red-200 rounded-lg">
                  <div className="flex items-start gap-2">
                    <Badge variant="destructive" className="mt-1">Erro</Badge>
                    <div>
                      <p className="text-sm text-red-700">{error.errorMessage}</p>
                      <p className="text-xs text-red-500 mt-1">{error.data}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderLinkContent = () => {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
          <div>
            <h3 className="text-sm font-medium text-gray-500">Informações do Pedido</h3>
            <dl className="mt-2 space-y-1">
              <div>
                <dt className="text-xs text-gray-500">Cliente</dt>
                <dd className="text-sm font-medium">{data.cliente}</dd>
              </div>
              <div>
                <dt className="text-xs text-gray-500">Data</dt>
                <dd className="text-sm font-medium">{data.data}</dd>
              </div>
              <div>
                <dt className="text-xs text-gray-500">Valor</dt>
                <dd className="text-sm font-medium">{data.valor}</dd>
              </div>
            </dl>
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-500">Vinculação</h3>
            <dl className="mt-2 space-y-1">
              <div>
                <dt className="text-xs text-gray-500">Status</dt>
                <dd className="text-sm font-medium">
                  <Badge variant={
                    data.vinculacao === 'matriz' 
                      ? 'default' 
                      : data.vinculacao === 'filial' 
                        ? 'secondary' 
                        : 'outline'
                  }>
                    {data.vinculacao === 'matriz' 
                      ? 'Matriz' 
                      : data.vinculacao === 'filial' 
                        ? 'Filial' 
                        : 'Pendente'
                    }
                  </Badge>
                </dd>
              </div>
              <div>
                <dt className="text-xs text-gray-500">Responsável</dt>
                <dd className="text-sm font-medium">{data.responsavel || '-'}</dd>
              </div>
            </dl>
          </div>
        </div>
      </div>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>
        {type === 'invoice' && renderInvoiceContent()}
        {type === 'link' && renderLinkContent()}
      </DialogContent>
    </Dialog>
  );
};

export default ViewModal;
