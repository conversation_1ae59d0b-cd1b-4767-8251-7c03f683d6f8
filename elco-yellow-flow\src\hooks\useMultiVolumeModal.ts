import { useState, useEffect } from 'react';
import { toast } from '@/components/ui/use-toast';
import { api } from '@/lib/api';
import { toSelectOption } from '@/lib/utils';
import { saveRomaneio, saveRomaneioDraft } from '@/services/romaneio-service';

export interface VolumesProps {
  separationOrderId: number;
  orderId: number | string;
  linking: 'matriz' | 'filial';
  volume: number;
  numberPackaging: number;
  length: number;
  width: number;
  height: number;
  netWeight: number;
}

export interface RomaneioProps {
  address: string;
  carrierId: number | string;
  driverId: number | string;
  vehicleId: number | string;
  totalLength: number | string;
  totalWidth: number | string;
  totalHeight: number | string;
  totalWeight: number | string;
  totalVolume: number | string;
  mainPackagingId: number | string;
  secondaryPackagingId?: number | string;
  observations: string;
}

export interface SecondaryPackaging {
  orderCode: string;
  productCode: string;
  volumeFormat: string;
  packagingId: string;
}

export type ProductWeight = {
  id: string;
  productCode: string;
  productName: string;
  weight: number;
  unit: string;
  createdAt: Date;
};

export const useMultiVolumeModal = (orders: any[], isOpen: boolean) => {
  const [volumes, setVolumes] = useState<VolumesProps[]>([]);
  const [romaneio, setRomaneio] = useState<RomaneioProps>({
    address: '',
    carrierId: '',
    driverId: '',
    vehicleId: '',
    totalLength: '',
    totalWidth: '',
    totalHeight: '',
    totalWeight: '',
    totalVolume: '',
    mainPackagingId: '',
    secondaryPackagingId: '',
    observations: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [globalVinculo, setGlobalVinculo] = useState<'matriz' | 'filial'>('filial');
  const [carrierList, setCarrierList] = useState([]);
  const [driverList, setDriverList] = useState([]);
  const [vehicleList, setVehicleList] = useState([]);
  const [packagingList, setPackagingList] = useState([]);
  const [activeTab, setActiveTab] = useState('general');
  const [loadingOrders, setLoadingOrders] = useState<Record<string, boolean>>({});
  const [productsByOrder, setProductsByOrder] = useState<Record<string, any[]>>({});
  const [packagingSelections, setPackagingSelections] = useState<Record<string, Record<string, string>>>({});
  const [productPackagings, setProductPackagings] = useState<{ orderCode: string; productCode: string; productName: string; packagingId: string; }[]>([]);
  const [packagingOptionsFull, setPackagingOptionsFull] = useState<any[]>([]);
  const [secondaryPackagings, setSecondaryPackagings] = useState<SecondaryPackaging[]>([]);
  const [availableProductWeights, setAvailableProductWeights] = useState<ProductWeight[]>([]);
  const [productWeights, setProductWeights] = useState<Record<string, Record<string, string>>>({});
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [invalidSecondaryVolumes, setInvalidSecondaryVolumes] = useState<string[]>([]);
  const [combinedOrders, setCombinedOrders] = useState<any[]>([]);
  const [bulkPackagingSelections, setBulkPackagingSelections] = useState<Record<string, string>>({});
  const [selectedProducts, setSelectedProducts] = useState<Record<string, Set<string>>>({});
  const [appliedFeedback, setAppliedFeedback] = useState<Record<string, Record<string, boolean>>>({});
  const [isUnlinkedFromDraft, setIsUnlinkedFromDraft] = useState(false);
  const [hasDraftAvailable, setHasDraftAvailable] = useState(false);

  // Fetch dropdown data
  useEffect(() => {
    const fetchDropdownData = async () => {
      try {
        const [packagings, drivers, transporters, vehicles] = await Promise.all([
          api.get('/packaging/list'),
          api.get('/drivers/list'),
          api.get('/transporters/list'),
          api.get('/vehicles/list'),
        ]);

        setPackagingOptionsFull(packagings.data);
        setPackagingList(
          packagings.data
            .map((p: any) => toSelectOption(p.id, p.name))
            .filter(Boolean) as { value: string; label: string }[]
        );
        setDriverList(
          drivers.data
            .map((d: any) => toSelectOption(d.id, d.name))
            .filter(Boolean)
        );
        setCarrierList(
          transporters.data
            .map((t: any) => toSelectOption(t.id, t.name))
            .filter(Boolean)
        );
        setVehicleList(
          vehicles.data
            .map((v: any) => toSelectOption(v.id, v.plate))
            .filter(Boolean)
        );
      } catch (error) {
        toast.error('Erro ao carregar dados dos selects');
        console.error(error);
      }
    };

    fetchDropdownData();
  }, []);

  // Fetch available product weights
  useEffect(() => {
    const fetchAvailableProductWeights = async () => {
      if (isOpen) {
        try {
          const response = await api.get('/products/weights');
          setAvailableProductWeights(Array.isArray(response.data) ? response.data : []);
        } catch (error) {
          console.error("Erro ao buscar pesos de produtos cadastrados:", error);
          setAvailableProductWeights([]);
        }
      } else {
        setAvailableProductWeights([]);
      }
    };

    fetchAvailableProductWeights();
  }, [isOpen]);

  // Update volumes linking
  useEffect(() => {
    if (volumes.length > 0) {
      const updatedVolumes = volumes.map(volume => ({
        ...volume,
        linking: globalVinculo
      }));
      setVolumes(updatedVolumes);
    }
  }, [globalVinculo]);

  // Calculate dimensions from secondary packagings
  useEffect(() => {
    const calculatedDimensions: Record<string, { length: number; width: number; height: number; weight: number }> = {};

    const groupedByOrderAndVolume = secondaryPackagings.reduce((acc, sp) => {
      if (!sp.volumeFormat) return acc;
      const [volumeNumberStr] = sp.volumeFormat.split('/');
      const volumeNumber = Number(volumeNumberStr);
      if (isNaN(volumeNumber) || volumeNumber < 1) return acc;

      if (!acc[sp.orderCode]) acc[sp.orderCode] = {};
      if (!acc[sp.orderCode][volumeNumber]) acc[sp.orderCode][volumeNumber] = [];

      if (sp.packagingId) {
        acc[sp.orderCode][volumeNumber].push(sp.packagingId);
      }

      return acc;
    }, {} as Record<string, Record<number, string[]>>);

    for (const orderCode in groupedByOrderAndVolume) {
      let totalOrderLength = 0;
      let totalOrderWidth = 0;
      let totalOrderHeight = 0;
      let totalOrderWeight = 0;

      const volumesInOrder = groupedByOrderAndVolume[orderCode];

      // Somar peso das embalagens secundárias
      for (const volumeNumber in volumesInOrder) {
        const packagingIdsInVolume = volumesInOrder[volumeNumber];
        const uniquePackagingIdsInVolume = Array.from(new Set(packagingIdsInVolume));

        let totalVolumeLength = 0;
        let totalVolumeWidth = 0;
        let totalVolumeHeight = 0;
        let totalVolumeWeight = 0;

        uniquePackagingIdsInVolume.forEach(pkgId => {
          const pkg = packagingOptionsFull.find(p => String(p.id) === String(pkgId));
          if (pkg) {
            totalVolumeLength += pkg.length || 0;
            totalVolumeWidth += pkg.width || 0;
            totalVolumeHeight += pkg.height || 0;
            totalVolumeWeight += pkg.weight || 0;
          }
        });

        totalOrderLength += totalVolumeLength;
        totalOrderWidth += totalVolumeWidth;
        totalOrderHeight += totalVolumeHeight;
        totalOrderWeight += totalVolumeWeight;
      }

      // Somar peso dos produtos do pedido
      const productsInOrder = productsByOrder[orderCode] || [];
      const productWeightsInOrder = productWeights[orderCode] || {};
      
      let totalProductsWeight = 0;
      productsInOrder.forEach(product => {
        const productWeight = Number(productWeightsInOrder[product.code] || 0);
        totalProductsWeight += productWeight;
      });

      // Peso total do pedido = embalagens + produtos
      totalOrderWeight += totalProductsWeight;

      calculatedDimensions[orderCode] = {
        length: totalOrderLength,
        width: totalOrderWidth,
        height: totalOrderHeight,
        weight: totalOrderWeight,
      };
    }

    setVolumes(prev =>
      prev.map(vol => {
        const orderCode = vol.orderId as string;
        const dims = calculatedDimensions[orderCode];
        const cubicVolume = (dims?.length || 0) * (dims?.width || 0) * (dims?.height || 0) / 1_000_000;
        const order = combinedOrders.find(o => o.internalCode === orderCode);
        const numberOfUnitVolumes = Number(order?.volume) || 1;

        return {
          ...vol,
          length: dims?.length || 0,
          width: dims?.width || 0,
          height: dims?.height || 0,
          netWeight: dims?.weight || 0,
          volume: cubicVolume,
          numberPackaging: numberOfUnitVolumes,
        };
      })
    );
  }, [secondaryPackagings, packagingOptionsFull, combinedOrders, productsByOrder, productWeights]);

  // Calculate romaneio totals
  useEffect(() => {
    if (volumes.length > 0) {
      const totalVolume = volumes.reduce((acc, order) => acc + order.volume, 0);
      const totalWeight = volumes.reduce((acc, order) => acc + order.netWeight, 0);
      const totalLength = volumes.reduce((acc, order) => acc + order.length, 0);
      const totalWidth = volumes.reduce((acc, order) => acc + order.width, 0);
      const totalHeight = volumes.reduce((acc, order) => acc + order.height, 0);
      
      setRomaneio(prev => ({
        ...prev,
        totalVolume: totalVolume,
        totalWeight: totalWeight,
        totalLength: totalLength,
        totalWidth: totalWidth,
        totalHeight: totalHeight,
      }));
    }
  }, [volumes]);

  // Calculate romaneio totals with main and secondary packaging
  useEffect(() => {
    let mainPackaging = null;
    let secondaryPackaging = null;
    
    if (romaneio.mainPackagingId) {
      mainPackaging = packagingOptionsFull.find(
        (p) => String(p.id) === String(romaneio.mainPackagingId)
      );
    }
    
    if (romaneio.secondaryPackagingId) {
      secondaryPackaging = packagingOptionsFull.find(
        (p) => String(p.id) === String(romaneio.secondaryPackagingId)
      );
    }

    const totalVolumeVolumes = volumes.reduce((acc, order) => acc + order.volume, 0);
    // O peso dos volumes já inclui embalagens secundárias + produtos
    const totalWeightVolumes = volumes.reduce((acc, order) => acc + order.netWeight, 0);
    const totalLengthVolumes = volumes.reduce((acc, order) => acc + order.length, 0);
    const totalWidthVolumes = volumes.reduce((acc, order) => acc + order.width, 0);
    const totalHeightVolumes = volumes.reduce((acc, order) => acc + order.height, 0);

    // Somar dimensões das embalagens principais
    let mainPackagingLength = mainPackaging ? (mainPackaging.length || 0) : 0;
    let mainPackagingWidth = mainPackaging ? (mainPackaging.width || 0) : 0;
    let mainPackagingHeight = mainPackaging ? (mainPackaging.height || 0) : 0;
    let mainPackagingWeight = mainPackaging ? (mainPackaging.weight || 0) : 0;
    let mainPackagingVolume = mainPackaging ? ((mainPackaging.length || 0) * (mainPackaging.width || 0) * (mainPackaging.height || 0)) / 1_000_000 : 0;
    
    let secondaryPackagingLength = secondaryPackaging ? (secondaryPackaging.length || 0) : 0;
    let secondaryPackagingWidth = secondaryPackaging ? (secondaryPackaging.width || 0) : 0;
    let secondaryPackagingHeight = secondaryPackaging ? (secondaryPackaging.height || 0) : 0;
    let secondaryPackagingWeight = secondaryPackaging ? (secondaryPackaging.weight || 0) : 0;
    let secondaryPackagingVolume = secondaryPackaging ? ((secondaryPackaging.length || 0) * (secondaryPackaging.width || 0) * (secondaryPackaging.height || 0)) / 1_000_000 : 0;

    const totalLength = totalLengthVolumes + mainPackagingLength + secondaryPackagingLength;
    const totalWidth = totalWidthVolumes + mainPackagingWidth + secondaryPackagingWidth;
    const totalHeight = totalHeightVolumes + mainPackagingHeight + secondaryPackagingHeight;
    
    // Peso total = soma de todos os pedidos (que já inclui embalagens + produtos) + embalagens principais
    const totalWeight = totalWeightVolumes + mainPackagingWeight + secondaryPackagingWeight;
    
    const totalVolume = totalVolumeVolumes + mainPackagingVolume + secondaryPackagingVolume;

    setRomaneio((prev) => ({
      ...prev,
      totalLength,
      totalWidth,
      totalHeight,
      totalWeight,
      totalVolume,
    }));
  }, [romaneio.mainPackagingId, romaneio.secondaryPackagingId, volumes, packagingOptionsFull]);

  // Reset state when modal closes
  useEffect(() => {
    if (!isOpen) {
      setErrorMessage(null);
      setProductWeights({});
      setProductsByOrder({});
      setPackagingSelections({});
      setSecondaryPackagings([]);
      setCombinedOrders([]);
      setVolumes([]);
      setRomaneio({
        address: '',
        carrierId: '',
        driverId: '',
        vehicleId: '',
        totalLength: '',
        totalWidth: '',
        totalHeight: '',
        totalWeight: '',
        totalVolume: '',
        mainPackagingId: '',
        secondaryPackagingId: undefined,
        observations: '',
      });
      setGlobalVinculo('filial');
      setIsUnlinkedFromDraft(false);
      setHasDraftAvailable(false);
    }
  }, [isOpen]);

  // Load products for order
  const loadProducts = async (order: any) => {
    const code = order.internalCode;
    if (productsByOrder[code]?.length) return;

    setLoadingOrders(prev => ({ ...prev, [code]: true }));

    try {
      const resp = await api.get(`/separation-orders/external-products/${code}`);
      const products = resp.data;

      const initialProductWeightsForOrder: Record<string, string> = {};
      products.forEach((product: any) => {
        const foundWeight = availableProductWeights.find(
          wp => wp.productCode === product.code || wp.productName === product.name
        );
        if (foundWeight) {
          const totalWeight = foundWeight.weight * (product.quantity || 1);
          initialProductWeightsForOrder[product.code] = String(totalWeight.toFixed(2));
        }
      });

      setProductsByOrder(prev => ({
        ...prev,
        [code]: products,
      }));

      setProductWeights(prev => ({
        ...prev,
        [code]: {
          ...prev[code],
          ...initialProductWeightsForOrder,
        },
      }));

    } catch (err) {
      toast.error(`Erro ao carregar produtos do pedido ${code}`);
      console.error(err);
    } finally {
      setLoadingOrders(prev => ({ ...prev, [code]: false }));
    }
  };

  // Load products when modal opens
  useEffect(() => {
    if (isOpen && combinedOrders.length > 0) {
      combinedOrders.forEach(order => {
        loadProducts(order);
      });
    }
  }, [isOpen, combinedOrders]);

  // Split product function (replaces duplicateProduct)
  const splitProduct = (orderCode: string, productCode: string, quantities: number[]) => {
    const products = productsByOrder[orderCode] || [];
    const productIndex = products.findIndex(p => p.code === productCode);
    
    if (productIndex === -1) return;

    const originalProduct = products[productIndex];
    const originalQuantity = originalProduct.quantity || 1;
    
    // Validar se a soma das quantidades é igual à original
    const totalSplitQuantity = quantities.reduce((sum, qty) => sum + qty, 0);
    if (totalSplitQuantity !== originalQuantity) {
      toast.error(`A soma das quantidades (${totalSplitQuantity}) deve ser igual à quantidade original (${originalQuantity})`);
      return;
    }

    // Criar produtos repartidos
    const splitProducts = quantities.map((quantity, index) => ({
      ...originalProduct,
      id: `${originalProduct.id}_split_${index + 1}`,
      code: `${originalProduct.code}_split_${index + 1}`,
      quantity: quantity,
      originalProductCode: originalProduct.code, // Referência ao produto original
      isSplit: true,
      splitIndex: index + 1,
      totalSplits: quantities.length,
      originalQuantity: originalQuantity
    }));

    // Atualizar produtos do pedido
    const updatedProducts = [...products];
    updatedProducts.splice(productIndex, 1, ...splitProducts);
    
    setProductsByOrder(prev => ({
      ...prev,
      [orderCode]: updatedProducts,
    }));

    // Atualizar pesos dos produtos repartidos
    const originalWeight = productWeights[orderCode]?.[productCode] || '0';
    const originalWeightNumber = Number(originalWeight);
    
    const updatedWeights = { ...productWeights[orderCode] };
    splitProducts.forEach((product, index) => {
      const weightRatio = quantities[index] / originalQuantity;
      const splitWeight = originalWeightNumber * weightRatio;
      updatedWeights[product.code] = splitWeight.toFixed(2);
    });
    
    setProductWeights(prev => ({
      ...prev,
      [orderCode]: updatedWeights,
    }));

    // Limpar seleções e embalagens do produto original
    setPackagingSelections(prev => {
      const updated = { ...prev };
      if (updated[orderCode]) {
        delete updated[orderCode][productCode];
      }
      return updated;
    });

    setSecondaryPackagings(prev => 
      prev.filter(sp => !(sp.orderCode === orderCode && sp.productCode === productCode))
    );

    toast.success(`Produto repartido em ${quantities.length} partes com quantidades: ${quantities.join(', ')}`);
  };

  // Duplicate product function (kept for backward compatibility, but now uses splitProduct)
  const duplicateProduct = (orderCode: string, productCode: string, divisions: number = 2) => {
    const products = productsByOrder[orderCode] || [];
    const productIndex = products.findIndex(p => p.code === productCode);
    
    if (productIndex === -1) return;

    const originalProduct = products[productIndex];
    const originalQuantity = originalProduct.quantity || 1;
    
    // Calcular quantidade por divisão (arredondando para cima para garantir que a soma seja pelo menos igual à original)
    const quantityPerDivision = Math.ceil(originalQuantity / divisions);
    
    // Criar array de quantidades para repartição
    const quantities = Array(divisions).fill(quantityPerDivision);
    
    // Ajustar a última divisão se necessário para que a soma seja exata
    const totalQuantity = quantities.reduce((sum, qty) => sum + qty, 0);
    if (totalQuantity > originalQuantity) {
      quantities[quantities.length - 1] = quantityPerDivision - (totalQuantity - originalQuantity);
    }
    
    // Usar a nova função de repartição
    splitProduct(orderCode, productCode, quantities);
  };

  // Load address by project name
  useEffect(() => {
    const fetchAddressByProjectName = async () => {
      const externalCode = orders?.[0]?.externalCode;
      if (isOpen && externalCode) {
        try {
          // Extrair o apelido do projeto do externalCode (formato: "APELIDO - DESCRIÇÃO")
          const externalCodeParts = externalCode.split(' - ');
          const projectApelido = externalCodeParts[0]?.trim();
          
          console.log('🔍 Buscando endereço para:', { externalCode, projectApelido });
          
          // Tentar buscar por apelido primeiro, depois por externalCode completo
          let response = await api.get(`/addresses?projectName=${encodeURIComponent(projectApelido)}`);
          
          if (!response.data || response.data.length === 0) {
            // Se não encontrar por apelido, tentar pelo externalCode completo
            console.log('📝 Tentando busca por externalCode completo:', externalCode);
            response = await api.get(`/addresses?projectName=${encodeURIComponent(externalCode)}`);
          }
          
          // Se ainda não encontrar, tentar busca por partes do nome
          if (!response.data || response.data.length === 0) {
            console.log('📝 Tentando busca por partes do nome:', projectApelido);
            // Tentar buscar apenas o código numérico se existir
            const numericCode = projectApelido.match(/\d+/)?.[0];
            if (numericCode) {
              response = await api.get(`/addresses?projectName=${encodeURIComponent(numericCode)}`);
            }
          }
          
          if (response.data && response.data.length > 0) {
            console.log('✅ Endereço encontrado:', response.data[0]);
            setRomaneio(prev => ({
              ...prev,
              address: response.data[0].address 
            }));
          } else {
            console.warn('❌ Nenhum endereço encontrado para:', { externalCode, projectApelido });
          }
        } catch (error) {
          console.warn('❌ Erro ao buscar endereço para o projeto:', externalCode, error);
        }
      }
    };

    fetchAddressByProjectName();
  }, [isOpen, orders]);

  return {
    // State
    volumes,
    setVolumes,
    romaneio,
    setRomaneio,
    isSubmitting,
    setIsSubmitting,
    globalVinculo,
    setGlobalVinculo,
    carrierList,
    driverList,
    vehicleList,
    packagingList,
    setPackagingList,
    activeTab,
    setActiveTab,
    loadingOrders,
    productsByOrder,
    setProductsByOrder,
    packagingSelections,
    setPackagingSelections,
    productPackagings,
    setProductPackagings,
    packagingOptionsFull,
    setPackagingOptionsFull,
    secondaryPackagings,
    setSecondaryPackagings,
    availableProductWeights,
    productWeights,
    setProductWeights,
    errorMessage,
    setErrorMessage,
    invalidSecondaryVolumes,
    setInvalidSecondaryVolumes,
    combinedOrders,
    setCombinedOrders,
    bulkPackagingSelections,
    setBulkPackagingSelections,
    selectedProducts,
    setSelectedProducts,
    appliedFeedback,
    setAppliedFeedback,
    isUnlinkedFromDraft,
    setIsUnlinkedFromDraft,
    hasDraftAvailable,
    setHasDraftAvailable,
    
    // Functions
    loadProducts,
    duplicateProduct,
    splitProduct,
  };
}; 