import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Box, ArrowRight } from 'lucide-react';
import { VolumesProps } from '@/hooks/useMultiVolumeModal';

interface OrdersTabProps {
  combinedOrders: any[];
  volumes: VolumesProps[];
  setVolumes: React.Dispatch<React.SetStateAction<VolumesProps[]>>;
  handleOrderChange: (orderIndex: number, value: string, field: string) => void;
  handleLengthChange: (orderIndex: number, value: string) => void;
  handleWidthChange: (orderIndex: number, value: string) => void;
  handleHeightChange: (orderIndex: number, value: string) => void;
  setActiveTab: (tab: string) => void;
  handleSaveDraft: () => void;
  isSubmitting: boolean;
  isUnlinkedFromDraft: boolean;
}

const OrdersTab: React.FC<OrdersTabProps> = ({
  combinedOrders,
  volumes,
  setVolumes,
  handleOrderChange,
  handleLengthChange,
  handleWidthChange,
  handleHeightChange,
  setActiveTab,
  handleSaveDraft,
  isSubmitting,
  isUnlinkedFromDraft,
}) => {
  return (
    <div className="space-y-4 mt-2">
      <div className="max-h-[60vh] overflow-y-auto pr-2">
        <Accordion type="multiple" defaultValue={combinedOrders.map((_, index) => `item-${index}`)}>
          {combinedOrders.map((order, index) => {
            const orderId = order.internalCode;
            const volumeEntry = volumes.find(v => v.separationOrderId === order.id);

            return (
              <AccordionItem value={`item-${index}`} key={orderId} className="border rounded-lg mb-3 bg-white">
                <AccordionTrigger className="px-4 hover:bg-gray-50 hover:no-underline">
                  <div className="flex flex-1 justify-between items-center">
                    <div className="flex items-center gap-2">
                      <Box className="h-5 w-5 text-primary" />
                      <span className="font-semibold">Pedido #{order.internalCode ?? order.id}</span>
                    </div>
                    <span className="text-sm text-muted-foreground">
                      {order.cliente ?? order.externalCode}
                    </span>
                  </div>
                </AccordionTrigger>
                
                <AccordionContent className="px-4 pt-2 pb-4">
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor={`length-${orderId}`}>Comprimento (cm)</Label>
                        <Input
                          id={`length-${orderId}`}
                          placeholder="0.00"
                          type="number"
                          value={volumeEntry?.length || ''}
                          onChange={(event) => {
                            handleOrderChange(index, event.target.value, 'length');
                            handleLengthChange(index, event.target.value);
                          }}
                          className={!volumeEntry?.length ? "border-red-300" : ""}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor={`width-${orderId}`}>Largura (cm)</Label>
                        <Input
                          id={`width-${orderId}`}
                          placeholder="0.00"
                          type="number"
                          value={volumeEntry?.width || ''}
                          onChange={(event) => {
                            handleOrderChange(index, event.target.value, 'width');
                            handleWidthChange(index, event.target.value);
                          }}
                          className={!volumeEntry?.width ? "border-red-300" : ""}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor={`height-${orderId}`}>Altura (cm)</Label>
                        <Input
                          id={`height-${orderId}`}
                          placeholder="0.00"
                          type="number"
                          value={volumeEntry?.height || ''}
                          onChange={(event) => {
                            handleOrderChange(index, event.target.value, 'height');
                            handleHeightChange(index, event.target.value);
                          }}
                          className={!volumeEntry?.height ? "border-red-300" : ""}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor={`weight-${orderId}`}>Peso Total do Pedido (kg)</Label>
                        <Input
                          id={`weight-${orderId}`}
                          placeholder="0.00"
                          type="number"
                          value={(volumeEntry?.netWeight || 0).toFixed(2)}
                          disabled
                          className="bg-gray-50"
                          title="Peso calculado automaticamente (embalagens + produtos)"
                        />
                        <p className="text-xs text-muted-foreground">
                          Peso das embalagens + produtos
                        </p>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor={`volume-${orderId}`}>Volume Unitário (m³)</Label>
                        <Input
                          id={`volume-${orderId}`}
                          placeholder="0.00"
                          type="number"
                          value={Number(order.volume)?.toFixed(3) || ''}
                          disabled
                          className="bg-gray-50"
                        />
                      </div>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>
            );
          })}
        </Accordion>
      </div>

      <div className="flex justify-between mt-4">
        <Button
          variant="outline"
          onClick={() => setActiveTab('packaging')}
          className="gap-2"
        >
          Voltar para Embalagens
        </Button>

        <div className="space-x-2">
          <Button 
            className={`${isUnlinkedFromDraft ? 'bg-gray-400 hover:bg-gray-500' : 'bg-yellow-500 hover:bg-yellow-600'} text-white`}
            onClick={handleSaveDraft} 
            disabled={isSubmitting || isUnlinkedFromDraft}
            title={isUnlinkedFromDraft ? "Desative o modo 'Novo Romaneio' para salvar rascunho" : "Salvar dados como rascunho"}
          >
            Salvar Rascunho
          </Button>
          <Button
            variant="outline"
            onClick={() => setActiveTab('observations')}
            className="gap-2"
          >
            Avançar para Observações <ArrowRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default OrdersTab; 