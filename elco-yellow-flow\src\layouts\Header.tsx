
import React from 'react';
import { User, Settings, LogOut } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useNavigate } from 'react-router-dom';
import { useIsMobile } from '@/hooks/use-mobile';

interface HeaderProps {
  title: string;
}

const Header: React.FC<HeaderProps> = ({ title }) => {
  const navigate = useNavigate();
  const isMobile = useIsMobile();

  const handleProfileClick = () => {
    navigate('/profile');
  };

  const handleSettingsClick = () => {
    navigate('/settings');
  };

  const handleLogout = () => {
    localStorage.removeItem('isAuthenticated');
    navigate('/login');
  };

  return (
    <header className="sticky top-0 z-50 h-14 bg-white/80 backdrop-blur-xl border-b border-gray-100 px-6 flex items-center justify-between">
      <div className="flex items-center">
        <h1 className="text-lg font-medium text-gray-900 tracking-tight">
          {!isMobile && title}
        </h1>
      </div>
      
      <div className="flex items-center gap-2">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button 
              variant="ghost" 
              size="icon" 
              className="h-8 w-8 rounded-xl hover:bg-gray-100/80 transition-all duration-200 focus:ring-0 focus:ring-offset-0"
            >
              <User className="h-4 w-4 text-gray-600" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48 border-0 shadow-xl bg-white/95 backdrop-blur-xl rounded-2xl p-2">
            <DropdownMenuLabel className="text-gray-900 font-medium px-3 py-2">Minha Conta</DropdownMenuLabel>
            <DropdownMenuSeparator className="bg-gray-100 my-1" />
            <DropdownMenuItem 
              onClick={handleProfileClick}
              className="cursor-pointer hover:bg-gray-100/80 focus:bg-gray-100/80 rounded-xl px-3 py-2 transition-all duration-200"
            >
              <User className="mr-3 h-4 w-4 text-gray-500" />
              <span className="text-gray-700 text-sm">Perfil</span>
            </DropdownMenuItem>
            <DropdownMenuItem 
              onClick={handleSettingsClick}
              className="cursor-pointer hover:bg-gray-100/80 focus:bg-gray-100/80 rounded-xl px-3 py-2 transition-all duration-200"
            >
              <Settings className="mr-3 h-4 w-4 text-gray-500" />
              <span className="text-gray-700 text-sm">Configurações</span>
            </DropdownMenuItem>
            <DropdownMenuSeparator className="bg-gray-100 my-1" />
            <DropdownMenuItem 
              onClick={handleLogout} 
              className="cursor-pointer text-red-600 hover:bg-red-50 focus:bg-red-50 hover:text-red-700 rounded-xl px-3 py-2 transition-all duration-200"
            >
              <LogOut className="mr-3 h-4 w-4" />
              <span className="font-medium text-sm">Logout</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  );
};

export default Header;
