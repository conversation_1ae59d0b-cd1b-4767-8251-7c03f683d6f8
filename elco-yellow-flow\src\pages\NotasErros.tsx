import React, { useState, useEffect } from 'react';
import Layout from '@/layouts/Layout';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from '@/components/ui/table';
import { Eye, MailWarning, Search, Filter, Download, Check, X, Loader2, Edit, Save } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import ViewModal from '@/features/expeditions/components/ViewModal';
import { Progress } from '@/components/ui/progress';
import { Checkbox } from '@/components/ui/checkbox';
import ConfirmReenvioModal from '@/features/shipping-notes/components/ConfirmReenvioModal';
import SmartPagination from '@/components/ui/SmartPagination';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import toast from 'react-hot-toast';
import { api } from '@/lib/api';

interface VnfError {
  id: number;
  numeroNota: string;
  clienteExternalCode: string | null;
  nomeFornecedor: string | null;
  tipoErro: string;
  compradorEmail: string | null;
  fornecedorEmail: string | null;
  dataEmailEnviado: string | null;
  createdAt: string;
}

const NotasErros = () => {
  const [notasErros, setNotasErros] = useState<VnfError[]>([]);
  const [filteredNotasErros, setFilteredNotasErros] = useState<VnfError[]>([]);
  const [loading, setLoading] = useState(true);
  const [viewModalOpen, setViewModalOpen] = useState(false);
  const [selectedNota, setSelectedNota] = useState<VnfError | null>(null);
  const [selectedNotas, setSelectedNotas] = useState<string[]>([]);
  const [modalConfirmOpen, setModalConfirmOpen] = useState(false);
  const [editingEmail, setEditingEmail] = useState<number | null>(null);
  const [editingEmailValue, setEditingEmailValue] = useState<string>('');
  const [savingEmail, setSavingEmail] = useState<number | null>(null);

  // Estados para paginação e filtros
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [searchQuery, setSearchQuery] = useState('');
  const [fornecedorFilter, setFornecedorFilter] = useState('todos');
  const [tipoErroFilter, setTipoErroFilter] = useState('todos');
  const [statusFilter, setStatusFilter] = useState('todos');

  const fetchNotasErros = async () => {
    setLoading(true);
    try {
      const res = await api.get('/vnf/erros');
      setNotasErros(res.data);
      setFilteredNotasErros(res.data);
      
      // Toast informativo sobre quantidade de erros
      if (res.data.length === 0) {
        toast.success('Nenhum erro encontrado. Sistema limpo!');
      } else {
        toast.success(`${res.data.length} erro(s) carregado(s) com sucesso`);
      }
    } catch (e) {
      setNotasErros([]);
      setFilteredNotasErros([]);
      toast.error('Erro ao carregar lista de erros. Tente novamente.');
    } finally {
      setLoading(false);
    }
  };

  // Aplicar filtros
  useEffect(() => {
    let filtered = [...notasErros];

    // Filtro por busca (número da nota ou fornecedor)
    if (searchQuery) {
      const lowerSearchQuery = searchQuery.toLowerCase();
      filtered = filtered.filter(nota =>
        nota.numeroNota.toLowerCase().includes(lowerSearchQuery) ||
        (nota.nomeFornecedor && nota.nomeFornecedor.toLowerCase().includes(lowerSearchQuery)) ||
        (nota.clienteExternalCode && nota.clienteExternalCode.toLowerCase().includes(lowerSearchQuery))
      );
    }

    // Filtro por fornecedor
    if (fornecedorFilter && fornecedorFilter !== 'todos') {
      filtered = filtered.filter(nota =>
        nota.nomeFornecedor === fornecedorFilter
      );
    }

    // Filtro por tipo de erro
    if (tipoErroFilter && tipoErroFilter !== 'todos') {
      filtered = filtered.filter(nota =>
        nota.tipoErro === tipoErroFilter
      );
    }

    // Filtro por status (email enviado ou não)
    if (statusFilter && statusFilter !== 'todos') {
      if (statusFilter === 'enviado') {
        filtered = filtered.filter(nota => nota.dataEmailEnviado);
      } else if (statusFilter === 'pendente') {
        filtered = filtered.filter(nota => !nota.dataEmailEnviado);
      }
    }

    setFilteredNotasErros(filtered);
    setCurrentPage(1); // Reset para primeira página quando filtrar
  }, [notasErros, searchQuery, fornecedorFilter, tipoErroFilter, statusFilter]);

  // Calcular dados para paginação
  const totalPages = Math.ceil(filteredNotasErros.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentItems = filteredNotasErros.slice(startIndex, endIndex);

  // Obter opções únicas para filtros
  const fornecedoresUnicos = [...new Set(notasErros.map(n => n.nomeFornecedor).filter(Boolean))];
  const tiposErroUnicos = [...new Set(notasErros.map(n => n.tipoErro))];

  const updateFornecedorEmail = async (erroId: number, email: string) => {
    const erro = notasErros.find(n => n.id === erroId);
    if (!erro || !erro.nomeFornecedor) {
      toast.error('Nome do fornecedor não encontrado');
      return;
    }

    setSavingEmail(erroId);
    try {
      const response = await api.post('/vnf/salvar-email-fornecedor', {
        nomeFornecedor: erro.nomeFornecedor,
        email: email
      });

      if (response.data.success) {
        toast.success(response.data.message);
        // Recarregar a lista completa para atualizar todos os erros com o mesmo fornecedor
        await fetchNotasErros();
        // Limpar o estado de edição
        setEditingEmail(null);
        setEditingEmailValue('');
      } else {
        toast.error(response.data.message);
      }
    } catch (error) {
      console.error('Erro ao salvar email:', error);
      toast.error('Erro ao salvar email do fornecedor');
    } finally {
      setSavingEmail(null);
    }
  };

  useEffect(() => {
    fetchNotasErros();
  }, []);

  const handleViewNota = (nota: VnfError) => {
    setSelectedNota(nota);
    setViewModalOpen(true);
  };

  const handleSelectNota = (id: string) => {
    const nota = notasErros.find(n => n.id.toString() === id);
    
    // Não permitir selecionar fornecedores sem email
    if (nota && !nota.fornecedorEmail) {
      toast.error(
        `Não é possível selecionar o fornecedor "${nota.nomeFornecedor}" que não possui email cadastrado. ` +
        'Por favor, cadastre o email do fornecedor primeiro.',
        { duration: 6000 }
      );
      return;
    }
    
    setSelectedNotas((prev) =>
      prev.includes(id) ? prev.filter(n => n !== id) : [...prev, id]
    );
  };

  const handleSelectAllNotas = () => {
    if (selectedNotas.length === currentItems.length) {
      setSelectedNotas([]);
      toast.success('Seleção removida');
    } else {
      // Selecionar apenas fornecedores com email
      const fornecedoresComEmail = currentItems.filter(n => n.fornecedorEmail);
      const fornecedoresSemEmail = currentItems.filter(n => !n.fornecedorEmail);
      
      if (fornecedoresSemEmail.length > 0) {
        toast.error(
          `${fornecedoresSemEmail.length} fornecedor(es) sem email foram ignorados. ` +
          'Selecione apenas fornecedores com email cadastrado.',
          { duration: 6000 }
        );
      }
      
      setSelectedNotas(fornecedoresComEmail.map(n => n.id.toString()));
      toast.success(`${fornecedoresComEmail.length} erro(s) selecionado(s)`);
    }
  };

  const [isSendingEmails, setIsSendingEmails] = useState(false);
  const [loadingActions, setLoadingActions] = useState<{ [key: number]: 'aceitar' | 'recusar' | null }>({});

  const handleReenviarEmails = () => {
    if (selectedNotas.length === 0) {
      toast.error("Selecione ao menos uma nota para enviar o e-mail.");
      return;
    }

    // Verificar se há fornecedores sem email
    const errosSelecionados = notasErros.filter(n => selectedNotas.includes(n.id.toString()));
    const fornecedoresSemEmail = errosSelecionados.filter(erro => !erro.fornecedorEmail);
    
    if (fornecedoresSemEmail.length > 0) {
      const fornecedoresUnicos = [...new Set(fornecedoresSemEmail.map(erro => erro.nomeFornecedor))];
      toast.error(
        `Não é possível enviar emails para ${fornecedoresUnicos.length} fornecedor(es) que não possuem email cadastrado: ${fornecedoresUnicos.join(', ')}. ` +
        'Por favor, cadastre o email do fornecedor antes de tentar enviar o email.',
        { duration: 8000 }
      );
      return;
    }

    toast.success(`Preparando envio de ${selectedNotas.length} email(s)...`);
    setModalConfirmOpen(true);
  };

  const confirmarReenvio = async (emails: string[]) => {
    setIsSendingEmails(true);
    
    // Calcular quantos emails serão enviados (agrupando por fornecedor e nota)
    const errosSelecionados = notasErros.filter(n => selectedNotas.includes(n.id.toString()));
    const grupos = new Map<string, VnfError[]>();
    
    for (const erro of errosSelecionados) {
      const chave = `${erro.fornecedorEmail || 'sem-email'}_${erro.numeroNota}`;
      if (!grupos.has(chave)) {
        grupos.set(chave, []);
      }
      grupos.get(chave)!.push(erro);
    }
    
    const totalEmails = grupos.size * emails.length; // Multiplicar pelos emails adicionais
    const totalErros = selectedNotas.length;
    
    toast.loading(`Enviando ${totalEmails} email(s) para ${totalErros} erro(s) agrupados...`, { id: 'enviar-lote' });
    
    try {
      // Converter IDs de string para number
      const erroIds = selectedNotas.map(id => parseInt(id));
      
      const response = await api.post('/vnf/enviar-emails-lote', { 
        erroIds,
        emailsAdicionais: emails // Enviar para todos os emails selecionados
      });
      
      if (response.data.success) {
        const successCount = response.data.results.filter((r: any) => r.success).length;
        const totalCount = response.data.results.length;
        
        if (successCount === totalCount) {
          toast.success(`${successCount} erro(s) processados com ${totalEmails} email(s) enviados com sucesso!`, { id: 'enviar-lote' });
        } else {
          toast.success(`${successCount} de ${totalCount} erro(s) processados. ${totalEmails} email(s) enviados.`, { id: 'enviar-lote' });
          
          // Mostrar erros específicos
          const errors = response.data.results.filter((r: any) => !r.success);
          errors.forEach((error: any) => {
            toast.error(`Erro ID ${error.id}: ${error.message}`);
          });
        }
        
        // Recarregar dados da tabela
        await fetchNotasErros();
        setSelectedNotas([]);
      } else {
        toast.error('Erro ao enviar emails em lote', { id: 'enviar-lote' });
      }
    } catch (error) {
      console.error('Erro ao enviar emails:', error);
      toast.error('Erro ao enviar emails. Verifique a conexão e tente novamente.', { id: 'enviar-lote' });
    } finally {
      setIsSendingEmails(false);
      setModalConfirmOpen(false);
    }
  };

  const handleAceitarErro = async (erroId: number) => {
    // Verificar se o erro tem email do fornecedor
    const erro = notasErros.find(n => n.id === erroId);
    if (!erro) {
      toast.error('Erro não encontrado');
      return;
    }

    if (!erro.fornecedorEmail) {
      toast.error(
        `Não é possível enviar email para o fornecedor "${erro.nomeFornecedor}" que não possui email cadastrado. ` +
        'Por favor, cadastre o email do fornecedor antes de tentar enviar o email.',
        { duration: 8000 }
      );
      return;
    }

    setLoadingActions(prev => ({ ...prev, [erroId]: 'aceitar' }));
    toast.loading('Enviando email para o fornecedor...', { id: `aceitar-${erroId}` });
    
    try {
      const response = await api.post('/vnf/aceitar-erro', { erroId });
      
      if (response.data.success) {
        toast.success(response.data.message, { id: `aceitar-${erroId}` });
        await fetchNotasErros(); // Recarregar dados
      } else {
        toast.error(response.data.message, { id: `aceitar-${erroId}` });
      }
    } catch (error) {
      console.error('Erro ao aceitar erro:', error);
      toast.error('Erro ao aceitar erro. Verifique a conexão e tente novamente.', { id: `aceitar-${erroId}` });
    } finally {
      setLoadingActions(prev => ({ ...prev, [erroId]: null }));
    }
  };

  const handleRecusarErro = async (erroId: number) => {
    setLoadingActions(prev => ({ ...prev, [erroId]: 'recusar' }));
    toast.loading('Recusando erro...', { id: `recusar-${erroId}` });
    
    try {
      const response = await api.post('/vnf/recusar-erro', { erroId });
      
      if (response.data.success) {
        toast.success(response.data.message, { id: `recusar-${erroId}` });
        await fetchNotasErros(); // Recarregar dados
      } else {
        toast.error(response.data.message, { id: `recusar-${erroId}` });
      }
    } catch (error) {
      console.error('Erro ao recusar erro:', error);
      toast.error('Erro ao recusar erro. Verifique a conexão e tente novamente.', { id: `recusar-${erroId}` });
    } finally {
      setLoadingActions(prev => ({ ...prev, [erroId]: null }));
    }
  };

  const totalNotas = notasErros.length;
  const totalEmailsEnviados = notasErros.filter(n => n.dataEmailEnviado).length;
  const totalAguardando = notasErros.filter(n => !n.dataEmailEnviado).length;
  const taxaResolucao = totalNotas > 0 ? Math.round((totalEmailsEnviados / totalNotas) * 100) : 0;

  const errosPorTipo = notasErros.reduce((acc: Record<string, number>, curr) => {
    acc[curr.tipoErro] = (acc[curr.tipoErro] || 0) + 1;
    return acc;
  }, {});
  const totalErros: any = Object.values(errosPorTipo).reduce((a, b) => Number(a) + Number(b), 0);
  const errosFrequentes = Object.entries(errosPorTipo)
    .map(([tipo, count]) => ({ tipo, count: Number(count), percent: totalErros > 0 ? Math.round((Number(count) / totalErros) * 100) : 0 }))
    .sort((a, b) => b.count - a.count);

  return (
    <Layout title="Verificador de Notas">
      {/* Estatísticas */}
      <div className="mb-6 grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="border-0 shadow-md">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total de Erros</p>
                <p className="text-2xl font-bold text-gray-900">{totalNotas}</p>
              </div>
              <MailWarning className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-md">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">E-mails Enviados</p>
                <p className="text-2xl font-bold text-green-600">{totalEmailsEnviados}</p>
              </div>
              <MailWarning className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-md">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Aguardando</p>
                <p className="text-2xl font-bold text-yellow-600">{totalAguardando}</p>
              </div>
              <MailWarning className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-md">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Taxa de Resolução</p>
                <p className="text-2xl font-bold text-blue-600">{taxaResolucao}%</p>
              </div>
              <div className="w-8 h-8">
                <Progress value={taxaResolucao} className="h-2" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Erros Mais Frequentes */}
      {errosFrequentes.length > 0 && (
        <div className="mb-6">
          <Card className="border-0 shadow-md">
            <CardHeader>
              <CardTitle className="text-lg">Erros Mais Frequentes</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {errosFrequentes.slice(0, 5).map((erro, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900 truncate">{erro.tipo}</p>
                      <p className="text-xs text-gray-500">{erro.count} ocorrências</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-bold text-blue-600">{erro.percent}%</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filtros e Busca */}
      <div className="mb-8 rounded-xl bg-white border shadow-sm p-5">
        <div className="flex flex-col md:flex-row justify-between gap-4">
          <div className="flex flex-wrap items-center gap-4 flex-grow">
            <div className="relative w-full md:w-auto">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                type="text"
                placeholder="Buscar por nota ou fornecedor..."
                className="pl-9 w-full"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            <Select value={fornecedorFilter} onValueChange={setFornecedorFilter}>
              <SelectTrigger className="w-[200px] bg-white">
                <SelectValue placeholder="Filtrar por Fornecedor" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="todos">Todos os Fornecedores</SelectItem>
                {fornecedoresUnicos.map((fornecedor) => (
                  <SelectItem key={fornecedor} value={fornecedor}>
                    {fornecedor}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={tipoErroFilter} onValueChange={setTipoErroFilter}>
              <SelectTrigger className="w-[200px] bg-white">
                <SelectValue placeholder="Filtrar por Tipo de Erro" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="todos">Todos os Tipos</SelectItem>
                {tiposErroUnicos.map((tipo) => (
                  <SelectItem key={tipo} value={tipo}>
                    {tipo}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[150px] bg-white">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="todos">Todos</SelectItem>
                <SelectItem value="enviado">Email Enviado</SelectItem>
                <SelectItem value="pendente">Pendente</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex space-x-2 w-full md:w-auto md:ml-auto justify-end">
            <Button
              onClick={handleReenviarEmails}
              disabled={selectedNotas.length === 0 || isSendingEmails}
              className="gap-2 bg-green-600 hover:bg-green-700 text-white"
            >
              <MailWarning className="h-4 w-4" />
              {isSendingEmails ? 'Enviando...' : `Enviar E-mails (${selectedNotas.length})`}
            </Button>
          </div>
        </div>
      </div>

      {/* Tabela de Erros */}
      <div className="rounded-xl overflow-hidden border shadow-sm">
        <div className="flex justify-end p-2 bg-gray-50 border-b">
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-500">Itens por página:</span>
            <Select
              value={String(itemsPerPage)}
              onValueChange={(value) => {
                setItemsPerPage(Number(value));
                setCurrentPage(1);
              }}
            >
              <SelectTrigger className="w-[80px]">
                <SelectValue placeholder="10" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="5">5</SelectItem>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="20">20</SelectItem>
                <SelectItem value="50">50</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Table>
          <TableHeader className="bg-gray-50">
            <TableRow>
              <TableHead className="w-10">
                <Checkbox
                  checked={selectedNotas.length === currentItems.length && currentItems.length > 0}
                  onCheckedChange={handleSelectAllNotas}
                  aria-label="Selecionar todas"
                />
              </TableHead>
              <TableHead className="w-[100px] font-medium">Nota #</TableHead>
              <TableHead className="w-[200px] font-medium">Fornecedor</TableHead>
              <TableHead className="w-[320px] font-medium">Tipo de Erro</TableHead>
              <TableHead className="w-[150px] font-medium">Comprador</TableHead>
              <TableHead className="w-[150px] font-medium">Email Fornecedor</TableHead>
              <TableHead className="w-[120px] font-medium">Email Enviado</TableHead>
              <TableHead className="w-[120px] text-right font-medium">Ações</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-8">
                  <div className="flex flex-col items-center space-y-2">
                    <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
                    <p className="text-gray-500">Carregando erros...</p>
                  </div>
                </TableCell>
              </TableRow>
            ) : currentItems.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-8">
                  <div className="flex flex-col items-center space-y-2">
                    <MailWarning className="h-8 w-8 text-gray-400" />
                    <p className="text-gray-500">
                      {filteredNotasErros.length === 0 && notasErros.length > 0 
                        ? 'Nenhum erro encontrado com os filtros aplicados.' 
                        : 'Nenhum erro encontrado.'}
                    </p>
                    <p className="text-sm text-gray-400">
                      {filteredNotasErros.length === 0 && notasErros.length > 0 
                        ? 'Tente ajustar os filtros de busca.' 
                        : 'O sistema está limpo!'}
                    </p>
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              currentItems.map((nota) => (
                <TableRow key={nota.id} className="border-b hover:bg-gray-50/80 transition-colors">
                  <TableCell>
                    <Checkbox
                      checked={selectedNotas.includes(nota.id.toString())}
                      onCheckedChange={() => handleSelectNota(nota.id.toString())}
                      aria-label={`Selecionar nota ${nota.numeroNota}`}
                      disabled={!nota.fornecedorEmail}
                      className={!nota.fornecedorEmail ? 'opacity-50' : ''}
                    />
                  </TableCell>
                  <TableCell className="font-medium w-[100px]">{nota.numeroNota}</TableCell>
                  <TableCell className="font-medium text-gray-900 w-[200px]">
                    <div className="max-w-[180px]">
                      <span className="font-medium text-gray-900">
                        {nota.nomeFornecedor || nota.clienteExternalCode || '-'}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="w-[320px]">
                    <div className="max-w-[300px]">
                      <span 
                        className="inline-block px-3 py-1.5 rounded-lg text-xs font-medium bg-red-100 text-red-800 border border-red-200 break-words leading-relaxed cursor-help"
                        title={nota.tipoErro}
                      >
                        {nota.tipoErro}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="w-[150px]">{nota.compradorEmail || '-'}</TableCell>
                  <TableCell className="w-[150px]">
                    {editingEmail === nota.id ? (
                      <Input
                        value={editingEmailValue}
                        onChange={(e) => setEditingEmailValue(e.target.value)}
                        onBlur={() => {
                          if (editingEmailValue.trim() !== '') {
                            updateFornecedorEmail(nota.id, editingEmailValue);
                          } else {
                            // Se o campo estiver vazio, apenas cancelar a edição
                            setEditingEmail(null);
                            setEditingEmailValue('');
                          }
                        }}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            if (editingEmailValue.trim() !== '') {
                              updateFornecedorEmail(nota.id, editingEmailValue);
                            } else {
                              setEditingEmail(null);
                              setEditingEmailValue('');
                            }
                          } else if (e.key === 'Escape') {
                            // Cancelar edição com Escape
                            setEditingEmail(null);
                            setEditingEmailValue('');
                          }
                        }}
                        className="w-full"
                        autoFocus
                      />
                    ) : (
                      <span className={`text-sm ${nota.fornecedorEmail ? 'text-gray-900' : 'text-red-600 font-medium'}`}>
                        {nota.fornecedorEmail || 'Email não cadastrado'}
                      </span>
                    )}
                  </TableCell>
                  <TableCell className="w-[120px]">{nota.dataEmailEnviado ? new Date(nota.dataEmailEnviado).toLocaleDateString() : '-'}</TableCell>
                  <TableCell className="w-[120px]">
                    <div className="flex justify-end space-x-1">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleAceitarErro(nota.id)}
                        className={`h-8 w-8 ${nota.fornecedorEmail ? 'text-green-600 hover:text-green-700 hover:bg-green-50' : 'text-gray-400 cursor-not-allowed'}`}
                        title={nota.fornecedorEmail ? "Aceitar e enviar email" : "Email do fornecedor não cadastrado"}
                        disabled={!nota.fornecedorEmail}
                      >
                        {loadingActions[nota.id] === 'aceitar' ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <Check className="h-4 w-4" />
                        )}
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleRecusarErro(nota.id)}
                        className="h-8 w-8 text-red-600 hover:text-red-700 hover:bg-red-50"
                        title="Recusar erro"
                      >
                        {loadingActions[nota.id] === 'recusar' ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <X className="h-4 w-4" />
                        )}
                      </Button>
                      {editingEmail === nota.id ? (
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => {
                            if (editingEmailValue.trim() !== '') {
                              updateFornecedorEmail(nota.id, editingEmailValue);
                            } else {
                              // Se o campo estiver vazio, apenas cancelar a edição
                              setEditingEmail(null);
                              setEditingEmailValue('');
                            }
                          }}
                          className="h-8 w-8 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                          title="Salvar email"
                          disabled={savingEmail === nota.id}
                        >
                          {savingEmail === nota.id ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <Save className="h-4 w-4" />
                          )}
                        </Button>
                      ) : (
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => {
                            setEditingEmail(nota.id);
                            setEditingEmailValue(nota.fornecedorEmail || '');
                          }}
                          className="h-8 w-8 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                          title="Editar email"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
        <div className="p-4 border-t">
          <SmartPagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={setCurrentPage}
          />
        </div>
      </div>

      {/* {selectedNota && (
        <ViewModal
          isOpen={viewModalOpen}
          onClose={() => setViewModalOpen(false)}
          title={`Nota com Erro ${selectedNota.id}`}
          data={selectedNota}
          type="error"
        />
      )} */}

      <ConfirmReenvioModal
        isOpen={modalConfirmOpen}
        onClose={() => setModalConfirmOpen(false)}
        onConfirm={confirmarReenvio}
        isSending={isSendingEmails}
        notasSelecionadas={(() => {
          const errosSelecionados = notasErros.filter(n => selectedNotas.includes(n.id.toString()));
          const grupos = new Map<string, VnfError[]>();
          
          for (const erro of errosSelecionados) {
            const chave = `${erro.fornecedorEmail || 'sem-email'}_${erro.numeroNota}`;
            if (!grupos.has(chave)) {
              grupos.set(chave, []);
            }
            grupos.get(chave)!.push(erro);
          }
          
          return Array.from(grupos.values()).map(grupo => {
            const primeiro = grupo[0];
            return {
              id: primeiro.numeroNota,
              cliente: primeiro.nomeFornecedor || primeiro.clienteExternalCode || 'N/A',
              emailEnviado: primeiro.dataEmailEnviado ? new Date(primeiro.dataEmailEnviado).toLocaleDateString() : '-',
              fornecedorEmail: primeiro.fornecedorEmail,
              tipoErro: grupo.length > 1 ? `${grupo.length} erros agrupados` : primeiro.tipoErro,
              quantidade: grupo.length
            };
          });
        })()}
      />
    </Layout>
  );
};

export default NotasErros;
