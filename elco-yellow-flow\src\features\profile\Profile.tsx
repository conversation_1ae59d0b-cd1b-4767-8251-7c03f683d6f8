
import React, { useEffect, useState } from 'react';
import MainLayout from '@/layouts/MainLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { toast } from '@/components/ui/use-toast';
import { api } from '@/lib/api';

const Profile = () => {
  const [isEditing, setIsEditing] = useState(false);
  const [userForm, setUserForm] = useState({
    name: '',
    email: '',
    phone: '',
    department: '',
    role: ''
  });

  const [formData, setFormData] = useState({ ...userForm });

  const fetchUserProfile = async () => {
    try {
      const res = await api.get('/users/me');

      setUserForm(res.data);
      setFormData(res.data);
    } catch (err) {
      toast.error('Erro ao carregar perfil');
    }
  };

  useEffect(() => {
    fetchUserProfile();
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    let newValue = value;
  
    if (name === 'phone') {
      newValue = maskPhone(value);
    }
  
    setFormData({
      ...formData,
      [name]: newValue,
    });
  };

  const handleSave = async () => {
    try {
      await api.put('/users/me', {
        name: formData.name,
        email: formData.email,
        phone: formData.phone,
      });

      setUserForm({ ...formData });
      setIsEditing(false);
      toast.success('Perfil atualizado com sucesso!');
    } catch (error) {
      console.error(error);
      toast.error('Erro ao atualizar o perfil');
    }
  };

  const handleCancel = () => {
    setFormData({ ...userForm });
    setIsEditing(false);
  };

  function maskPhone(value: string) {
    return value
      .replace(/\D/g, '')
      .replace(/^(\d{2})(\d)/, '($1) $2')
      .replace(/(\d{5})(\d{1,4})/, '$1-$2')
      .replace(/(-\d{4})\d+?$/, '$1');
  }

  return (
    <MainLayout title="Meu Perfil">
      <div className="max-w-3xl mx-auto">
        <div className="grid gap-6 md:grid-cols-3">
          <Card className="md:col-span-1">
            <CardHeader className="text-center">
              <div className="flex justify-center mb-4">
                <Avatar className="h-24 w-24">
                  <AvatarImage src="" alt="Profile" />
                  <AvatarFallback className="text-3xl bg-elco-200 text-elco-800">JS</AvatarFallback>
                </Avatar>
              </div>
              <CardTitle>{userForm.name}</CardTitle>
              <CardDescription>{userForm.role}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-muted-foreground">E-mail:</span>
                <span className="truncate max-w-[150px]">{userForm.email}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Telefone:</span>
                <span>{userForm.phone}</span>
              </div>
            </CardContent>
            <CardFooter>
              <Button
                variant="outline"
                className="w-full"
                onClick={() => setIsEditing(true)}
                disabled={isEditing}
              >
                Editar Perfil
              </Button>
            </CardFooter>
          </Card>
          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle>Informações Pessoais</CardTitle>
              <CardDescription>
                Atualize suas informações pessoais aqui
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Nome Completo</Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  disabled={!isEditing}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  disabled={!isEditing}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone">Telefone</Label>
                <Input
                  id="phone"
                  name="phone"
                  value={maskPhone(formData.phone)}
                  onChange={handleInputChange}
                  disabled={!isEditing}
                />
              </div>
              <div className="grid gap-4 grid-cols-1 md:grid-cols-1">
                <div className="space-y-2">
                  <Label htmlFor="role">Cargo</Label>
                  <Input
                    id="role"
                    name="role"
                    value={formData.role}
                    onChange={handleInputChange}
                    disabled={true}
                  />
                </div>
              </div>
            </CardContent>
            <CardFooter className={isEditing ? "flex justify-end gap-2" : "hidden"}>
              <Button variant="outline" onClick={handleCancel}>Cancelar</Button>
              <Button onClick={handleSave}>Salvar Alterações</Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    </MainLayout>
  );
};

export default Profile;
