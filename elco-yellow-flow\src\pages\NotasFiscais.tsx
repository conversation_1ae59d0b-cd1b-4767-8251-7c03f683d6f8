import React, { useState, useEffect } from 'react';
import Layout from '@/layouts/Layout';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from '@/components/ui/table';
import StatusBadge from '@/components/ui/StatusBadge';
import { Download, Eye, Search, Filter, FileText, Send, RefreshCw, Loader2, CheckCircle2, Clock, AlertTriangle } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import ViewModal from '@/features/expeditions/components/ViewModal';
import StatCard from '@/components/ui/StatCard';
import { toast } from '@/components/ui/use-toast';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { api } from '@/lib/api';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { <PERSON><PERSON>, <PERSON>bsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import SmartPagination from '@/components/ui/SmartPagination';

// PDF + OCR -------------------------------------------------------------------
import * as pdfjsLib from 'pdfjs-dist/legacy/build/pdf';
import Tesseract from 'tesseract.js';

// garante que o worker do PDF.js seja encontrado pelo bundler
if (pdfjsLib.GlobalWorkerOptions) {
  pdfjsLib.GlobalWorkerOptions.workerSrc = new URL(
    'pdfjs-dist/legacy/build/pdf.worker.min.js',
    import.meta.url,
  ).toString();
}

// -----------------------------------------------------------------------------

interface Invoice {
  id: number;
  idOrdem: number;
  idRomaneio: number;
  notInNumero: number;
  notDtEmissao: string;
  notStCgc: string;
  notStChaveacesso?: string;
  items: InvoiceItem[];
  errors: InvoiceError[];
  xml?: string;
  status?: string;
  pdfBase64?: string;
}

interface InvoiceItem {
  id: number;
  itnStDescricao: string;
  itnReQuantidade: number;
  itnReValorunitario: number;
  itnReValortotal: number;
}

interface InvoiceError {
  id: number;
  errorMessage: string;
  data: string;
  xmlEnviado?: string;
  soapEnvelope?: string;
  idRomaneio: number;
  idOrdem: number;
}

const NotasFiscais: React.FC = () => {
  const [viewModalOpen, setViewModalOpen] = useState(false);
  const [selectedNota, setSelectedNota] = useState<Invoice | null>(null);
  const [notaPdfOpen, setNotaPdfOpen] = useState(false);
  const [notaPdfId, setNotaPdfId] = useState('');
  const [notaPdfBase64, setNotaPdfBase64] = useState<string | null>(null);
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [total, setTotal] = useState(0);
  const [search, setSearch] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [invoiceErrors, setInvoiceErrors] = useState<InvoiceError[]>([]);
  const [selectedError, setSelectedError] = useState<InvoiceError | null>(null);
  const [errorModalOpen, setErrorModalOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<string>('pendentes');
  const [updating, setUpdating] = useState(false);
  const [danfeNumber, setDanfeNumber] = useState<string | null>(null); // NOVO

  useEffect(() => {
    fetchInvoices();
    fetchInvoiceErrors();
  }, [page, search, statusFilter]);

  const extractDanfeNumber = async (base64: string): Promise<string | null> => {
    const binary = atob(base64);
    const len = binary.length;
    const uint8Array = new Uint8Array(len);
    for (let i = 0; i < len; i++) uint8Array[i] = binary.charCodeAt(i);

    const pdf = await pdfjsLib.getDocument({ data: uint8Array }).promise;
    const page = await pdf.getPage(1);

    const textContent = await page.getTextContent();
    const allText = textContent.items.map((i: any) => i.str).join(' ');
    let match = allText.match(/DANFE[\s\S]*?N[º°]?\s*(\d{1,})/i);
    if (match) return match[1];

    const viewport = page.getViewport({ scale: 2 }); 
    const canvas = document.createElement('canvas');
    canvas.width = viewport.width;
    canvas.height = viewport.height;

    await page.render({ canvasContext: canvas.getContext('2d')!, viewport }).promise;

    const crop = document.createElement('canvas');
    const [x, y, w, h] = [viewport.width * 0.6, 0, viewport.width * 0.4, viewport.height * 0.15];
    crop.width = w;
    crop.height = h;
    crop.getContext('2d')!.drawImage(canvas, x, y, w, h, 0, 0, w, h);

    const { data: { text } } = await Tesseract.recognize(crop, 'por');
    match = text.match(/\d{6,}/);
    return match ? match[0] : null;
  };

  const fetchInvoices = async () => {
    try {
      setLoading(true);
      const response = await api.get(`/invoices?page=${page}&limit=10`);
      setInvoices(response.data.invoices);
      setTotal(response.data.total);
    } catch (error) {
      toast.error('Erro ao carregar notas fiscais');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const fetchInvoiceErrors = async () => {
    try {
      const response = await api.get('/invoices/errors');
      setInvoiceErrors(response.data.errors);
    } catch (error) {
      toast.error('Erro ao carregar notas com erro');
      console.error(error);
    }
  };

  const handleViewNota = (nota: Invoice) => {
    setSelectedNota(nota);
    setViewModalOpen(true);
  };

  const handleAbrirNota = async (notaId: number) => {
    setNotaPdfId(notaId.toString());
    const nota = invoices.find((n) => n.id === notaId);
    const base64 = nota?.pdfBase64 || null;
    setNotaPdfBase64(base64);
    setNotaPdfOpen(true);
  };

  const handleBaixarXML = (notaId: number) => {
    const nota = invoices.find((n) => n.id === notaId);
    if (!nota || !nota.xml) {
      toast.error('XML não disponível para esta nota.');
      return;
    }
    const blob = new Blob([nota.xml], { type: 'application/xml' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `nota-fiscal-${nota.notInNumero}.xml`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
    toast.success(`XML da nota ${nota.notInNumero} baixado com sucesso!`);
  };

  const getStatus = (nota: Invoice): 'PENDING' | 'ERROR' | 'ISSUED' => {
    if (nota.errors && nota.errors.length > 0) {
      return 'ERROR';
    }
    if (nota.status === 'ISSUED') {
      return 'ISSUED';
    }
    return 'PENDING';
  };

  const completedCount = invoices.filter((n) => getStatus(n) === 'ISSUED').length;
  const pendingCount = invoices.filter((n) => getStatus(n) === 'PENDING').length;
  const errorCount = invoices.filter((n) => getStatus(n) === 'ERROR').length;

  const formatDate = (date: string) => {
    return format(new Date(date), 'dd/MM/yyyy', { locale: ptBR });
  };

  const handleViewError = (error: InvoiceError) => {
    setSelectedError(error);
    setErrorModalOpen(true);
  };

  const handleEnviarXML = async (error: InvoiceError) => {
    try {
      const response = await api.post('/mega/errors/send-xml', {
        errorId: error.id,
        xmlEnviado: error.xmlEnviado,
      });

      if (response.status === 200 || response.status === 201) {
        toast.success('XML enviado com sucesso!');
      } else {
        toast.error('Erro ao enviar XML');
      }
    } catch (error) {
      toast.error('Erro ao enviar XML');
    }
  };

  const handleUpdateInvoices = async () => {
    setUpdating(true);
    try {
      const pendingInvoices = invoices.filter(
        (n) => getStatus(n) === 'PENDING' && n.notStChaveacesso,
      );
      const accessKeys = pendingInvoices
        .map((n) => n.notStChaveacesso)
        .filter((key): key is string => !!key);

      if (accessKeys.length === 0) {
        toast({
          title: 'Nenhuma nota fiscal pendente com chave de acesso para atualizar.',
        });
        setUpdating(false);
        return;
      }

      await api.post('/mega/consultar-xmls', { chavesAcesso: accessKeys });

      toast.success('Verificação de notas concluída. Atualizando a lista...');
      await fetchInvoices();
      await fetchInvoiceErrors();
    } catch (error) {
      toast.error('Erro ao verificar notas fiscais.');
      console.error(error);
    } finally {
      setUpdating(false);
    }
  };

  return (
    <Layout title="Notas Fiscais">
      <div className="mb-8 grid grid-cols-1 gap-6 md:grid-cols-3">
        <StatCard title="Emitidas" value={completedCount} variant="green" icon={<FileText className="h-5 w-5" />} />
        <StatCard title="Pendentes" value={pendingCount} variant="amber" icon={<FileText className="h-5 w-5 text-amber-500" />} />
        <StatCard title="Com Erros" value={invoiceErrors.length} variant="red" icon={<FileText className="h-5 w-5" />} />
      </div>

      <div className="mb-6 backdrop-blur-sm rounded-xl bg-white/80 border border-gray-100 shadow-sm p-4">
        <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
          <div className="flex flex-col space-y-2 md:flex-row md:space-x-2 md:space-y-0">
            <div className="relative w-full max-w-sm">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input 
                type="text" 
                placeholder="Buscar notas..." 
                className="pl-9"
                value={search}
                onChange={(e) => setSearch(e.target.value)}
              />
            </div>
          </div>

          <div className="flex space-x-2">
            <Button variant="outline" className="gap-2 bg-white" onClick={handleUpdateInvoices} disabled={updating}>
              {updating ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Atualizando...
                </>
              ) : (
                <>
                  <RefreshCw className="h-4 w-4" />
                  Atualizar
                </>
              )}
            </Button>
          </div>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <div className="mb-4 flex gap-2 justify-center">
          <button
            className={`flex items-center gap-2 px-6 py-2 rounded-lg font-medium transition-all duration-200 border shadow-sm focus:outline-none
              ${activeTab === 'emitidas' ? 'bg-green-100 text-green-700 border-green-300 shadow-green-200 scale-105' : 'bg-white text-gray-700 border-gray-200 hover:bg-green-50'}
            `}
            onClick={() => setActiveTab('emitidas')}
            type="button"
          >
            <CheckCircle2 className="h-4 w-4" /> Emitidas
          </button>
          <button
            className={`flex items-center gap-2 px-6 py-2 rounded-lg font-medium transition-all duration-200 border shadow-sm focus:outline-none
              ${activeTab === 'pendentes' ? 'bg-yellow-100 text-yellow-700 border-yellow-300 shadow-yellow-200 scale-105' : 'bg-white text-gray-700 border-gray-200 hover:bg-yellow-50'}
            `}
            onClick={() => setActiveTab('pendentes')}
            type="button"
          >
            <Clock className="h-4 w-4" /> Pendentes
          </button>
          <button
            className={`flex items-center gap-2 px-6 py-2 rounded-lg font-medium transition-all duration-200 border shadow-sm focus:outline-none
              ${activeTab === 'erros' ? 'bg-red-100 text-red-700 border-red-300 shadow-red-200 scale-105' : 'bg-white text-gray-700 border-gray-200 hover:bg-red-50'}
            `}
            onClick={() => setActiveTab('erros')}
            type="button"
          >
            <AlertTriangle className="h-4 w-4" /> Com Erros
          </button>
        </div>
        <TabsContent value="emitidas">
          <div className="rounded-xl overflow-hidden border-0 shadow-md mb-8">
            <Table>
              <TableHeader className="bg-gray-50">
                <TableRow>
                  <TableHead className="w-[120px] font-medium">Nota #</TableHead>
                  <TableHead className="font-medium">CNPJ</TableHead>
                  <TableHead className="font-medium">Data</TableHead>
                  <TableHead className="font-medium">Status</TableHead>
                  <TableHead className="font-medium">Nº Romaneio</TableHead>
                  <TableHead className="text-right font-medium">Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-4">
                      Carregando...
                    </TableCell>
                  </TableRow>
                ) : invoices.filter(n => getStatus(n) === 'ISSUED').length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-4">
                      Nenhuma nota fiscal emitida encontrada
                    </TableCell>
                  </TableRow>
                ) : (
                  invoices.filter(n => getStatus(n) === 'ISSUED').map((nota) => (
                    <TableRow key={nota.id} className="border-b hover:bg-gray-50/80 transition-colors">
                      <TableCell className="font-medium">{nota.notInNumero}</TableCell>
                      <TableCell className="font-medium text-gray-900">{nota.notStCgc}</TableCell>
                      <TableCell>{formatDate(nota.notDtEmissao)}</TableCell>
                      <TableCell>
                        <StatusBadge status={getStatus(nota)} />
                      </TableCell>
                      <TableCell>{nota.idRomaneio}</TableCell>
                      <TableCell className="text-right space-x-1">
                        <Button variant="ghost" size="icon" onClick={() => handleViewNota(nota)} title="Visualizar">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon" onClick={() => handleAbrirNota(nota.id)} title="Abrir Nota PDF">
                          <FileText className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon" onClick={() => handleBaixarXML(nota.id)} title="Baixar XML">
                          <Download className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
            <div className="flex justify-center my-4">
              <SmartPagination
                totalPages={Math.ceil(total / 10) || 1}
                currentPage={page}
                onPageChange={setPage}
              />
            </div>
          </div>
        </TabsContent>
        <TabsContent value="pendentes">
          <div className="rounded-xl overflow-hidden border-0 shadow-md mb-8">
            <Table>
              <TableHeader className="bg-yellow-50">
                <TableRow>
                  <TableHead className="w-[120px] font-medium">Nota #</TableHead>
                  <TableHead className="font-medium">CNPJ</TableHead>
                  <TableHead className="font-medium">Data</TableHead>
                  <TableHead className="font-medium">Status</TableHead>
                  <TableHead className="font-medium">Nº Romaneio</TableHead>
                  <TableHead className="text-right font-medium">Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-4">
                      Carregando...
                    </TableCell>
                  </TableRow>
                ) : invoices.filter(n => getStatus(n) === 'PENDING').length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-4">
                      Nenhuma nota fiscal pendente encontrada
                    </TableCell>
                  </TableRow>
                ) : (
                  invoices.filter(n => getStatus(n) === 'PENDING').map((nota) => (
                    <TableRow key={nota.id} className="border-b hover:bg-yellow-50/80 transition-colors">
                      <TableCell className="font-medium">{nota.notInNumero}</TableCell>
                      <TableCell className="font-medium text-gray-900">{nota.notStCgc}</TableCell>
                      <TableCell>{formatDate(nota.notDtEmissao)}</TableCell>
                      <TableCell>
                        <StatusBadge status={getStatus(nota)} />
                      </TableCell>
                      <TableCell>{nota.idRomaneio}</TableCell>
                      <TableCell className="text-right space-x-1">
                        <Button variant="ghost" size="icon" onClick={() => handleViewNota(nota)} title="Visualizar">
                          <Eye className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
            <div className="flex justify-center my-4">
              <SmartPagination
                totalPages={Math.ceil(total / 10) || 1}
                currentPage={page}
                onPageChange={setPage}
              />
            </div>
          </div>
        </TabsContent>
        <TabsContent value="erros">
          <div className="rounded-xl overflow-hidden border-0 shadow-md mb-8">
            <Table>
              <TableHeader className="bg-red-50">
                <TableRow>
                  <TableHead className="w-[120px] font-medium">ID Erro</TableHead>
                  <TableHead className="font-medium">Mensagem</TableHead>
                  <TableHead className="font-medium">Data</TableHead>
                  <TableHead className="font-medium">Romaneio</TableHead>
                  <TableHead className="font-medium">Ordem</TableHead>
                  <TableHead className="text-right font-medium">Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {invoiceErrors.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-4">
                      Nenhuma nota com erro encontrada
                    </TableCell>
                  </TableRow>
                ) : (
                  invoiceErrors.map((err) => (
                    <TableRow key={err.id} className="border-b hover:bg-red-50/80 transition-colors">
                      <TableCell className="font-medium">{err.id}</TableCell>
                      <TableCell className="font-medium text-red-700">{err.errorMessage}</TableCell>
                      <TableCell>{formatDate(err.data)}</TableCell>
                      <TableCell>{err.idRomaneio}</TableCell>
                      <TableCell>{err.idOrdem}</TableCell>
                      <TableCell className="text-right space-x-1">
                        <Button variant="ghost" size="icon" onClick={() => handleViewError(err)} title="Visualizar Erro">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          onClick={() => handleEnviarXML(err)} 
                          title="Enviar XML"
                          className="text-blue-600 hover:text-blue-700"
                        >
                          <Send className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
            <div className="flex justify-center my-4">
              <SmartPagination
                totalPages={Math.ceil(total / 10) || 1}
                currentPage={page}
                onPageChange={setPage}
              />
            </div>
          </div>
        </TabsContent>
      </Tabs>

      <ViewModal
        isOpen={viewModalOpen}
        onClose={() => setViewModalOpen(false)}
        title={`Nota Fiscal ${selectedNota?.notInNumero}`}
        data={selectedNota}
        type="invoice"
      />

      <Dialog open={notaPdfOpen} onOpenChange={(open) => {
        setNotaPdfOpen(open);
        if (!open) setNotaPdfBase64(null);
      }}>
        <DialogContent className="w-full max-w-6xl h-[90vh] max-h-[95vh] bg-white p-0 flex flex-col overflow-hidden">
          <DialogHeader className="p-4 border-b">
            <DialogTitle className="text-lg font-bold">Nota Fiscal {notaPdfId}</DialogTitle>
          </DialogHeader>
          <div className="flex-1">
            <iframe
              src={notaPdfBase64 ? `data:application/pdf;base64,${notaPdfBase64}` : ''}
              className="w-full h-[80vh]"
              title={`Nota Fiscal ${notaPdfId}`}
            />
          </div>
        </DialogContent>
      </Dialog>

      <Dialog open={errorModalOpen} onOpenChange={setErrorModalOpen}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle>Detalhes do Erro</DialogTitle>
          </DialogHeader>
          {selectedError && (
            <div className="space-y-4">
              <div>
                <span className="font-semibold">Mensagem:</span>
                <div className="text-red-700 mt-1 whitespace-pre-line break-words">{selectedError.errorMessage}</div>
              </div>
              <div>
                <span className="font-semibold">Data:</span> {formatDate(selectedError.data)}
              </div>
              <div>
                <span className="font-semibold">Romaneio:</span> {selectedError.idRomaneio}
              </div>
              <div>
                <span className="font-semibold">Ordem:</span> {selectedError.idOrdem}
              </div>
              {selectedError.xmlEnviado && (
                <div>
                  <span className="font-semibold">XML Enviado:</span>
                  <div className="rounded bg-gray-100 border p-2 mt-1 max-h-40 overflow-auto text-xs">
                    <pre className="whitespace-pre-wrap break-all">{selectedError.xmlEnviado}</pre>
                  </div>
                </div>
              )}
              {selectedError.soapEnvelope && (
                <div>
                  <span className="font-semibold">SOAP Envelope:</span>
                  <div className="rounded bg-gray-100 border p-2 mt-1 max-h-40 overflow-auto text-xs">
                    <pre className="whitespace-pre-wrap break-all">{selectedError.soapEnvelope}</pre>
                  </div>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </Layout>
  );
};

export default NotasFiscais;
