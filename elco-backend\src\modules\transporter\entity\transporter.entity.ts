import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

@Entity('transporters')
export class Transporter {
  @PrimaryGeneratedColumn()
  id: string;

  @Column()
  name: string;

  @Column()
  cnpj: string;

  @Column()
  phone: string;

  @Column()
  department: string;

  @Column({ nullable: true })
  zipCode: string;

  @Column()
  city: string;

  @Column()
  state: string;

  @Column({ nullable: true })
  erpCode: string;
}
