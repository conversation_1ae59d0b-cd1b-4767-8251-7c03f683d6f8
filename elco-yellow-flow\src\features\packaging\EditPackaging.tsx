import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useNavigate, useParams } from 'react-router-dom';
import MainLayout from '@/layouts/MainLayout';
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { api } from '@/lib/api';
import { Edit, ArrowLeft, Package } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

const embalagemTipos = [
  '<PERSON><PERSON>',
  '<PERSON><PERSON><PERSON><PERSON>',
  '<PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>',
  'Caixa Simples',
  '<PERSON><PERSON><PERSON>',
  'Engradado',
  'Estr. Met',
  'Outros',
] as const;

const formSchema = z.object({
  name: z.string().min(2, 'Nome obrigatório'),
  weight: z.coerce.number().min(0.1, 'Peso mínimo é 0.1kg'),
  height: z.coerce.number().min(0.1, 'Altura mínima é 0.1cm'),
  length: z.coerce.number().min(0.1, 'Comprimento mínimo é 0.1cm'),
  width: z.coerce.number().min(0.1, 'Largura mínima é 0.1cm'),
  type: z.enum(embalagemTipos, { errorMap: () => ({ message: 'Selecione um tipo de embalagem' }) }),
  volume: z.coerce.number().min(0, 'Volume mínimo é 0m³'),
});

type FormValues = z.infer<typeof formSchema>;

const EditPackaging = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      weight: 0,
      height: 0,
      length: 0,
      width: 0,
      type: embalagemTipos[0],
      volume: 0,
    },
  });

  useEffect(() => {
    const length = form.watch('length');
    const width = form.watch('width');
    const height = form.watch('height');
    if (length > 0 && width > 0 && height > 0) {
      const calculatedVolume = (length * width * height) / 10000000;
      form.setValue('volume', parseFloat(calculatedVolume.toFixed(6)));
    }
  }, [form.watch('length'), form.watch('width'), form.watch('height'), form]);

  useEffect(() => {
    const fetchPackaging = async () => {
      try {
        const response = await api.get(`/packaging/view/${id}`);
        form.reset({
          name: response.data.name || '',
          weight: response.data.weight || 0,
          height: response.data.height || 0,
          length: response.data.length || 0,
          width: response.data.width || 0,
          type: response.data.type || embalagemTipos[0],
          volume: response.data.volume || 0,
        });
      } catch (error) {
        toast.error('Erro ao carregar embalagem');
        navigate('/packaging');
      } finally {
        setIsLoading(false);
      }
    };
  
    if (id) fetchPackaging();
  }, [id, form, navigate]);
  

  const onSubmit = async (data: FormValues) => {
    try {
      await api.put(`/packaging/update/${id}`, data);
      toast.success('Embalagem atualizada com sucesso!');
      navigate('/packaging');
    } catch (error) {
      toast.error('Erro ao atualizar embalagem');
    }
  };

  const handleBack = () => navigate('/packaging');

  return (
    <MainLayout title="Editar Embalagem">
      <div className="max-w-3xl mx-auto">
        <Button variant="ghost" className="mb-4" onClick={handleBack}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Voltar para Lista
        </Button>

        <Card>
          <CardHeader>
            <div className="flex items-center gap-2">
              <Package className="h-6 w-6 text-primary" />
              <CardTitle>Editar Embalagem</CardTitle>
            </div>
            <CardDescription>Atualize os dados da embalagem</CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary" />
              </div>
            ) : (
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Nome</FormLabel>
                        <FormControl>
                          <Input placeholder="Digite o nome da embalagem" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tipo de Embalagem</FormLabel>
                        <FormControl>
                          <Select
                            onValueChange={field.onChange}
                            value={field.value}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Selecione o tipo" />
                            </SelectTrigger>
                            <SelectContent>
                              {embalagemTipos.map((tipo) => (
                                <SelectItem key={tipo} value={tipo}>
                                  {tipo}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="weight"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Peso (kg)</FormLabel>
                        <FormControl>
                          <Input type="number" step="0.01" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="height"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Altura (cm)</FormLabel>
                        <FormControl>
                          <Input type="number" step="0.01" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="length"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Comprimento (cm)</FormLabel>
                        <FormControl>
                          <Input type="number" step="0.01" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="width"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Largura (cm)</FormLabel>
                        <FormControl>
                          <Input type="number" step="0.01" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="volume"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Volume (m³) - Calculado automaticamente</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.000001"
                            readOnly
                            className="bg-muted"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="pt-4 flex justify-end space-x-4">
                    <Button variant="outline" onClick={handleBack}>
                      Cancelar
                    </Button>
                    <Button type="submit">
                      <Edit className="mr-2 h-4 w-4" />
                      Salvar Alterações
                    </Button>
                  </div>
                </form>
              </Form>
            )}
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};

export default EditPackaging;
