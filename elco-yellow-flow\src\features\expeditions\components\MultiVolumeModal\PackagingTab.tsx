import React, { useRef, useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Loader2, Package, Database, Box, Plus, HelpCircle, CheckCircle2, ArrowRight, Copy, Scissors } from 'lucide-react';
import { SecondaryPackaging } from '@/hooks/useMultiVolumeModal';
import { toast } from '@/components/ui/use-toast';

interface PackagingTabProps {
  combinedOrders: any[];
  productsByOrder: Record<string, any[]>;
  loadingOrders: Record<string, boolean>;
  packagingList: { value: string; label: string }[];
  packagingSelections: Record<string, Record<string, string>>;
  setPackagingSelections: React.Dispatch<React.SetStateAction<Record<string, Record<string, string>>>>;
  secondaryPackagings: SecondaryPackaging[];
  setSecondaryPackagings: React.Dispatch<React.SetStateAction<SecondaryPackaging[]>>;
  productWeights: Record<string, Record<string, string>>;
  setProductWeights: React.Dispatch<React.SetStateAction<Record<string, Record<string, string>>>>;
  invalidSecondaryVolumes: string[];
  setInvalidSecondaryVolumes: React.Dispatch<React.SetStateAction<string[]>>;
  selectedProducts: Record<string, Set<string>>;
  setSelectedProducts: React.Dispatch<React.SetStateAction<Record<string, Set<string>>>>;
  bulkPackagingSelections: Record<string, string>;
  setBulkPackagingSelections: React.Dispatch<React.SetStateAction<Record<string, string>>>;
  appliedFeedback: Record<string, Record<string, boolean>>;
  setAppliedFeedback: React.Dispatch<React.SetStateAction<Record<string, Record<string, boolean>>>>;
  loadProducts: (order: any) => Promise<void>;
  handlePackagingChange: (orderCode: string, productCode: string, packagingId: string) => void;
  handleSecondaryVolumeChange: (orderCode: string, productCode: string, value: string, orderNumberOfUnitVolumes: number) => void;
  handleProductWeightChange: (orderCode: string, productCode: string, value: string) => void;
  duplicateProduct: (orderCode: string, productCode: string, divisions: number) => void;
  splitProduct: (orderCode: string, productCode: string, quantities: number[]) => void;
  setIsAddPackagingOpen: (open: boolean) => void;
  setActiveTab: (tab: string) => void;
  handleSaveDraft: () => void;
  isSubmitting: boolean;
  isUnlinkedFromDraft: boolean;
}

const PackagingTab: React.FC<PackagingTabProps> = ({
  combinedOrders,
  productsByOrder,
  loadingOrders,
  packagingList,
  packagingSelections,
  setPackagingSelections,
  secondaryPackagings,
  setSecondaryPackagings,
  productWeights,
  setProductWeights,
  invalidSecondaryVolumes,
  setInvalidSecondaryVolumes,
  selectedProducts,
  setSelectedProducts,
  bulkPackagingSelections,
  setBulkPackagingSelections,
  appliedFeedback,
  setAppliedFeedback,
  loadProducts,
  handlePackagingChange,
  handleSecondaryVolumeChange,
  handleProductWeightChange,
  duplicateProduct,
  splitProduct,
  setIsAddPackagingOpen,
  setActiveTab,
  handleSaveDraft,
  isSubmitting,
  isUnlinkedFromDraft,
}) => {
  const selectAllRef = useRef<HTMLInputElement>(null);
  const [splitModalOpen, setSplitModalOpen] = useState(false);
  const [selectedProductForSplit, setSelectedProductForSplit] = useState<{
    orderCode: string;
    productCode: string;
    productName: string;
    quantity: number;
  } | null>(null);
  const [splitQuantities, setSplitQuantities] = useState<number[]>([1, 1]);
  const [splitCount, setSplitCount] = useState(2);

  useEffect(() => {
    if (selectAllRef.current) {
      selectAllRef.current.indeterminate = combinedOrders.some(order => {
        const orderCode = order.internalCode;
        const products = productsByOrder[orderCode] || [];
        const selectedSet = selectedProducts[orderCode] || new Set();
        return selectedSet.size > 0 && selectedSet.size < products.length;
      });
    }
  }, [combinedOrders, selectedProducts, productsByOrder]);

  const handleBulkPackagingChange = (orderCode: string, packagingId: string) => {
    setBulkPackagingSelections(prev => ({
      ...prev,
      [orderCode]: packagingId,
    }));
  };

  const handleProductCheckboxChange = (orderCode: string, productCode: string, checked: boolean) => {
    setSelectedProducts(prev => {
      const current = new Set(prev[orderCode] || []);
      if (checked) {
        current.add(productCode);
      } else {
        current.delete(productCode);
      }
      return { ...prev, [orderCode]: current };
    });
  };

  const handleApplyBulkPackaging = (orderCode: string) => {
    const packagingId = bulkPackagingSelections[orderCode];
    const products = productsByOrder[orderCode] || [];
    const selected = selectedProducts[orderCode] || new Set();
    
    setPackagingSelections(prev => {
      const updated = { ...prev };
      if (!updated[orderCode]) updated[orderCode] = {};
      products.forEach(product => {
        if (selected.has(product.code)) {
          updated[orderCode][product.code] = packagingId;
        }
      });
      return updated;
    });

    setAppliedFeedback(prev => {
      const updated = { ...prev };
      if (!updated[orderCode]) updated[orderCode] = {};
      products.forEach(product => {
        if (selected.has(product.code)) {
          updated[orderCode][product.code] = true;
        }
      });
      return updated;
    });

    setTimeout(() => {
      setAppliedFeedback(prev => {
        const updated = { ...prev };
        if (updated[orderCode]) {
          products.forEach(product => {
            if (selected.has(product.code)) {
              updated[orderCode][product.code] = false;
            }
          });
        }
        return updated;
      });
    }, 1500);
  };

  const handleSplitProduct = (orderCode: string, productCode: string, productName: string, quantity: number) => {
    setSelectedProductForSplit({
      orderCode,
      productCode,
      productName,
      quantity
    });
    setSplitModalOpen(true);
    // Inicializar com divisão igual
    const initialQuantities = Array(splitCount).fill(Math.floor(quantity / splitCount));
    // Ajustar a última divisão para garantir que a soma seja exata
    const totalInitial = initialQuantities.reduce((sum, qty) => sum + qty, 0);
    if (totalInitial < quantity) {
      initialQuantities[initialQuantities.length - 1] += quantity - totalInitial;
    }
    setSplitQuantities(initialQuantities);
  };

  const handleSplitCountChange = (newCount: number) => {
    setSplitCount(newCount);
    const originalQuantity = selectedProductForSplit?.quantity || 0;
    const newQuantities = Array(newCount).fill(Math.floor(originalQuantity / newCount));
    const totalInitial = newQuantities.reduce((sum, qty) => sum + qty, 0);
    if (totalInitial < originalQuantity) {
      newQuantities[newQuantities.length - 1] += originalQuantity - totalInitial;
    }
    setSplitQuantities(newQuantities);
  };

  const handleQuantityChange = (index: number, value: number) => {
    const newQuantities = [...splitQuantities];
    newQuantities[index] = value;
    setSplitQuantities(newQuantities);
  };

  const confirmSplit = () => {
    if (selectedProductForSplit) {
      const originalQuantity = selectedProductForSplit.quantity;
      const totalSplitQuantity = splitQuantities.reduce((sum, qty) => sum + qty, 0);
      
      if (totalSplitQuantity !== originalQuantity) {
        toast.error(`A soma das quantidades (${totalSplitQuantity}) deve ser igual à quantidade original (${originalQuantity})`);
        return;
      }

      splitProduct(
        selectedProductForSplit.orderCode,
        selectedProductForSplit.productCode,
        splitQuantities
      );
      setSplitModalOpen(false);
      setSelectedProductForSplit(null);
      setSplitCount(2);
      setSplitQuantities([1, 1]);
    }
  };

  const isSecondaryVolumeValid = (numerator: string, orderVolume: number) => {
    if (!numerator) return false;
    const num = Number(numerator);
    if (isNaN(num) || !Number.isInteger(num) || num < 1) return false;
    if (orderVolume === 1 && num > 1) return false;
    if (orderVolume > 1 && num > orderVolume) return false;
    return true;
  };

  const handleSecondaryVolumeChangeWithValidation = (
    orderCode: string,
    productCode: string,
    value: string,
    orderNumberOfUnitVolumes: number
  ) => {
    if (!isSecondaryVolumeValid(value, orderNumberOfUnitVolumes)) {
      setInvalidSecondaryVolumes(prev => ([...new Set([...prev, `${orderCode}-${productCode}`])]));
    } else {
      setInvalidSecondaryVolumes(prev => prev.filter(v => v !== `${orderCode}-${productCode}`));
    }

    const currentPackagingId = packagingSelections[orderCode]?.[productCode] ?? '';

    setSecondaryPackagings(prev => {
      const filtered = prev.filter(
        sp => !(sp.orderCode === orderCode && sp.productCode === productCode)
      );
      return [
        ...filtered,
        {
          orderCode,
          productCode,
          packagingId: currentPackagingId,
          volumeFormat: `${value}/${orderNumberOfUnitVolumes}`
        }
      ];
    });
  };

  return (
    <div className="space-y-4 mt-2">
      <div className="max-h-[60vh] overflow-y-auto pr-2">
        <div className="flex justify-end mb-2">
          <Button
            type="button"
            variant="outline"
            size="sm"
            className="gap-1"
            onClick={() => setIsAddPackagingOpen(true)}
          >
            <Plus className="h-4 w-4" />
            Nova Embalagem
          </Button>
        </div>
        
        <Accordion type="multiple" defaultValue={[]}>
          {combinedOrders.map((order, index) => {
            const orderCode = order.internalCode;
            const products = productsByOrder[orderCode] || [];
            const orderNumberOfUnitVolumes = Number(order.volume) || 1;
            const selectedSet = selectedProducts[orderCode] || new Set();
            const feedback = appliedFeedback[orderCode] || {};

            return (
              <AccordionItem
                value={`pack-item-${index}`}
                key={orderCode}
                className="border rounded-lg mb-3 bg-white"
              >
                <AccordionTrigger
                  className="px-4 hover:bg-gray-50 hover:no-underline"
                  onClick={() => loadProducts(order)}
                >
                  <div className="flex flex-1 justify-between items-center">
                    <div className="flex items-center gap-2">
                      <Box className="h-5 w-5 text-primary" />
                      <span className="font-semibold">Pedido #{orderCode}</span>
                    </div>
                    <span className="text-sm text-muted-foreground">
                      {order.cliente ?? order.externalCode}
                    </span>
                  </div>
                </AccordionTrigger>
                
                <AccordionContent className="px-4 pt-2 pb-4 space-y-4">
                  <div className="flex items-center gap-2 p-2 bg-white rounded shadow-sm mb-4 border border-gray-100">
                    <span className="text-sm font-medium">Embalagem para selecionados:</span>
                    <Select
                      value={bulkPackagingSelections[orderCode] || ''}
                      onValueChange={v => handleBulkPackagingChange(orderCode, v)}
                    >
                      <SelectTrigger className="h-8 w-44 text-xs border-gray-300">
                        <SelectValue placeholder="Selecione" />
                      </SelectTrigger>
                      <SelectContent>
                        {packagingList.map((opt) => (
                          <SelectItem key={opt.value} value={opt.value} className="text-xs">
                            {opt.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <Button
                      type="button"
                      size="sm"
                      className="bg-yellow-500 hover:bg-yellow-600 text-white px-3 py-1 rounded ml-1 flex items-center gap-1 shadow-none"
                      onClick={() => handleApplyBulkPackaging(orderCode)}
                      disabled={!bulkPackagingSelections[orderCode] || selectedSet.size === 0}
                      title="Aplicar embalagem"
                    >
                      <Package className="w-4 h-4 mr-1" />
                      Aplicar
                    </Button>
                    <Tooltip delayDuration={0}>
                      <TooltipTrigger asChild>
                        <HelpCircle className="w-4 h-4 text-gray-400 cursor-pointer ml-1" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <span>Selecione os produtos, escolha uma embalagem e clique para aplicar.</span>
                      </TooltipContent>
                    </Tooltip>
                  </div>

                  <div className="grid grid-cols-[36px,1.5fr,1.2fr,0.7fr,1fr,auto] gap-4 px-2 pb-1 text-xs font-semibold text-gray-500 border-b border-gray-200">
                    <input
                      type="checkbox"
                      className="form-checkbox h-4 w-4 rounded-full text-blue-600 mx-auto border-gray-300"
                      checked={products.length > 0 && products.every(p => selectedSet.has(p.code))}
                      ref={selectAllRef}
                      onChange={e => {
                        const checked = e.target.checked;
                        setSelectedProducts(prev => ({
                          ...prev,
                          [orderCode]: checked ? new Set(products.map(p => p.code)) : new Set(),
                        }));
                      }}
                      aria-label="Selecionar todos"
                    />
                    <span>Produto</span>
                    <span>Embalagem</span>
                    <span>Volume</span>
                    <span>Peso</span>
                    <span>Ações</span>
                  </div>

                  <div className="divide-y divide-gray-100">
                    {loadingOrders[orderCode] ? (
                      <div className="flex justify-center py-4">
                        <Loader2 className="animate-spin h-6 w-6 text-gray-500" />
                      </div>
                    ) : productsByOrder[orderCode]?.length ? (
                      productsByOrder[orderCode].map((product: any) => {
                        const secondaryPack = secondaryPackagings.find(
                          sp => sp.orderCode === orderCode && sp.productCode === product.code
                        );
                        const productWeightValue = productWeights[orderCode]?.[product.code] || '';
                        const numerator = secondaryPack?.volumeFormat?.split('/')[0] || '';
                        const individualSelected = packagingSelections[orderCode]?.[product.code];
                        const effectivePackaging = individualSelected || '';
                        const checked = selectedSet.has(product.code);
                        const showFeedback = feedback[product.code];
                        const isSplit = product.isSplit;
                        const isDuplicate = product.isDuplicate;

                        return (
                          <div
                            key={product.id}
                            className="grid grid-cols-[36px,1.5fr,1.2fr,0.7fr,1fr,auto] gap-4 items-center px-2 py-2 bg-white rounded border border-gray-100 shadow-sm my-1"
                          >
                            <input
                              type="checkbox"
                              checked={checked}
                              onChange={e => handleProductCheckboxChange(orderCode, product.code, e.target.checked)}
                              className="form-checkbox h-4 w-4 rounded-full text-blue-600 mx-auto border-gray-300"
                            />
                            <span className="truncate text-sm font-medium">
                              {product.name}
                              <span className="ml-2 inline-flex items-center gap-1 rounded bg-gray-100 px-2 py-0.5 text-xs font-semibold text-gray-700">
                                <Database className="w-3 h-3" />
                                {product.quantity}
                              </span>
                              {isSplit && (
                                <span className="ml-2 inline-flex items-center gap-1 rounded bg-green-100 px-2 py-0.5 text-xs font-semibold text-green-700">
                                  <Scissors className="w-3 h-3" />
                                  {product.splitIndex}/{product.totalSplits}
                                </span>
                              )}
                              {isDuplicate && (
                                <span className="ml-2 inline-flex items-center gap-1 rounded bg-blue-100 px-2 py-0.5 text-xs font-semibold text-blue-700">
                                  <Copy className="w-3 h-3" />
                                  {product.duplicateIndex}/{product.totalDivisions}
                                </span>
                              )}
                            </span>
                            <div className="w-full flex items-center gap-1">
                              <Select
                                value={individualSelected || ''}
                                onValueChange={v => handlePackagingChange(orderCode, product.code, v)}
                              >
                                <SelectTrigger className="h-8 w-full text-xs text-left border-gray-300">
                                  <SelectValue
                                    placeholder="Selecione"
                                    className="text-muted-foreground text-xs truncate text-left"
                                  />
                                </SelectTrigger>
                                <SelectContent className="w-full">
                                  {packagingList.map((opt) => (
                                    <SelectItem
                                      key={opt.value}
                                      value={opt.value}
                                      className="text-xs"
                                    >
                                      {opt.label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              {showFeedback && (
                                <CheckCircle2 className="w-4 h-4 text-yellow-500 ml-1 animate-fade-in" />
                              )}
                            </div>
                            <div className="flex items-center justify-end gap-1">
                              <Input
                                type="number"
                                min="1"
                                step="1"
                                value={numerator}
                                onChange={(e) => handleSecondaryVolumeChangeWithValidation(
                                  orderCode,
                                  product.code,
                                  e.target.value,
                                  orderNumberOfUnitVolumes
                                )}
                                disabled={!effectivePackaging}
                                className={`w-14 h-8 rounded-md border ${invalidSecondaryVolumes.includes(`${orderCode}-${product.code}`) ? 'border-red-500' : 'border-gray-300'} bg-background px-2 py-1 text-xs text-right`}
                                placeholder="X"
                              />
                              <span className="text-xs">/ {orderNumberOfUnitVolumes}</span>
                            </div>
                            <div className="flex items-center justify-end">
                              <Input
                                type="number"
                                min="0"
                                step="0.01"
                                className="w-20 h-8 rounded-md border border-gray-300 bg-background px-2 py-1 text-xs text-right"
                                placeholder="Peso (kg)"
                                value={productWeightValue}
                                onChange={e => handleProductWeightChange(orderCode, product.code, e.target.value)}
                              />
                            </div>
                            <div className="flex items-center justify-center">
                              {product.quantity > 1 && (
                                <Button
                                  type="button"
                                  size="sm"
                                  variant="outline"
                                  className="h-6 w-6 p-0"
                                  onClick={() => handleSplitProduct(orderCode, product.code, product.name, product.quantity)}
                                >
                                  <Scissors className="h-3 w-3" />
                                </Button>
                              )}
                            </div>
                          </div>
                        );
                      })
                    ) : (
                      <p className="text-sm text-muted-foreground">
                        Nenhum produto encontrado.
                      </p>
                    )}
                  </div>
                </AccordionContent>
              </AccordionItem>
            );
          })}
        </Accordion>
      </div>

      <div className="flex justify-between mt-4">
        <div>
          <Button variant="outline" onClick={() => setActiveTab('general')} className="gap-2">
            Voltar para Informações Gerais
          </Button>
        </div>
        <div className="flex gap-2">
          <Button
            className={`${isUnlinkedFromDraft ? 'bg-gray-400 hover:bg-gray-500' : 'bg-yellow-500 hover:bg-yellow-600'} text-white`}
            onClick={handleSaveDraft} 
            disabled={isSubmitting || isUnlinkedFromDraft}
            title={isUnlinkedFromDraft ? "Desative o modo 'Novo Romaneio' para salvar rascunho" : "Salvar dados como rascunho"}
          >
            Salvar Rascunho
          </Button>
          <Button
            variant="outline"
            onClick={() => setActiveTab('orders')}
            className="gap-2"
          >
            Avançar para Pedidos <ArrowRight className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Modal de Repartição */}
      <Dialog open={splitModalOpen} onOpenChange={setSplitModalOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Repartir Produto</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="product-name">Produto</Label>
              <Input
                id="product-name"
                value={selectedProductForSplit?.productName || ''}
                disabled
                className="bg-gray-50"
              />
            </div>
            <div>
              <Label htmlFor="product-quantity">Quantidade Original</Label>
              <Input
                id="product-quantity"
                value={selectedProductForSplit?.quantity || 0}
                disabled
                className="bg-gray-50"
              />
            </div>
            <div>
              <Label htmlFor="split-count">Número de Divisões</Label>
              <Input
                id="split-count"
                type="number"
                min="2"
                max="10"
                value={splitCount}
                onChange={(e) => handleSplitCountChange(Number(e.target.value))}
                placeholder="2"
              />
            </div>
            <div>
              <Label>Quantidades por Divisão</Label>
              <div className="space-y-2">
                {splitQuantities.map((quantity, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <Label className="text-xs w-8">Parte {index + 1}:</Label>
                    <Input
                      type="number"
                      min="1"
                      value={quantity}
                      onChange={(e) => handleQuantityChange(index, Number(e.target.value))}
                      className="w-20"
                    />
                  </div>
                ))}
              </div>
              <div className="mt-2 text-xs text-muted-foreground">
                Total: {splitQuantities.reduce((sum, qty) => sum + qty, 0)} / {selectedProductForSplit?.quantity || 0}
                {splitQuantities.reduce((sum, qty) => sum + qty, 0) !== (selectedProductForSplit?.quantity || 0) && (
                  <span className="text-red-500 ml-2">A soma deve ser igual à quantidade original</span>
                )}
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setSplitModalOpen(false)}>
              Cancelar
            </Button>
            <Button 
              onClick={confirmSplit}
              disabled={splitQuantities.reduce((sum, qty) => sum + qty, 0) !== (selectedProductForSplit?.quantity || 0)}
            >
              Repartir
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default PackagingTab; 