import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { toast } from '@/hooks/use-toast';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

// Rotas restritas que só podem ser acessadas por Administrador e Coordenador
const restrictedRoutes = [
  '/users',
  '/packaging',
  '/drivers',
  '/transporters',
  '/vehicles',
  '/product-weights',
  '/supplier-emails',
  '/addresses',
];

// Rotas que o Operador de Obra não pode acessar (todas exceto romaneios)
const operatorRestrictedRoutes = [
  '/pedidos-expedicao',
  '/notas-fiscais',
  '/notas-erros',
  '/profile',
  '/settings',
  '/users',
  '/packaging',
  '/drivers',
  '/transporters',
  '/vehicles',
  '/product-weights',
  '/supplier-emails',
  '/addresses',
  '/visualizar-xml',
];

// Perfis que podem acessar rotas restritas
const allowedProfiles = ['Administrador', 'Coordenador'];

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const token = localStorage.getItem('token') || sessionStorage.getItem('token');
  const location = useLocation();
  const { user } = useAuth();

  // Verificar se o token é válido
  if (!token || token.split('.').length !== 3) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Verificar se a rota atual é restrita para Operador de Obra
  const isOperatorRestrictedRoute = operatorRestrictedRoutes.some(route => 
    location.pathname.startsWith(route)
  );

  // Se o usuário é Operador de Obra e está tentando acessar uma rota restrita
  if (user?.role === 'Operador de Obra' && isOperatorRestrictedRoute) {
    toast({
      title: 'Acesso Negado',
      description: 'Operador de Obra só pode acessar a tela de Romaneios.',
      variant: 'destructive',
    });
    
    // Redirecionar para romaneios
    return <Navigate to="/romaneios" replace />;
  }

  // Verificar se a rota atual é restrita para outros perfis
  const isRestrictedRoute = restrictedRoutes.some(route => 
    location.pathname.startsWith(route)
  );

  // Se é uma rota restrita, verificar se o usuário tem permissão
  if (isRestrictedRoute && user?.role && !allowedProfiles.includes(user.role)) {
    // Mostrar notificação de acesso negado
    toast({
      title: 'Acesso Negado',
      description: 'Você não tem permissão para acessar esta página. Entre em contato com o administrador do sistema.',
      variant: 'destructive',
    });
    
    // Redirecionar para a página principal se não tiver permissão
    return <Navigate to="/pedidos-expedicao" replace />;
  }

  return <>{children}</>;
};
