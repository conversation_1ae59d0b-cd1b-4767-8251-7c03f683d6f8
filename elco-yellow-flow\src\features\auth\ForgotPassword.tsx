
import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { toast } from '@/components/ui/use-toast';
import { Mail, ArrowLeft, CheckCircle } from 'lucide-react';
import { Progress } from '@/components/ui/progress';

const formSchema = z.object({
  email: z.string().email({
    message: "Por favor, digite um e-mail válido",
  }),
});

const ForgotPassword = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [progress, setProgress] = useState(0);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
    },
  });

  const onSubmit = (values: z.infer<typeof formSchema>) => {
    setIsSubmitting(true);
    setProgress(0);
    
    // Simulate API call with progress
    const interval = setInterval(() => {
      setProgress((prev) => {
        if (prev >= 100) {
          clearInterval(interval);
          return 100;
        }
        return prev + 10;
      });
    }, 150);
    
    setTimeout(() => {
      clearInterval(interval);
      setProgress(100);
      console.log(values);
      toast.success('Email de recuperação enviado com sucesso!');
      setIsSubmitting(false);
      setIsSubmitted(true);
    }, 1500);
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center items-center p-4">
      <Card className="w-full max-w-md border-0 shadow-lg">
        <CardHeader className="space-y-1">
          <div className="flex justify-center mb-4">
            <div className="w-12 h-12 flex items-center justify-center bg-amber-100 rounded-full">
              <Mail className="h-6 w-6 text-amber-600" />
            </div>
          </div>
          <CardTitle className="text-2xl font-bold text-center">Recuperar senha</CardTitle>
          <CardDescription className="text-center">
            {!isSubmitted 
              ? "Digite seu e-mail e enviaremos um link para redefinir sua senha."
              : "Verifique seu e-mail para redefinir sua senha."
            }
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {!isSubmitted ? (
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input 
                          placeholder="<EMAIL>"
                          type="email"
                          {...field}
                          disabled={isSubmitting}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                {isSubmitting && (
                  <div className="mt-2">
                    <Progress value={progress} />
                  </div>
                )}
                
                <Button 
                  type="submit" 
                  className="w-full" 
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Enviando..." : "Enviar link de recuperação"}
                </Button>
              </form>
            </Form>
          ) : (
            <div className="text-center space-y-4">
              <div className="flex justify-center">
                <div className="w-16 h-16 flex items-center justify-center bg-green-100 rounded-full">
                  <CheckCircle className="h-8 w-8 text-green-600" />
                </div>
              </div>
              <div className="space-y-2">
                <h3 className="font-medium text-lg">Email enviado com sucesso!</h3>
                <p className="text-sm text-muted-foreground">
                  Enviamos um email para <span className="font-semibold">{form.getValues().email}</span> com instruções para redefinir sua senha.
                </p>
                <p className="text-sm text-muted-foreground">
                  Se você não receber o email em alguns minutos, verifique sua pasta de spam.
                </p>
              </div>
              <Button 
                variant="outline" 
                type="button" 
                className="mt-4"
                onClick={() => setIsSubmitted(false)}
              >
                Tentar com outro email
              </Button>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-center border-t pt-4">
          <Button variant="link" asChild>
            <Link to="/login" className="flex items-center text-sm">
              <ArrowLeft className="h-4 w-4 mr-1" /> Voltar para o login
            </Link>
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default ForgotPassword;
