import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import MainLayout from '@/layouts/MainLayout';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from '@/components/ui/table';
import { Edit, Package, Plus, Trash2 } from 'lucide-react';
import { api } from '@/lib/api';
import SmartPagination from '@/components/ui/SmartPagination';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from '@/components/ui/alert-dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from '@/components/ui/use-toast';

interface Packaging {
  id: string;
  name: string;
  weight: number;
  width: number;
  height: number;
  length: number;
  type: string;
}

const PackagingList = () => {
    const [packagings, setPackagings] = useState<Packaging[]>([]);
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage, setItemsPerPage] = useState(5);
    const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
    const [packagingToDelete, setPackagingToDelete] = useState<Packaging | null>(null);
    const navigate = useNavigate();
  
    useEffect(() => {
      const fetchData = async () => {
        const res = await api.get('/packaging/list');
        setPackagings(res.data);
      };
      fetchData();
    }, []);
  
    const handleDelete = async () => {
      if (!packagingToDelete) return;
      try {
        await api.delete(`/packaging/delete/${packagingToDelete.id}`);
        setPackagings((prev) => prev.filter((p) => p.id !== packagingToDelete.id));
        toast.success('Embalagem removida com sucesso!');
      } catch (error: any) {
        if (error.response && error.response.data && error.response.data.message) {
          toast.error(error.response.data.message);
        } else {
          toast.error('Erro ao remover embalagem');
        }
      } finally {
        setDeleteDialogOpen(false);
      }
    };
  
    const handleEdit = (id: string) => navigate(`/packaging/edit/${id}`);
    const handleCreate = () => navigate(`/packaging/create`);
  
    const totalPages = Math.ceil(packagings.length / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const currentItems = packagings.slice(startIndex, startIndex + itemsPerPage);
  
    return (
      <MainLayout title="Embalagens">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div className="flex items-center gap-2">
              <Package className="h-6 w-6 text-primary" />
              <CardTitle>Listagem de Embalagens</CardTitle>
            </div>
            <Button onClick={handleCreate}>
              <Plus className="mr-2 h-4 w-4" />
              Nova Embalagem
            </Button>
          </CardHeader>
  
          <CardContent>
            <div className="flex justify-end mb-4">
              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">Itens por página:</span>
                <Select value={String(itemsPerPage)} onValueChange={(v) => {
                  setItemsPerPage(Number(v));
                  setCurrentPage(1);
                }}>
                  <SelectTrigger className="w-[80px]">
                    <SelectValue placeholder="5" />
                  </SelectTrigger>
                  <SelectContent>
                    {[5, 10, 20, 50].map(num => (
                      <SelectItem key={num} value={String(num)}>{num}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
  
            <div className="border rounded-lg overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Nome</TableHead>
                    <TableHead>Peso (kg)</TableHead>
                    <TableHead>Altura (cm)</TableHead>
                    <TableHead>Comprimento (cm)</TableHead>
                    <TableHead>Largura (cm)</TableHead>
                    <TableHead>Tipo</TableHead>
                    <TableHead className="text-right">Ações</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {currentItems.length > 0 ? (
                    currentItems.map(p => (
                      <TableRow key={p.id}>
                        <TableCell className="font-medium">{p.name}</TableCell>
                        <TableCell>{p.weight}</TableCell>
                        <TableCell>{p.height}</TableCell>
                        <TableCell>{p.length}</TableCell>
                        <TableCell>{p.width}</TableCell>
                        <TableCell>{p.type}</TableCell>
                        <TableCell className="text-right space-x-2">
                          <Button variant="ghost" size="sm" onClick={() => handleEdit(p.id)}>
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-destructive hover:text-destructive hover:bg-destructive/10"
                            onClick={() => {
                              setPackagingToDelete(p);
                              setDeleteDialogOpen(true);
                            }}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-6 text-muted-foreground">
                        Nenhuma embalagem encontrada
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
  
            {packagings.length > 0 && (
              <div className="mt-4">
                <SmartPagination
                  totalPages={totalPages}
                  currentPage={currentPage}
                  onPageChange={setCurrentPage}
                />
              </div>
            )}
          </CardContent>
        </Card>
  
        <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Excluir Embalagem</AlertDialogTitle>
              <AlertDialogDescription>
                Deseja realmente excluir a embalagem <strong>{packagingToDelete?.name}</strong>?
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancelar</AlertDialogCancel>
              <AlertDialogAction onClick={handleDelete} className="bg-destructive text-white">
                Excluir
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </MainLayout>
    );
  };
  
  export default PackagingList;
  
