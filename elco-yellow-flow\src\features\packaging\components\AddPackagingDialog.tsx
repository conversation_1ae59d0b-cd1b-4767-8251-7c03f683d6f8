
import React, { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Loader2, Package } from 'lucide-react'
import { toast } from '@/hooks/use-toast'
import { api } from '@/lib/api'
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from '@/components/ui/select'

const embalagemTipos = [
  'Solto',
  'Berço',
  'Pacote',
  'Caixa',
  'Caixa Simples',
  'Pallet',
  'Engradado',
  'Estr. Met',
  'Outros',
] as const

interface AddPackagingDialogProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: (newPackaging: any) => void
}

interface PackagingFormData {
  name: string
  weight: number
  height: number
  length: number
  width: number
  type: string
  volume: number
}

const AddPackagingDialog: React.FC<AddPackagingDialogProps> = ({
  isOpen,
  onClose,
  onSuccess,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formData, setFormData] = useState<PackagingFormData>({
    name: '',
    weight: 0,
    height: 0,
    length: 0,
    width: 0,
    type: embalagemTipos[0],
    volume: 0,
  })

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    const numericFields = ['weight', 'height', 'length', 'width', 'volume']
    const newValue = numericFields.includes(name)
      ? parseFloat(value) || 0
      : value

    setFormData(prev => ({
      ...prev,
      [name]: newValue,
    }))
  }

  useEffect(() => {
    const { length, width, height } = formData
    if (length > 0 && width > 0 && height > 0) {
      const calculatedVolume = (length * width * height) / 10000000
      setFormData(prev => ({
        ...prev,
        volume: parseFloat(calculatedVolume.toFixed(6))
      }))
    }
  }, [formData.length, formData.width, formData.height])

  const handleSelectChange = (value: string) => {
    setFormData(prev => ({
      ...prev,
      type: value,
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.name || !formData.type) {
      toast.error('Nome e tipo de embalagem são obrigatórios.')
      return
    }

    setIsSubmitting(true)
    try {
      const { data } = await api.post('/packaging/create', formData)
      toast.success('Embalagem adicionada com sucesso.')
      onSuccess(data)
      onClose()
    } catch (err) {
      console.error(err)
      toast.error('Não foi possível adicionar a embalagem.')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px] p-4 md:p-6 mx-4 max-w-[95vw]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Nova Embalagem
          </DialogTitle>
          <DialogDescription>
            Preencha os detalhes da nova embalagem abaixo.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            {/* Nome e tipo */}
            <div className="grid grid-cols-1 gap-3">
              <div className="space-y-2">
                <Label htmlFor="name">Nome</Label>
                <Input
                  id="name"
                  name="name"
                  placeholder="Nome da embalagem"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="type">Tipo de Embalagem</Label>
                <Select
                  value={formData.type}
                  onValueChange={handleSelectChange}
                >
                  <SelectTrigger id="type">
                    <SelectValue placeholder="Selecione o tipo" />
                  </SelectTrigger>
                  <SelectContent>
                    {embalagemTipos.map((tipo) => (
                      <SelectItem key={tipo} value={tipo}>
                        {tipo}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Dimensões */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              <div className="space-y-2">
                <Label htmlFor="length">Comprimento (cm)</Label>
                <Input
                  id="length"
                  name="length"
                  type="number"
                  placeholder="0"
                  value={formData.length}
                  onChange={handleInputChange}
                  min="0"
                  step="0.01"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="width">Largura (cm)</Label>
                <Input
                  id="width"
                  name="width"
                  type="number"
                  placeholder="0"
                  value={formData.width}
                  onChange={handleInputChange}
                  min="0"
                  step="0.01"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              <div className="space-y-2">
                <Label htmlFor="height">Altura (cm)</Label>
                <Input
                  id="height"
                  name="height"
                  type="number"
                  placeholder="0"
                  value={formData.height}
                  onChange={handleInputChange}
                  min="0"
                  step="0.01"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="weight">Peso (kg)</Label>
                <Input
                  id="weight"
                  name="weight"
                  type="number"
                  placeholder="0"
                  value={formData.weight}
                  onChange={handleInputChange}
                  min="0"
                  step="0.01"
                />
              </div>
            </div>
          </div>

          {/* Volume calculado automaticamente */}
          <div className="space-y-2 mt-2">
            <Label htmlFor="volume">Volume (m³) - Calculado automaticamente</Label>
            <Input
              id="volume"
              name="volume"
              type="number"
              placeholder="0"
              value={formData.volume}
              readOnly
              className="bg-muted"
            />
          </div>

          <DialogFooter className="mt-6 flex-col sm:flex-row gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
              className="w-full sm:w-auto"
            >
              Cancelar
            </Button>
            <Button type="submit" disabled={isSubmitting} className="w-full sm:w-auto">
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Salvando...
                </>
              ) : (
                'Salvar Embalagem'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

export default AddPackagingDialog
