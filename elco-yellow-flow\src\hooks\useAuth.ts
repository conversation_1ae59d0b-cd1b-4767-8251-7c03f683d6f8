import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { api } from '@/lib/api';
import { toast } from '@/components/ui/use-toast';

interface LoginParams {
  email: string;
  password: string;
  remember: boolean;
}

interface User {
  name: string;
  email: string;
  phone: string;
  role: string;
}

export function useAuth() {
  const [token, setToken] = useState<string | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    const checkToken = async () => {
      const storedToken = localStorage.getItem('token') || sessionStorage.getItem('token');

      if (!storedToken || storedToken.split('.').length !== 3) {
        logout();
      } else {
        setToken(storedToken);
        try {
          const response = await api.get('/users/me', {
            headers: {
              Authorization: `Bearer ${storedToken}`,
            },
          });
          setUser(response.data);
        } catch (err) {
          console.error('Erro ao buscar dados do usuário:', err);
          logout();
        }
      }
      setLoading(false);
    };

    checkToken();

    const handleStorageChange = () => checkToken();
    window.addEventListener('storage', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  async function login({ email, password, remember }: LoginParams, onSuccess?: () => void) {
    try {
      const response = await api.post('/auth/login', { email, password });
      const { access_token } = response.data;

      if (remember) {
        localStorage.setItem('token', access_token);
      } else {
        sessionStorage.setItem('token', access_token);
      }

      setToken(access_token);

      const userRes = await api.get('/users/me', {
        headers: {
          Authorization: `Bearer ${access_token}`,
        },
      });

      setUser(userRes.data);
      toast.success('Login realizado com sucesso!');

      if (onSuccess) onSuccess();
    } catch (error: any) {
      const message = error?.response?.data?.message || 'Erro ao fazer login';
      toast.error(message);
    }
  }

  function logout() {
    localStorage.removeItem('token');
    sessionStorage.removeItem('token');
    setToken(null);
    setUser(null);
    navigate('/login');
  }

  function isAuthenticated() {
    return !!token;
  }

  return {
    token,
    user,
    loading,
    login,
    logout,
    isAuthenticated,
  };
}