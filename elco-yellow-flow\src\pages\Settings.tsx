
import React from 'react';
import Layout from '@/layouts/Layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from '@/components/ui/use-toast';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';

const Settings = () => {
  const handleSaveSettings = () => {
    toast.success('Configurações salvas com sucesso!');
  };

  return (
    <Layout title="Configurações">
      <div className="max-w-4xl mx-auto">
        <Tabs defaultValue="general">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="md:w-56 shrink-0">
              <TabsList className="flex flex-col w-full h-auto p-0 bg-transparent space-y-1">
                <TabsTrigger
                  value="general"
                  className="justify-start w-full h-9 px-3 py-2 bg-white data-[state=active]:bg-elco-50 rounded-md"
                >
                  Geral
                </TabsTrigger>
                <TabsTrigger
                  value="notifications"
                  className="justify-start w-full h-9 px-3 py-2 bg-white data-[state=active]:bg-elco-50 rounded-md"
                >
                  Notificações
                </TabsTrigger>
                <TabsTrigger
                  value="security"
                  className="justify-start w-full h-9 px-3 py-2 bg-white data-[state=active]:bg-elco-50 rounded-md"
                >
                  Segurança
                </TabsTrigger>
                <TabsTrigger
                  value="display"
                  className="justify-start w-full h-9 px-3 py-2 bg-white data-[state=active]:bg-elco-50 rounded-md"
                >
                  Exibição
                </TabsTrigger>
              </TabsList>
            </div>

            <div className="flex-1">
              <TabsContent value="general" className="mt-0">
                <Card>
                  <CardHeader>
                    <CardTitle>Configurações Gerais</CardTitle>
                    <CardDescription>
                      Gerencie suas preferências gerais de sistema
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-4">
                      <div className="flex flex-row items-center justify-between">
                        <div>
                          <Label htmlFor="language">Idioma</Label>
                          <p className="text-sm text-muted-foreground">
                            Selecione o idioma de exibição do sistema
                          </p>
                        </div>
                        <Select defaultValue="pt-BR">
                          <SelectTrigger className="w-[180px]">
                            <SelectValue placeholder="Selecione" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="pt-BR">Português (BR)</SelectItem>
                            <SelectItem value="en-US">English (US)</SelectItem>
                            <SelectItem value="es">Español</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <Separator />

                      <div className="flex flex-row items-center justify-between">
                        <div>
                          <Label>Auto-atualizações</Label>
                          <p className="text-sm text-muted-foreground">
                            Atualizar dados automaticamente
                          </p>
                        </div>
                        <Switch defaultChecked />
                      </div>

                      <Separator />

                      <div className="flex flex-row items-center justify-between">
                        <div>
                          <Label>Registros de Atividade</Label>
                          <p className="text-sm text-muted-foreground">
                            Registrar atividades do usuário
                          </p>
                        </div>
                        <Switch defaultChecked />
                      </div>
                    </div>

                    <Button onClick={handleSaveSettings}>Salvar Alterações</Button>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="notifications" className="mt-0">
                <Card>
                  <CardHeader>
                    <CardTitle>Configurações de Notificações</CardTitle>
                    <CardDescription>
                      Gerencie como você recebe notificações
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-4">
                      <div className="flex flex-row items-center justify-between">
                        <div>
                          <Label>Notificações no Sistema</Label>
                          <p className="text-sm text-muted-foreground">
                            Receber notificações no sistema
                          </p>
                        </div>
                        <Switch defaultChecked />
                      </div>

                      <Separator />

                      <div className="flex flex-row items-center justify-between">
                        <div>
                          <Label>Notificações por Email</Label>
                          <p className="text-sm text-muted-foreground">
                            Receber notificações por email
                          </p>
                        </div>
                        <Switch />
                      </div>

                      <Separator />

                      <div className="flex flex-row items-center justify-between">
                        <div>
                          <Label>Alertas de Pedidos</Label>
                          <p className="text-sm text-muted-foreground">
                            Receber alertas sobre novos pedidos
                          </p>
                        </div>
                        <Switch defaultChecked />
                      </div>

                      <Separator />

                      <div className="flex flex-row items-center justify-between">
                        <div>
                          <Label>Alertas de Erros</Label>
                          <p className="text-sm text-muted-foreground">
                            Receber alertas sobre problemas e erros
                          </p>
                        </div>
                        <Switch defaultChecked />
                      </div>
                    </div>

                    <Button onClick={handleSaveSettings}>Salvar Alterações</Button>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="security" className="mt-0">
                <Card>
                  <CardHeader>
                    <CardTitle>Configurações de Segurança</CardTitle>
                    <CardDescription>
                      Gerencie suas configurações de segurança e senha
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="current-password">Senha Atual</Label>
                        <Input id="current-password" type="password" />
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="new-password">Nova Senha</Label>
                        <Input id="new-password" type="password" />
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="confirm-password">Confirmar Nova Senha</Label>
                        <Input id="confirm-password" type="password" />
                      </div>

                      <Separator />

                      <div className="flex flex-row items-center justify-between">
                        <div>
                          <Label>Autenticação de Dois Fatores</Label>
                          <p className="text-sm text-muted-foreground">
                            Ativar autenticação de dois fatores
                          </p>
                        </div>
                        <Switch />
                      </div>
                    </div>

                    <Button onClick={handleSaveSettings}>Atualizar Senha</Button>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="display" className="mt-0">
                <Card>
                  <CardHeader>
                    <CardTitle>Configurações de Exibição</CardTitle>
                    <CardDescription>
                      Personalize a aparência do sistema
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-4">
                      <div className="flex flex-row items-center justify-between">
                        <div>
                          <Label>Modo Denso</Label>
                          <p className="text-sm text-muted-foreground">
                            Usar layout compacto nas tabelas
                          </p>
                        </div>
                        <Switch />
                      </div>

                      <Separator />

                      <div>
                        <Label>Tamanho da Fonte</Label>
                        <Select defaultValue="medium">
                          <SelectTrigger className="w-full mt-1">
                            <SelectValue placeholder="Selecione" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="small">Pequena</SelectItem>
                            <SelectItem value="medium">Média</SelectItem>
                            <SelectItem value="large">Grande</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <Separator />

                      <div className="flex flex-row items-center justify-between">
                        <div>
                          <Label>Animações</Label>
                          <p className="text-sm text-muted-foreground">
                            Ativar animações na interface
                          </p>
                        </div>
                        <Switch defaultChecked />
                      </div>
                    </div>

                    <Button onClick={handleSaveSettings}>Salvar Alterações</Button>
                  </CardContent>
                </Card>
              </TabsContent>
            </div>
          </div>
        </Tabs>
      </div>
    </Layout>
  );
};

export default Settings;
