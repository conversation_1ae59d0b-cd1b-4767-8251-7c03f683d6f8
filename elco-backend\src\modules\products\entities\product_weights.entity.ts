import { Column, CreateDateColumn, DeleteDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity('product_weights')
export class ProductWeights {
  @PrimaryGeneratedColumn()
  id: string;

  @Column({ name: 'product_code', type: 'varchar', length: 100 })
  productCode: string;

  @Column({ name: 'product_name', type: 'varchar', length: 255 })
  productName: string;

  @Column({ name: 'weight', type: 'float' })
  weight: number;

  @Column({ name: 'unit', type: 'varchar', length: 50 })
  unit: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @DeleteDateColumn({ name: 'deleted_at' })
  deletedAt: Date;
} 