
// Arquivo principal dos providers
// Aqui você pode exportar providers de contexto global

// Exemplo de estrutura:
// export { AuthProvider } from './AuthProvider';
// export { ThemeProvider } from './ThemeProvider';
// export { NotificationProvider } from './NotificationProvider';

import React from 'react';

// Por enquanto, deixamos uma estrutura base
export const AppProvider = ({ children }: { children: React.ReactNode }) => {
  return <>{children}</>;
};
