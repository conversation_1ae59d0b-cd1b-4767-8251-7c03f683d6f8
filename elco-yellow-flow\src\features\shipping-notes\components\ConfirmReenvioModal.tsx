import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Content,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Input } from '@/components/ui/input';
import { X, Plus, Mail } from 'lucide-react';

interface ConfirmReenvioModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (emails: string[]) => void;
  notasSelecionadas: { id: string; cliente: string; emailEnviado: string; fornecedorEmail?: string; tipoErro?: string }[];
  isSending?: boolean;
}

const ConfirmReenvioModal: React.FC<ConfirmReenvioModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  notasSelecionadas,
  isSending = false
}) => {
  const [emails, setEmails] = useState<string[]>(['<EMAIL>']);
  const [newEmail, setNewEmail] = useState<string>('');

  useEffect(() => {
    if (isOpen) {
      setEmails(['<EMAIL>']);
      setNewEmail('');
    }
  }, [isOpen]);

  const addEmail = () => {
    if (newEmail.trim() && !emails.includes(newEmail.trim())) {
      setEmails([...emails, newEmail.trim()]);
      setNewEmail('');
    }
  };

  const removeEmail = (emailToRemove: string) => {
    if (emailToRemove !== '<EMAIL>') {
      setEmails(emails.filter(email => email !== emailToRemove));
    }
  };

  const handleConfirm = () => {
    onConfirm(emails);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addEmail();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md md:max-w-lg bg-white border-0 shadow-lg rounded-xl">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold text-elco-800">Confirmar Envio de Emails</DialogTitle>
          <DialogDescription className="text-muted-foreground">
            Configure os emails que receberão as notificações de divergência
          </DialogDescription>
        </DialogHeader>

        {/* Seção de Emails */}
        <div className="my-4">
          <div className="mb-3">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Emails que receberão as notificações:
            </label>
            <div className="space-y-2">
              {emails.map((email, index) => (
                <div key={index} className="flex items-center space-x-2 p-2 bg-gray-50 rounded-md">
                  <Mail className="h-4 w-4 text-gray-500" />
                  <span className="flex-1 text-sm text-gray-700">{email}</span>
                  {email !== '<EMAIL>' && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeEmail(email)}
                      className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Adicionar novo email */}
          <div className="flex space-x-2">
            <Input
              type="email"
              placeholder="Adicionar email (opcional)"
              value={newEmail}
              onChange={(e) => setNewEmail(e.target.value)}
              onKeyPress={handleKeyPress}
              className="flex-1"
            />
            <Button
              onClick={addEmail}
              disabled={!newEmail.trim() || emails.includes(newEmail.trim())}
              size="sm"
              variant="outline"
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Lista de Notas */}
        <div className="my-4 max-h-48 overflow-hidden border rounded-md">
          <ScrollArea className="h-full px-2 py-1">
            <ul className="space-y-2">
              {notasSelecionadas.map((nota) => (
                <li key={nota.id} className="text-sm border-b py-1">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <strong>NF {nota.id}</strong> — {nota.cliente}<br />
                      <span className="text-xs text-muted-foreground">
                        Email: {nota.fornecedorEmail || 'Não informado'}
                      </span>
                      {nota.tipoErro && (
                        <div className="text-xs text-red-600 mt-1 truncate" title={nota.tipoErro}>
                          {nota.tipoErro}
                        </div>
                      )}
                    </div>
                    <div className="text-right text-xs text-muted-foreground">
                      {nota.emailEnviado !== '-' ? (
                        <span className="text-green-600">✓ Enviado</span>
                      ) : (
                        <span className="text-yellow-600">⏳ Pendente</span>
                      )}
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          </ScrollArea>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isSending}>Cancelar</Button>
          <Button onClick={handleConfirm} disabled={isSending}>
            {isSending ? 'Enviando...' : `Enviar para ${emails.length} email(s)`}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ConfirmReenvioModal;
