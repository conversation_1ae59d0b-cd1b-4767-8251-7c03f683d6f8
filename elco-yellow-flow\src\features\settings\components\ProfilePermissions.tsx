
import React from 'react';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

interface Module {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
}

interface UserProfile {
  name: string;
  modules: Module[];
}

interface ProfilePermissionsProps {
  profile: UserProfile;
  onModuleToggle: (profileName: string, moduleId: string, enabled: boolean) => void;
}

const ProfilePermissions: React.FC<ProfilePermissionsProps> = ({
  profile,
  onModuleToggle,
}) => {
  const enabledCount = profile.modules.filter(module => module.enabled).length;
  const totalModules = profile.modules.length;

  // Organizar módulos por categoria
  const coreModules = profile.modules.filter(module => 
    ['pedidos-expedicao', 'romaneios', 'notas-fiscais', 'notas-erros'].includes(module.id)
  );
  
  const managementModules = profile.modules.filter(module => 
    ['usuarios', 'embalagens', 'motoristas', 'transportadoras', 'veiculos'].includes(module.id)
  );
  
  const operationalModules = profile.modules.filter(module => 
    ['pesos-produtos', 'etiquetas'].includes(module.id)
  );

  const ModuleSection = ({ title, modules, description }: { 
    title: string; 
    modules: Module[]; 
    description: string;
  }) => (
    <Card>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">{title}</CardTitle>
            <CardDescription>{description}</CardDescription>
          </div>
          <Badge variant="outline">
            {modules.filter(m => m.enabled).length}/{modules.length}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {modules.map((module) => (
          <div key={module.id} className="flex items-center justify-between p-3 rounded-lg border bg-card">
            <div className="space-y-1 flex-1">
              <Label htmlFor={`${profile.name}-${module.id}`} className="text-base font-medium">
                {module.name}
              </Label>
              <p className="text-sm text-muted-foreground">
                {module.description}
              </p>
            </div>
            <Switch
              id={`${profile.name}-${module.id}`}
              checked={module.enabled}
              onCheckedChange={(checked) => 
                onModuleToggle(profile.name, module.id, checked)
              }
            />
          </div>
        ))}
      </CardContent>
    </Card>
  );

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-2xl font-semibold">{profile.name}</h3>
          <p className="text-muted-foreground">
            Configure as permissões de acesso para este perfil
          </p>
        </div>
        <Badge variant="secondary" className="text-sm">
          {enabledCount} de {totalModules} módulos ativos
        </Badge>
      </div>

      <div className="grid gap-6">
        {coreModules.length > 0 && (
          <ModuleSection 
            title="Módulos Principais" 
            modules={coreModules}
            description="Funcionalidades essenciais do sistema"
          />
        )}
        
        {managementModules.length > 0 && (
          <ModuleSection 
            title="Gestão e Cadastros" 
            modules={managementModules}
            description="Módulos para gerenciamento de cadastros"
          />
        )}
        
        {operationalModules.length > 0 && (
          <ModuleSection 
            title="Operacionais" 
            modules={operationalModules}
            description="Ferramentas operacionais e utilitários"
          />
        )}
      </div>
    </div>
  );
};

export default ProfilePermissions;
