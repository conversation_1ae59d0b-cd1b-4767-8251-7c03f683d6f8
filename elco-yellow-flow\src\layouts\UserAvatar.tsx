import React from 'react';
import { User } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAuth } from '@/hooks/useAuth';

const UserAvatar: React.FC = () => {
  const { user } = useAuth();

  return (
    <div className="flex items-center gap-3 p-2 cursor-pointer rounded-xl transition-all duration-200
      bg-gradient-to-br from-elco-500 to-elco-600 text-white shadow-lg
      hover:from-elco-600 hover:to-elco-700
      focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-elco-500
    ">
      <div className="flex h-8 w-8 items-center justify-center rounded-full bg-white/30">
        <User className="h-4 w-4 text-white" />
      </div>
      <div className="flex flex-col text-left">
        <span className="text-sm font-semibold truncate max-w-[120px]">{user?.name || 'Usu<PERSON>rio'}</span>
        <span className="text-xs opacity-80 truncate max-w-[120px]">{user?.email || '<EMAIL>'}</span>
      </div>
    </div>
  );
};

export default UserAvatar; 