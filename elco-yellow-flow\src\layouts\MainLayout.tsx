
import React, { useState, useEffect } from 'react';
import Header from '@/layouts/Header';
import Sidebar from '@/layouts/Sidebar';
import { cn } from '@/lib/utils';
import { useIsMobile } from '@/hooks/use-mobile';
import EngineeringLoader from '@/components/ui/EngineeringLoader';

interface MainLayoutProps {
  children: React.ReactNode;
  title: string;
}

const MainLayout: React.FC<MainLayoutProps> = ({ children, title }) => {
  const isMobile = useIsMobile();
  const [loading, setLoading] = useState(true);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false);
    }, 500);
    
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="flex min-h-screen bg-gray-50">
      <Sidebar onCollapseChange={setSidebarCollapsed} />
      <div className={cn(
        "flex-1 flex flex-col h-screen transition-all duration-300 ease-in-out", 
        isMobile 
          ? "ml-0" 
          : sidebarCollapsed 
            ? "ml-20" 
            : "ml-[280px]"
      )}>
        <Header title={title} />
        <main className="p-4 md:p-6 flex-1 overflow-y-auto">
          {loading ? (
            <div className="flex items-center justify-center min-h-[60vh]">
              <EngineeringLoader />
            </div>
          ) : (
            <div className="max-w-[1400px] mx-auto w-full text-left">
              {children}
            </div>
          )}
        </main>
      </div>
    </div>
  );
};

export default MainLayout;
