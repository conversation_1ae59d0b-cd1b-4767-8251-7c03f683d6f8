import { IsOptional } from 'class-validator';
import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn } from 'typeorm';

@Entity('separation_orders')
export class SeparationOrder {
  @PrimaryGeneratedColumn()
  id?: number;

  @Column()
  internalCode: string;

  @Column()
  depositorName: string;

  @Column({ type: 'timestamp' })
  orderDate: Date;

  @Column({ default: 'PENDING' })
  status: string;

  @Column({ nullable: true })
  externalCode: string;

  @Column({ type: 'text', nullable: true })
  observation: string;
  
  @Column()
  environment: string;

  @IsOptional()
  @Column()
  typeOfBond?: string;

  @IsOptional()
  @Column({ type: 'integer', nullable: true })
  volume?: number;
}