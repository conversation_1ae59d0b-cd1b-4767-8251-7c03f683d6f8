import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import ExcelJS from 'exceljs';
import { saveAs } from 'file-saver';
import { compressToEncodedURIComponent } from 'lz-string';

import romaneioTemplate from '@/assets/Romaneio_Rev_4_1.xlsx?url';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatCurrency(value: string | number): string {
  if (typeof value === 'string' && value.startsWith('R$')) {
    return value;
  }

  const numValue = typeof value === 'string' ? parseFloat(value.replace(/[^\d,.-]/g, '').replace(',', '.')) : value;

  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  }).format(numValue);
}

export const maskCnpj = (value: string) => {
  return value
    .replace(/\D/g, '')
    .replace(/^(\d{2})(\d)/, '$1.$2')
    .replace(/^(\d{2})\.(\d{3})(\d)/, '$1.$2.$3')
    .replace(/\.(\d{3})(\d)/, '.$1/$2')
    .replace(/(\d{4})(\d)/, '$1-$2')
    .slice(0, 18);
};

export const maskPhone = (value: string) => {
  const cleaned = value.replace(/\D/g, '').slice(0, 11);
  if (cleaned.length <= 10) {
    return cleaned
      .replace(/^(\d{2})(\d{4})(\d{0,4})$/, '($1) $2-$3')
      .replace(/(-\d{4})\d+?$/, '$1');
  }
  return cleaned
    .replace(/^(\d{2})(\d{5})(\d{0,4})$/, '($1) $2-$3')
    .replace(/(-\d{4})\d+?$/, '$1');
};

export const maskCnh = (value: string) => {
  return value.replace(/\D/g, '').slice(0, 11);
};

export async function exportarRomaneioExcel(romaneio: any) {
  console.log("🚀 ~ exportarRomaneioExcel ~ romaneio:", romaneio)
  const buf = await (await fetch(romaneioTemplate)).arrayBuffer();
  const wb = new ExcelJS.Workbook();
  await wb.xlsx.load(buf);
  const ws = wb.getWorksheet('ROMANEIO')!;
  const firstRow = 23;

  let closeRow = ws.rowCount;
  for (let r = firstRow; r <= ws.rowCount; r++) {
    const row = ws.getRow(r);
    const match = Array.isArray(row.values) && row.values
      .filter(cell => typeof cell === 'string')
      .some(cell => (cell as string).toUpperCase().includes('REQUISITOS PARA ESTOCAGEM'));
    if (match) {
      closeRow = r;
      break;
    }
  }

  [
    'Q3', 'D12', 'D14', 'Q6', 'Q8', 'Q10', 'Q12', 'Q14',
    'L15', 'M15', 'N15', 'O15', 'P15', 'G17', 'I17', 'K17', 'M17', 'O17'
  ].forEach(cell => ws.getCell(cell).value = null);

  for (let r = firstRow; r < closeRow; r++) {
    ws.getRow(r).eachCell(c => c.value = null);
  }

  const need = romaneio.products.length;
  const avail = closeRow - firstRow;
  if (need > avail) ws.duplicateRow(firstRow, need - avail, true);
  if (need < avail) {
    for (let i = firstRow + need; i < closeRow; i++) {
      ws.getRow(i).eachCell(cell => cell.value = null);
      ws.getRow(i).hidden = true;
    }
  }

  const today = new Date().toLocaleDateString('pt-BR');

  ws.getCell('Q3').value = `ROMANEIO N°: ${romaneio.id}`;
  ws.getCell('Q6').value = today;
  ws.getCell('Q8').value = romaneio.address;
  ws.getCell('Q10').value = romaneio.carrierName ?? '';
  ws.getCell('Q12').value = romaneio.driverName;
  ws.getCell('Q14').value = upperCasePlateLetters(romaneio.vehiclePlace);
  ws.getCell('D12').value = romaneio.romaneioOrders[0]?.separationOrders[0]?.externalCode ?? '';
  ws.getCell('D14').value = (romaneio.romaneioOrders[0]?.separationOrders[0]?.externalCode?.split(' - ')[0] ?? '');

  const typeCellMap: Record<string, string> = {
    'Solto': 'G17', 'Berço': 'I17', 'Caixa Simples': 'K17',
    'Pallet': 'M17', 'Estr. Met': 'O17', 'Pacote': 'G18',
    'Caixa': 'I18', 'Container': 'K18', 'Engradado': 'M18',
    'Outros': 'O18'
  };
  
  // Marcar primeira embalagem principal
  const mark = typeCellMap[romaneio.packagingType];
  if (mark) ws.getCell(mark).value = 'X';
  
  // Marcar segunda embalagem principal (se existir)
  const secondaryMark = typeCellMap[romaneio.secondaryPackagingType];
  if (secondaryMark && secondaryMark !== mark) {
    ws.getCell(secondaryMark).value = 'X';
  }

  const hdr = ws.getRow(firstRow - 1);
  const col: Record<string, number> = {};
  hdr.eachCell((c, i) => {
    const t = String(c.value || '').toLowerCase();
    if (t.startsWith('seq')) col.seq = i;
    else if (t.startsWith('item')) col.item = i;
    else if (t.startsWith('código')) col.cod = i;
    else if (t.includes('descri')) col.desc = i;
    else if (t.startsWith('unid')) col.uni = i;
    else if (t.startsWith('qtd')) col.qtd = i;
    else if (t.startsWith('nota fiscal')) col.nf = i;
    else if (t.startsWith('pedido')) col.wms = i;
    else if (t.startsWith('embalagem prim')) col.pk = i;
    else if (t.startsWith('embalagem secund')) col.sec = i;
    else if (t.startsWith('comp')) col.len = i;
    else if (t.startsWith('largura')) col.wid = i;
    else if (t.startsWith('altura')) col.hei = i;
    else if (t.startsWith('volume')) col.vol = i;
    else if (t.startsWith('peso')) col.pes = i;
  });

  const prods = romaneio.products
    .map((p: any) => ({
      ...p,
      volIdx: parseInt(String(p.volume).split('/')[0], 10) || 1
    }))
    .sort((a, b) => a.volIdx - b.volIdx);

  const netWeights: number[] = new Array(prods.length).fill(0);

  type Group = { start: number; end: number; volIndex: number; sumL: number; sumW: number; sumH: number; sumP: number; sumV: number };
  const groups: Group[] = [];
  prods.forEach((p: any, i: number) => {
    const abs = firstRow + i;
    const volIdx = p.volIdx;
    const last = groups[groups.length - 1];
    if (!last || last.volIndex !== volIdx) {
      groups.push({ start: abs, end: abs, volIndex: volIdx, sumL: 0, sumW: 0, sumH: 0, sumP: 0, sumV: 0 });
    } else {
      last.end = abs;
    }
  });

  const global = { L: 0, W: 0, H: 0, P: 0, V: 0 };

  groups.forEach(g => {
    const seenPackagings = new Set<number>();
    let sumL = 0, sumW = 0, sumH = 0, sumP = 0, sumV = 0;
    let totalProductWeight = 0;

    for (let i = g.start - firstRow; i <= g.end - firstRow; i++) {
      const prod = prods[i];
      const qty = prod.quantity || 1;

      totalProductWeight += (prod.weight || 0) * qty;

      const pkg = prod.packaging;
      if (pkg?.id && !seenPackagings.has(pkg.id)) {
        seenPackagings.add(pkg.id);
        sumL += pkg.length;
        sumW += pkg.width;
        sumH += pkg.height;
        sumP += pkg.weight;
        sumV += (pkg.length * pkg.width * pkg.height) / 1e6;
      }
    }

    g.sumL = sumL;
    g.sumW = sumW;
    g.sumH = sumH;
    g.sumP = sumP;
    g.sumV = +sumV.toFixed(6);
    global.L += sumL;
    global.W += sumW;
    global.H += sumH;
    global.P += sumP;
    global.V += sumV;

    let packagingAdded = false;
    for (let i = g.start - firstRow; i <= g.end - firstRow; i++) {
      const prod = prods[i];
      const baseWeight = (prod.weight);

      let finalWeight = baseWeight;
      if (!packagingAdded) {
        finalWeight += sumP;
        packagingAdded = true;
      }

      netWeights[i] = finalWeight;
    }
  });

  prods.forEach((p: any, idx: number) => {
    const r = ws.getRow(firstRow + idx);
    if (col.seq) r.getCell(col.seq).value = idx + 1;
    if (col.item) r.getCell(col.item).value = idx + 1;
    if (col.cod) r.getCell(col.cod).value = p.idExternal;
    if (col.desc) r.getCell(col.desc).value = p.name;
    if (col.uni) r.getCell(col.uni).value = 'PC';
    if (col.qtd) r.getCell(col.qtd).value = p.quantity;
    if (col.nf) r.getCell(col.nf).value = romaneio.taxOrder || p.taxNumber;
    if (col.wms) r.getCell(col.wms).value = p.internalCode;
    if (col.sec) r.getCell(col.sec).value = p.volume;
    if (col.pes) r.getCell(col.pes).value = netWeights[idx];
  });

  const centerMiddle: ExcelJS.Alignment = {
    horizontal: 'center', vertical: 'middle',
    wrapText: false, shrinkToFit: false,
    indent: 0, readingOrder: 'rtl', textRotation: 0
  };
  const thinBorder: Partial<ExcelJS.Borders> = {
    top: { style: 'thin' }, left: { style: 'thin' },
    bottom: { style: 'thin' }, right: { style: 'thin' }
  };

  const mergeCols = [col.pk, col.len, col.wid, col.hei, col.vol, col.pes].filter(Boolean);
  for (let r = firstRow; r < firstRow + need; r++) {
    mergeCols.forEach(c => ws.unMergeCells(r, c, r, c));
  }

  groups.forEach(g => {
    mergeCols.forEach(c => {
      if (g.end > g.start) ws.mergeCells(g.start, c, g.end, c);
      for (let rr = g.start; rr <= g.end; rr++) {
        const cell = ws.getRow(rr).getCell(c);
        cell.alignment = centerMiddle;
        cell.border = thinBorder;
      }
    });
    const row = ws.getRow(g.start);
    row.getCell(col.pk).value = g.volIndex;
    row.getCell(col.len).value = g.sumL;
    row.getCell(col.wid).value = g.sumW;
    row.getCell(col.hei).value = g.sumH;
    row.getCell(col.vol).value = g.sumV;
    row.getCell(col.pes).value = netWeights[g.start - firstRow];
  });

  let totalProductWeight = 0;
  let totalPackagingWeight = 0;

  // Calcular peso total dos produtos e suas embalagens
  romaneio.products.forEach((p: any) => {
    totalProductWeight += (p.weight || 0);
    
    // Cada produto tem sua própria embalagem, então somamos o peso da embalagem de cada produto
    const pkg = p.packaging;
    if (pkg?.weight) {
      totalPackagingWeight += pkg.weight;
    }
  });

  // Adicionar peso da embalagem principal do romaneio
  const mainPackagingWeight = romaneio.mainPackaging?.weight || 0;
  
  // Peso total = peso dos produtos + peso das embalagens dos produtos + peso da embalagem principal
  const totalNetWeight = totalProductWeight + totalPackagingWeight + mainPackagingWeight;
  
  console.log('🔍 Cálculo do peso total no Excel:');
  console.log('  - Peso dos produtos:', totalProductWeight);
  console.log('  - Peso das embalagens dos produtos:', totalPackagingWeight);
  console.log('  - Peso da embalagem principal:', mainPackagingWeight);
  console.log('  - Peso total:', totalNetWeight);
  
  ws.getCell('U18').value = global.L;
  ws.getCell('V18').value = global.W;
  ws.getCell('W18').value = global.H;
  ws.getCell('X18').value = totalNetWeight.toFixed(2);
  ws.getCell('Y18').value = global.V.toFixed(2);

  ws.eachRow(row => {
    row.eachCell(cell => {
      if (typeof cell.value !== 'string') return;

      const raw = cell.value as string;
      const upper = raw.toUpperCase().trim();

      if (upper.startsWith('INSPECIONADO POR')) {
        cell.value = `INSPECIONADO POR: ${romaneio.userName}`;
      }

      if (upper.startsWith('EMITIDO POR')) {
        cell.value = `EMITIDO POR: ${romaneio.userName}`;

        const belowRow = row.number + 1;
        if (belowRow <= ws.rowCount) {
          ws.getRow(belowRow).getCell(cell.col).value = today;
        }
      }
    });
  });

  // Adicionar QR Code nas colunas X e Y, linhas 1 a 7
  try {
    const urlQRCode = await gerarUrlQRCode(romaneio);
    const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(urlQRCode)}`;
    
    // Buscar a imagem do QR code
    const qrResponse = await fetch(qrCodeUrl);
    const qrImageBuffer = await qrResponse.arrayBuffer();
    
    // Adicionar a imagem ao workbook
    const qrImageId = wb.addImage({
      buffer: qrImageBuffer,
      extension: 'png',
    });
    
    // Inserir o QR code nas colunas X e Y, da linha 1 até a linha 7
    ws.addImage(qrImageId, 'X3:Y7');
  } catch (error) {
    console.error('Erro ao adicionar QR Code ao Excel:', error);
  }

  const buffer = await wb.xlsx.writeBuffer();
  saveAs(new Blob([buffer], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  }), `Romaneio_${romaneio.id}.xlsx`);
}

export function upperCasePlateLetters(plate: string): string {
  return plate.replace(/[a-zA-Z]/g, (char) => char.toUpperCase());
}

export function toSelectOption(raw: unknown, label: string) {
  if (raw === null || raw === undefined || raw === '') return null
  return { value: String(raw), label }
}

// Função para encurtar URL usando TinyURL
async function encurtarUrl(urlLonga: string): Promise<string> {
  try {
    const TINYURL_API_TOKEN = (import.meta.env.VITE_TINYURL_API_TOKEN || import.meta.env.TINYURL_API_TOKEN) as string;
    
    const response = await fetch('https://api.tinyurl.com/create', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${TINYURL_API_TOKEN}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        url: urlLonga,
        domain: 'tinyurl.com',
      }),
    });
    const data = await response.json();
    return data.data.tiny_url;
  } catch (e) {
    return urlLonga;
  }
}

// Função para gerar URL do QR code
async function gerarUrlQRCode(romaneio: any): Promise<string> {
  const { xml, pdfBase64, ...taxOrderWithoutXmlAndPdf } = romaneio.taxOrder || {};
  const idRomaneio = romaneio.idRomaneio || romaneio.id;
  
  const jsonCompact = JSON.stringify({
    i: idRomaneio,
    t: romaneio.packagingType,
    u: romaneio.userName,
    c: romaneio.carrierName,
    d: romaneio.driverName,
    v: romaneio.vehiclePlace,
    di: romaneio.dateIssue,
    l: romaneio.totalLength,
    w: romaneio.totalWidth,
    h: romaneio.totalHeight,
    we: romaneio.totalWeight,
    vo: romaneio.totalVolume,
    s: romaneio.statusName,
    ...taxOrderWithoutXmlAndPdf
  });
  
  const jsonCompressed = compressToEncodedURIComponent(jsonCompact);
  const urlLonga = `/visualizar-xml?json=${jsonCompressed}`;
  const urlCompleta = window.location.origin + urlLonga;
  
  return await encurtarUrl(urlCompleta);
}

