# 📁 Estrutura do Projeto - Elco Yellow Flow

Este documento descreve a nova arquitetura moderna e escalável do projeto React + TypeScript + TailwindCSS.

## 🏗️ Estrutura de Pastas

```
src/
├── assets/               # Imagens, ícones, arquivos estáticos
├── components/           # Componentes altamente reutilizáveis
│   └── ui/               # Componentes visuais puros (shadcn/ui + customizados)
├── features/             # Organização por domínio da aplicação
│   ├── addresses/        # Funcionalidades de endereços
│   ├── auth/             # Autenticação e autorização
│   ├── driver/           # Gerenciamento de motoristas
│   ├── expeditions/      # Pedidos de expedição
│   ├── manifestos/       # Romaneios
│   ├── packaging/        # Embalagens
│   ├── products/         # Produtos
│   ├── settings/         # Configurações
│   ├── shipping-notes/   # Notas fiscais
│   ├── supplier-email/   # Email do fornecedor
│   ├── transporter/      # Transportadoras
│   ├── users/            # Usuários
│   └── vehicle/          # Veículos
├── hooks/                # Hooks reutilizáveis
├── layouts/              # Layouts principais da aplicação
├── lib/                  # Configurações externas (axios, utils, etc.)
├── pages/                # Páginas principais
├── providers/            # Providers de contexto global
├── routes/               # Definição de rotas e guards
├── services/             # Camada de comunicação com APIs
├── store/                # Estado global (Zustand/Redux/Context)
├── styles/               # Estilos globais e configurações Tailwind
├── types/                # Tipagens globais
├── utils/                # Funções auxiliares e helpers
├── App.tsx
├── main.tsx
└── vite-env.d.ts
```

## 📋 Regras de Organização

### 1. **Componentes Reutilizáveis** (`components/ui/`)
- Componentes visuais puros (botões, inputs, modais genéricos)
- Componentes do shadcn/ui
- Componentes customizados de UI que podem ser reutilizados em várias features

### 2. **Features** (`features/`)
- Organização por domínio da aplicação
- Cada feature contém:
  - Componentes específicos da feature
  - Hooks específicos da feature
  - Tipos específicos da feature
  - Arquivo `index.ts` para exportações

### 3. **Layouts** (`layouts/`)
- Componentes de layout principais (Header, Sidebar, Footer)
- Layouts de página (MainLayout, AuthLayout, etc.)

### 4. **Hooks** (`hooks/`)
- Hooks reutilizáveis entre features
- Hooks genéricos (useAuth, useToast, etc.)

### 5. **Store** (`store/`)
- Estado global da aplicação
- Stores do Zustand, Redux ou Context API

### 6. **Providers** (`providers/`)
- Providers de contexto global
- Configurações de providers (Theme, Auth, etc.)

### 7. **Styles** (`styles/`)
- Estilos globais
- Configurações do Tailwind
- Arquivos CSS principais

## 🔧 Importações

### Importações Recomendadas:

```typescript
// Componentes UI
import { Button, Card, Dialog } from '@/components/ui';

// Layouts
import { Header, Sidebar, MainLayout } from '@/layouts';

// Features
import { PedidosExpedicao, MultiVolumeModal } from '@/features/expeditions';

// Hooks
import { useAuth } from '@/hooks/useAuth';

// Utils
import { cn } from '@/lib/utils';
```

### Arquivos `index.ts`

Cada pasta principal deve ter um arquivo `index.ts` para facilitar as importações:

```typescript
// features/expeditions/index.ts
export { default as PedidosExpedicao } from './PedidosExpedicao';
export { default as MultiVolumeModal } from './components/MultiVolumeModal';
export { default as ViewModal } from './components/ViewModal';
```

## 🚀 Próximos Passos

1. **Atualizar imports** nos arquivos existentes
2. **Implementar estado global** na pasta `store/`
3. **Criar providers** necessários na pasta `providers/`
4. **Documentar componentes** específicos de cada feature
5. **Implementar testes** organizados por feature

## 📝 Convenções de Nomenclatura

- **Componentes**: PascalCase (ex: `MultiVolumeModal`)
- **Hooks**: camelCase com prefixo `use` (ex: `useAuth`)
- **Arquivos**: PascalCase para componentes, camelCase para utilitários
- **Pastas**: kebab-case (ex: `shipping-notes`)

## 🔍 Benefícios da Nova Estrutura

- ✅ **Escalabilidade**: Fácil adicionar novas features
- ✅ **Manutenibilidade**: Código organizado por domínio
- ✅ **Reutilização**: Componentes UI centralizados
- ✅ **Testabilidade**: Estrutura clara para testes
- ✅ **Performance**: Imports otimizados
- ✅ **Developer Experience**: Fácil navegação e desenvolvimento 