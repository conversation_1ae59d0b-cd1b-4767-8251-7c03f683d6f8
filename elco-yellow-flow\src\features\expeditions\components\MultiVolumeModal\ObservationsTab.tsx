import React from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2 } from 'lucide-react';
import { RomaneioProps } from '@/hooks/useMultiVolumeModal';

interface ObservationsTabProps {
  romaneio: RomaneioProps;
  setRomaneio: React.Dispatch<React.SetStateAction<RomaneioProps>>;
  errorMessage: string | null;
  invalidSecondaryVolumes: string[];
  missingGeneralInfo: boolean;
  missingOrderInfo: boolean;
  handleSaveDraft: () => void;
  handleSubmit: () => void;
  onClose: () => void;
  isSubmitting: boolean;
  isUnlinkedFromDraft: boolean;
}

const ObservationsTab: React.FC<ObservationsTabProps> = ({
  romaneio,
  setRomaneio,
  errorMessage,
  invalidSecondaryVolumes,
  missingGeneralInfo,
  missingOrderInfo,
  handleSaveDraft,
  handleSubmit,
  onClose,
  isSubmitting,
  isUnlinkedFromDraft,
}) => {
  const hasValidationErrors = missingGeneralInfo || missingOrderInfo || invalidSecondaryVolumes.length > 0;

  return (
    <div className="space-y-4 mt-2">
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Observações do Romaneio</CardTitle>
          <CardDescription>Adicione informações adicionais relevantes para este romaneio</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Label htmlFor="observations">Observações</Label>
            <Textarea
              id="observations"
              placeholder="Digite aqui suas observações sobre este romaneio..."
              className="min-h-[150px]"
              value={romaneio.observations}
              onChange={(e) => setRomaneio({ ...romaneio, observations: e.target.value })}
            />
          </div>
        </CardContent>
      </Card>

      {errorMessage && (
        <div className="text-red-600 font-semibold text-sm px-2 py-2 border border-red-200 bg-red-50 rounded">
          {errorMessage}
        </div>
      )}
      
      {invalidSecondaryVolumes.length > 0 && (
        <div className="text-red-600 font-semibold text-sm px-2 py-2 border border-red-200 bg-red-50 rounded">
          Existem volumes informados incorretamente. Corrija os campos destacados.
        </div>
      )}

      <div className="flex justify-between mt-4">
        <Button 
          className={`${isUnlinkedFromDraft ? 'bg-gray-400 hover:bg-gray-500' : 'bg-yellow-500 hover:bg-yellow-600'} text-white`}
          onClick={handleSaveDraft} 
          disabled={isSubmitting || isUnlinkedFromDraft}
          title={isUnlinkedFromDraft ? "Desative o modo 'Novo Romaneio' para salvar rascunho" : "Salvar dados como rascunho"}
        >
          Salvar Rascunho
        </Button>
        
        <div className="space-x-2">
          <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
            Cancelar
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isSubmitting || hasValidationErrors}
            className={hasValidationErrors ? "opacity-70" : ""}
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Criando...
              </>
            ) : (
              'Criar Romaneio'
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ObservationsTab; 