import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import MainLayout from '@/layouts/MainLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { toast } from '@/components/ui/use-toast';
import { api } from '@/lib/api';
import { UserPlus, User, Phone, Mail, Lock } from 'lucide-react';
import { gerRoles } from '@/services/user-service';

const passwordStrength = (password: string) => {
  let score = 0;
  if (password.length >= 8) score++;
  if (/[A-Z]/.test(password)) score++;
  if (/[a-z]/.test(password)) score++;
  if (/[0-9]/.test(password)) score++;
  if (/[^A-Za-z0-9]/.test(password)) score++;

  if (score <= 2) return 'fraca';
  if (score === 3) return 'média';
  if (score === 4) return 'forte';
  return 'muito forte';
};

const formSchema = z
  .object({
    name: z.string().min(3, 'Nome deve ter pelo menos 3 caracteres'),
    phone: z.string().min(14, 'Telefone inválido'),
    email: z.string().email('Email inválido'),
    role: z.string().min(1, 'Selecione um cargo'),
    password: z.string().min(6, 'A senha deve ter pelo menos 6 caracteres'),
    confirmPassword: z.string(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: 'As senhas não coincidem',
    path: ['confirmPassword'],
  })
  .refine((data) => passwordStrength(data.password) !== 'fraca', {
    message: 'Senha muito fraca. Use letras, números e caracteres especiais.',
    path: ['password'],
  });

type FormValues = z.infer<typeof formSchema>;

const CreateUser = () => {
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      phone: '',
      email: '',
      role: '',
      password: '',
      confirmPassword: '',
    },
  });

  const [strengthLabel, setStrengthLabel] = useState('');
  const [roleOptions, setRoleOptions] = useState<
    { value: string; label: string }[]
  >([]);

  useEffect(() => {
    const fetchProfiles = async () => {
      const { data } = await gerRoles();
      setRoleOptions(
        data.map((x: { key: string; name: string }) => ({
          value: x.key,
          label: x.name,
        }))
      );
    };
    fetchProfiles();
  }, []);

  const onSubmit = async (data: FormValues) => {
    try {
      await api.post('/users', data);
      toast.success('Usuário criado com sucesso!');
      form.reset();
      setStrengthLabel('');
    } catch (error: any) {
      console.error(error);

      if (error?.response?.status === 409) {
        const errorMessage = error?.response?.data?.message || 'E-mail já cadastrado';
        toast.error(errorMessage);
      } else if (error?.response?.status === 400) {
        const errorMessage = error?.response?.data?.message || 'Dados inválidos';
        toast.error(errorMessage);
      } else if (error?.response?.status === 404) {
        const errorMessage = error?.response?.data?.message || 'Tipo de perfil não encontrado';
        toast.error(errorMessage);
      } else {
        toast.error('Erro ao criar usuário. Tente novamente.');
      }
    }
  };


  const maskPhone = (value: string) => {
    return value
      .replace(/\D/g, '')
      .replace(/^(\d{2})(\d)/, '($1) $2')
      .replace(/(\d{5})(\d{1,4})/, '$1-$2')
      .replace(/(-\d{4})\d+?$/, '$1');
  };

  return (
    <MainLayout title="Criar Usuário">
      <div className="max-w-3xl mx-auto">
        <Card>
          <CardHeader>
            <div className="flex items-center gap-2">
              <UserPlus className="h-6 w-6 text-primary" />
              <CardTitle>Criar Novo Usuário</CardTitle>
            </div>
            <CardDescription>
              Preencha os campos abaixo para criar um novo usuário no sistema
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-6"
              >
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <User className="h-4 w-4" />
                        Nome Completo
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Digite o nome completo"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <Phone className="h-4 w-4" />
                        Telefone
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="(00) 00000-0000"
                          value={maskPhone(field.value)}
                          onChange={(e) =>
                            field.onChange(maskPhone(e.target.value))
                          }
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <Mail className="h-4 w-4" />
                        Email
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          placeholder="<EMAIL>"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="role"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Cargo</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione um cargo" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {roleOptions.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <Lock className="h-4 w-4" />
                        Senha
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="password"
                          placeholder="Digite a senha"
                          {...field}
                          onChange={(e) => {
                            field.onChange(e.target.value);
                            setStrengthLabel(passwordStrength(e.target.value));
                          }}
                        />
                      </FormControl>
                      <p className="text-sm mt-1">
                        Força:{' '}
                        <strong
                          className={
                            strengthLabel === 'fraca'
                              ? 'text-red-500'
                              : strengthLabel === 'média'
                                ? 'text-yellow-500'
                                : strengthLabel === 'forte'
                                  ? 'text-blue-500'
                                  : strengthLabel === 'muito forte'
                                    ? 'text-green-600'
                                    : ''
                          }
                        >
                          {strengthLabel.toUpperCase()}
                        </strong>
                      </p>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="confirmPassword"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Confirmar Senha</FormLabel>
                      <FormControl>
                        <Input
                          type="password"
                          placeholder="Confirme a senha"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="pt-4">
                  <Button type="submit" className="w-full">
                    <UserPlus className="mr-2 h-4 w-4" />
                    Criar Usuário
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};

export default CreateUser;
