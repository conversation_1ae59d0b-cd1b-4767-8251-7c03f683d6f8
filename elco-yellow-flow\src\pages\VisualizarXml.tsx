import React, { useEffect, useState } from 'react';
import { decompressFromEncodedURIComponent } from 'lz-string';

const isISODate = (value: string) => {
  return /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.*Z$/.test(value);
};

const formatDate = (value: string) => {
  try {
    const date = new Date(value);
    return date.toLocaleDateString('pt-BR');
  } catch {
    return value;
  }
};

const formatTime = (value: string) => {
  try {
    const date = new Date(value);
    return date.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' });
  } catch {
    return value;
  }
};

const translateStatus = (value: string) => {
  const map: Record<string, string> = {
    ISSUED: 'Emitida',
    CANCELED: 'Cancelada',
    PENDING: 'Pendente',
  };
  return map[value] || value;
};

const fieldTranslations: Record<string, string> = {
  i: 'ID do Romaneio',
  t: 'Tipo de Embalagem',
  u: 'Usuário',
  c: 'Transportadora',
  d: 'Motorista',
  v: 'Placa do Veículo',
  di: 'Data de Emissão',
  l: 'Comprimento Total (cm)',
  w: 'Largura Total (cm)',
  h: 'Altura Total (cm)',
  we: 'Peso Total (kg)',
  vo: 'Volume Total (m³)',
  s: 'Status',
  cfopStDescricao: 'Descrição CFOP',
  createdAt: 'Data de Criação da NF',
  deletedAt: 'Data de Exclusão',
  filInCodigo: 'Código da Filial',
  idOrdem: 'ID da Ordem',
  notDtEmissao: 'Data de Emissão da NF',
  notDtSaida: 'Data de Saída da NF',
  notHrHoraemissao: 'Hora de Emissão',
  notHrHorasaida: 'Hora de Saída',
  notInCodigo: 'Código da Nota',
  notInNumero: 'Número da Nota Fiscal',
  notStCgc: 'CNPJ Emitente',
  notStChaveacesso: 'Chave de Acesso',
  notStIncrestadual: 'Inscrição Estadual',
  notStMunicipio: 'Município Emitente',
  notStUf: 'UF Emitente',
  numeroDanfe: 'Número da DANFE',
  projeto: 'Projeto',
  status: 'Status da NF',
  tpdInCodigo: 'Tipo de Documento',
  updatedAt: 'Última Atualização',
  ccfInReduzido: 'ccfInReduzido',
};

const formatFieldValue = (key: string, value: any): string => {
  if (!value) return '-';

  if (key.toLowerCase().includes('hora')) {
    return formatTime(value);
  }

  if (typeof value === 'string' && isISODate(value)) {
    return formatDate(value);
  }

  if (key === 'status' || key === 'statusName') {
    return translateStatus(value);
  }

  return String(value);
};

const VisualizarXml: React.FC = () => {
  const params = new URLSearchParams(window.location.search);
  const jsonBase64 = params.get('json');
  const [romaneio, setRomaneio] = useState<any>(null);

  useEffect(() => {
    if (jsonBase64) {
      try {
        const jsonString = decompressFromEncodedURIComponent(jsonBase64);
        setRomaneio(JSON.parse(jsonString));
      } catch (e) {
        setRomaneio(null);
      }
    }
  }, [jsonBase64]);

  const flattenedFields = romaneio
    ? {
      ...romaneio,
      ...(romaneio.taxOrder
        ? Object.fromEntries(
          Object.entries(romaneio.taxOrder).filter(
            ([key]) => key !== 'xml' && key !== 'pdfBase64'
          )
        )
        : {}),
    }
    : {};

  return (
    <div
      style={{
        padding: '24px',
        maxWidth: '95%',
        width: '800px',
        margin: '0 auto',
        fontFamily: 'Inter, sans-serif',
        background: '#fff',
        borderRadius: 10,
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.06)',
      }}
    >
      <h2
        style={{
          marginBottom: 24,
          fontSize: 22,
          borderBottom: '2px solid #000',
          paddingBottom: 6,
        }}
      >
        Dados do Romaneio
      </h2>

      {romaneio ? (
        <div style={{ display: 'flex', flexDirection: 'column', gap: 12 }}>
          {Object.entries(flattenedFields).map(([key, value]) => {
            if (key === 'xml' || key === 'pdfBase64') return null;
            const formattedValue = formatFieldValue(key, value);
            if (
              value === null ||
              value === undefined ||
              (typeof value === 'string' && value === '[object Object]') ||
              formattedValue === '-'
            ) {
              return null;
            }
            return (
              <div
                key={key}
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  flexWrap: 'wrap',
                  borderBottom: '1px solid #eee',
                  paddingBottom: 6,
                }}
              >
                <strong style={{ color: '#333', width: '100%', fontSize: 14 }}>
                  {fieldTranslations[key] || key}
                </strong>
                <span style={{ color: '#555', fontSize: 14 }}>
                  {formattedValue}
                </span>
              </div>
            );
          })}
        </div>
      ) : (
        <p style={{ color: 'red' }}>Nenhum dado encontrado ou erro ao decodificar.</p>
      )}
    </div>
  );
};

export default VisualizarXml;
