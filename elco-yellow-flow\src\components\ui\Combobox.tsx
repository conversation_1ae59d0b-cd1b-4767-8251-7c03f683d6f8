import React, { useState } from 'react';
import { Input } from './input';
import { cn } from '@/lib/utils';

interface ComboboxProps {
  options: (string | number | null | undefined)[];
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  label?: string;
  renderOption?: (option: string) => React.ReactNode;
  className?: string;
}

const statusLabels: Record<string, string> = {
  ROMANIO: 'Aguardando Notas',
  LINKED: 'Vinculado',
  PENDING: 'Pendente',
};

const Combobox: React.FC<ComboboxProps> = ({ options, value, onChange, placeholder, label, renderOption, className }) => {
  const [query, setQuery] = useState('');
  const [open, setOpen] = useState(false);
  const filteredOptions = options.filter(opt =>
    String(opt ?? '').toLowerCase().includes(query.toLowerCase())
  );

  // Função para extrair o texto do renderOption (caso seja ReactNode)
  function getLabel(val: string) {
    if (!val) return '';
    if (renderOption) {
      const rendered = renderOption(val);
      if (typeof rendered === 'string') return rendered;
      if (typeof rendered === 'number') return String(rendered);
      // Se for ReactNode, tenta extrair o texto
      if (React.isValidElement(rendered)) {
        // Se for um elemento simples
        // @ts-ignore
        if (typeof rendered.props.children === 'string') return rendered.props.children;
        // Se for um array de children
        // @ts-ignore
        if (Array.isArray(rendered.props.children)) return rendered.props.children.join(' ');
      }
    }
    return val;
  }

  return (
    <div className={cn("relative w-full", className)}>
      {label && <label className="block text-sm font-medium mb-1">{label}</label>}
      <Input
        type="text"
        placeholder={placeholder}
        value={query !== '' ? query : getLabel(value)}
        onChange={e => {
          setQuery(e.target.value);
          setOpen(true);
          onChange('');
        }}
        onFocus={() => setOpen(true)}
        onBlur={() => setTimeout(() => setOpen(false), 100)}
        className="pr-8"
      />
      {open && (
        <ul className="absolute z-10 bg-white border w-full mt-1 rounded shadow max-h-40 overflow-auto">
          {filteredOptions.length === 0 && (
            <li className="px-3 py-2 text-gray-500">Nenhuma opção</li>
          )}
          {filteredOptions.map(opt => (
            <li
              key={String(opt)}
              className="px-3 py-2 hover:bg-gray-100 cursor-pointer flex items-center gap-2"
              onMouseDown={() => {
                onChange(String(opt));
                setQuery('');
                setOpen(false);
              }}
            >
              {renderOption ? renderOption(String(opt)) : String(opt)}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default Combobox; 