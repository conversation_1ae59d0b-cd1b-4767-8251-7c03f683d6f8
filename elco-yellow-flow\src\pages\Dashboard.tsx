
import React from 'react';
import Layout from '@/layouts/Layout';
import StatCard from '@/components/ui/StatCard';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON>Chart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { FileCheck, ShoppingCart, AlertTriangle, Briefcase } from 'lucide-react';

const data = [
  { name: 'Jan', pedidos: 40, notas: 24 },
  { name: 'Fev', pedidos: 30, notas: 20 },
  { name: '<PERSON>', pedidos: 38, notas: 29 },
  { name: '<PERSON>b<PERSON>', pedidos: 45, notas: 36 },
  { name: '<PERSON>', pedidos: 52, notas: 41 },
  { name: 'Jun', pedidos: 61, notas: 49 },
];

const Dashboard = () => {
  return (
    <Layout title="Dashboard">
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="Pedidos de Expedição"
          value="125"
          icon={<ShoppingCart className="h-5 w-5" />}
          change={{ value: "12%", positive: true }}
        />
        <StatCard
          title="Notas Fiscais"
          value="83"
          icon={<FileCheck className="h-5 w-5" />}
          change={{ value: "8%", positive: true }}
        />
        <StatCard
          title="Verificador de Notas"
          value="12"
          icon={<AlertTriangle className="h-5 w-5" />}
          change={{ value: "3%", positive: false }}
        />
        <StatCard
          title="Pedidos Vinculados"
          value="78"
          icon={<Briefcase className="h-5 w-5" />}
          change={{ value: "5%", positive: true }}
        />
      </div>

      <div className="mt-6">
        <Card>
          <CardHeader>
            <CardTitle>Visão Geral</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={data}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="pedidos" name="Pedidos" fill="#FFEB3B" />
                  <Bar dataKey="notas" name="Notas Fiscais" fill="#FBC02D" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
};

export default Dashboard;
