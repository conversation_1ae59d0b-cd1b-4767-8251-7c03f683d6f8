import React, { useEffect, useMemo, useState } from 'react';
import { X, Check, User, Clock, Search, Save, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from '@/components/ui/table';
import { toast } from '@/hooks/use-toast';
import { format } from 'date-fns';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import SmartPagination from '@/components/ui/SmartPagination';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { useIsMobile } from '@/hooks/use-mobile';
import { api } from '@/lib/api';
import { useAuth } from '@/hooks/useAuth';

interface RomaneioVerificationModalProps {
  isOpen: boolean;
  onClose: () => void;
  romaneio: any;
  onVerificationComplete?: () => void;
}

// Função utilitária para formatar data de verificação
const formatVerificationDate = (verifiedDate: string, verifiedTime: string): string => {
  try {
    console.log('Formatando data:', { verifiedDate, verifiedTime });
    
    // Se temos data e hora separadas
    if (verifiedDate && verifiedTime) {
      const dateString = `${verifiedDate} ${verifiedTime}`;
      console.log('String combinada:', dateString);
      
      const date = new Date(dateString);
      console.log('Data criada:', date);
      
      if (!isNaN(date.getTime())) {
        return format(date, 'dd/MM/yyyy HH:mm');
      }
    }
    
    // Se temos apenas a data
    if (verifiedDate) {
      const date = new Date(verifiedDate);
      if (!isNaN(date.getTime())) {
        return format(date, 'dd/MM/yyyy');
      }
    }
    
    return 'Data inválida';
  } catch (error) {
    console.error('Erro ao formatar data:', error);
    return 'Data inválida';
  }
};

const RomaneioVerificationModal: React.FC<RomaneioVerificationModalProps> = ({
  isOpen,
  onClose,
  romaneio,
  onVerificationComplete,
}) => {
  console.log("🚀 ~ romaneio:", romaneio)
  console.log("🚀 ~ produtos com verificação:", romaneio.products?.filter(p => p.verified)?.map(p => ({
    id: p.id,
    verified: p.verified,
    verifiedBy: p.verifiedBy,
    verifiedDate: p.verifiedDate,
    verifiedTime: p.verifiedTime
  })))
  const [productVerification, setProductVerification] = useState<Record<string, boolean>>({});
  const [verificationDetails, setVerificationDetails] = useState<Record<string, { userName: string; verifiedAt: string }>>({});
  const [pendingVerifications, setPendingVerifications] = useState<Record<string, boolean>>({});
  const [processingProducts, setProcessingProducts] = useState<Record<string, boolean>>({});
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredProducts, setFilteredProducts] = useState<any[]>([]);
  const [observations, setObservations] = useState(romaneio.verifiedObservations || '');
  const [isSaving, setIsSaving] = useState(false);
  const isMobile = useIsMobile();
  const itemsPerPage = isMobile ? 3 : 5;

  const { user } = useAuth();

  const toggleProductVerificationState = (productId: string) => {
    const isCurrentlyVerified = productVerification[productId];
    const isCurrentlyPending = pendingVerifications[productId];
    
    // Se está tentando desfazer verificação, verificar se é administrador
    if (isCurrentlyVerified && !isCurrentlyPending && user?.role !== 'ADMIN') {
      toast({
        title: 'Acesso Negado',
        description: 'Apenas administradores podem desfazer verificações.',
        variant: 'destructive'
      });
      return;
    }
    
    // Mostrar loading durante o processamento
    setProcessingProducts(prev => ({ ...prev, [productId]: true }));
    
    // Simular um pequeno delay para melhor UX
    setTimeout(() => {
      setPendingVerifications(prev => ({ 
        ...prev, 
        [productId]: !isCurrentlyPending 
      }));
      setProcessingProducts(prev => ({ ...prev, [productId]: false }));
    }, 300);
  };

  const handleToggleAllVerification = () => {
    const isCurrentlyAllVerified = totalVerified === totalProducts && totalProducts > 0;
    const updatedPendingVerifications: Record<string, boolean> = {};

    if (isCurrentlyAllVerified) {
      // Verificar se é administrador para desfazer verificação
      if (user?.role !== 'ADMIN') {
        toast({
          title: 'Acesso Negado',
          description: 'Apenas administradores podem desfazer verificações.',
          variant: 'destructive'
        });
        return;
      }

      // Confirmar ação de desfazer verificação
      if (!confirm('Tem certeza que deseja desfazer a verificação de todos os produtos?')) {
        return;
      }

      // Desfazer verificação: marcar todos os produtos verificados como pendentes para desmarcar
      (romaneio.products || []).forEach((product: any) => {
        if (productVerification[product.id]) {
          updatedPendingVerifications[product.id] = true; // Marcar como pendente para desmarcar
        }
      });
      setPendingVerifications(updatedPendingVerifications);
    } else {
      // Marcar todos como pendentes para verificar
      (romaneio.products || []).forEach((product: any) => {
        if (!productVerification[product.id]) {
          updatedPendingVerifications[product.id] = true;
        }
      });
      setPendingVerifications(updatedPendingVerifications);
    }
  };

  const handleSaveVerification = async () => {
    setIsSaving(true);
    try {
      const productsToUpdate = (romaneio.products || []).map((product: any) => {
        const isPending = pendingVerifications[product.id];
        const wasVerified = productVerification[product.id];
        
        // Lógica corrigida para desfazer verificação
        let shouldBeVerified = wasVerified;
        
        if (isPending) {
          // Se está pendente, inverte o estado atual
          shouldBeVerified = !wasVerified;
        }
        
        const productData: { id: string; verified: boolean; userName?: string; verifiedAt?: string } = {
          id: product.id,
          verified: shouldBeVerified,
        };

        if (shouldBeVerified && user?.name) {
          productData.userName = user.name;
          productData.verifiedAt = new Date().toISOString();
        }

        return productData;
      });

      const resp = await api.patch(`/products/verify`, {
        romaneioId: romaneio.id,
        products: productsToUpdate,
        observations
      });

      if ([200, 204].includes(resp.status)) {
        toast({
          title: 'Sucesso',
          description: `Verificação do romaneio #${romaneio.id} salva com sucesso!`,
          variant: 'success'
        });
        
        // Atualizar o estado local com as verificações salvas
        const updatedVerification: Record<string, boolean> = {};
        const updatedDetails: Record<string, { userName: string; verifiedAt: string }> = {};
        
        productsToUpdate.forEach(product => {
          updatedVerification[product.id] = product.verified;
          if (product.verified && product.userName && product.verifiedAt) {
            updatedDetails[product.id] = {
              userName: product.userName,
              verifiedAt: product.verifiedAt
            };
          }
        });
        
        setProductVerification(updatedVerification);
        setVerificationDetails(updatedDetails);
        setPendingVerifications({});
        
        onVerificationComplete?.();
        onClose();
      } else {
        toast({
          title: 'Erro',
          description: 'Erro ao salvar a verificação',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Erro ao salvar verificação:', error);
      toast({
        title: 'Erro',
        description: 'Erro ao salvar a verificação',
        variant: 'destructive'
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Filtering and pagination
  useEffect(() => {
    const filtered = (romaneio.products || []).filter((product: any) =>
      product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.depositor.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredProducts(filtered);
    setCurrentPage(1);
  }, [searchTerm, romaneio.products]);

  useEffect(() => {
    if (romaneio.products?.length) {
      const verificationInit: Record<string, boolean> = {};
      const verificationDetailsInit: Record<string, { userName: string; verifiedAt: string }> = {};
  
      romaneio.products.forEach((product: any) => {
        verificationInit[product.id] = !!product.verified;
  
        if (product.verified && product.verifiedBy && product.verifiedDate && product.verifiedTime) {
          // Combinar data e hora do backend
          const dateString = `${product.verifiedDate} ${product.verifiedTime}`;
          const parsedDate = new Date(dateString);
  
          if (!isNaN(parsedDate.getTime())) {
            verificationDetailsInit[product.id] = {
              userName: product.verifiedBy,
              verifiedAt: parsedDate.toISOString()
            };
          }
        }
      });
      
      setProductVerification(verificationInit);
      setVerificationDetails(verificationDetailsInit);
      setPendingVerifications({});
    }
  }, [romaneio]);  

  // Calcular total de verificados (incluindo pendentes)
  const totalVerified = Object.values(productVerification).filter(Boolean).length + 
                       Object.values(pendingVerifications).filter(Boolean).length;
  const totalProducts = romaneio.products?.length || 0;
  const progress = totalProducts ? (totalVerified / totalProducts) * 100 : 0;

  const paginatedProducts = useMemo(() => {
    const start = (currentPage - 1) * itemsPerPage;
    return filteredProducts.slice(start, start + itemsPerPage);
  }, [filteredProducts, currentPage, itemsPerPage]);

  const totalPages = Math.ceil(filteredProducts.length / itemsPerPage);

  // Função para verificar se um produto está verificado (incluindo pendentes)
  const isProductVerified = (productId: string) => {
    return productVerification[productId] || pendingVerifications[productId];
  };

  // Função para verificar se um produto tem detalhes de verificação (apenas salvos)
  const hasVerificationDetails = (productId: string) => {
    const product = romaneio.products?.find((p: any) => p.id === productId);
    return product?.verified && product?.verifiedBy && product?.verifiedDate && product?.verifiedTime;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-2 sm:p-4">
      <div className="bg-white rounded-xl shadow-xl w-full max-w-4xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="p-3 sm:p-6 flex items-center justify-between border-b border-gray-100">
          <div className="flex items-center gap-2 sm:gap-3">
            <div className="bg-blue-100 rounded-full p-2">
              <Check className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600" />
            </div>
            <div>
              <h2 className="text-lg sm:text-xl font-semibold">
                Verificação do Romaneio #{romaneio.id}
              </h2>
              <p className="text-xs sm:text-sm text-gray-500">
                {romaneio.dateIssue ? format(romaneio.dateIssue, 'dd/MM/yyyy') : 'Data não disponível'}
              </p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 sm:h-9 sm:w-9 p-0 rounded-full hover:bg-gray-100"
            onClick={onClose}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-3 sm:p-6 flex-1 overflow-auto space-y-4 sm:space-y-6">
          {/* Action Buttons */}
          <div className="flex justify-between items-center">
            <div className="flex gap-2">
              <Button
                variant="default"
                size="sm"
                className="gap-2 bg-green-600 hover:bg-green-700 text-white"
                onClick={handleToggleAllVerification}
              >
                <Check className="h-4 w-4" />
                Verificar Todos
              </Button>
              
              {/* Botão "Desfazer Verificação" apenas para administradores */}
              {user?.role === 'ADMIN' && totalVerified === totalProducts && totalProducts > 0 && (
                <Button
                  variant="outline"
                  size="sm"
                  className="gap-2 border-red-500 text-red-600 hover:bg-red-50"
                  onClick={handleToggleAllVerification}
                >
                  <X className="h-4 w-4" />
                  Desfazer Verificação
                </Button>
              )}
            </div>
          </div>

          {/* Verification Progress */}
          {progress > 0 && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="font-medium">Progresso de verificação</span>
                <span className="text-gray-600">
                  {totalVerified} de {totalProducts} ({Math.round(progress)}%)
                </span>
              </div>
              <div className="w-full bg-gray-100 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all"
                  style={{ width: `${progress}%` }}
                ></div>
              </div>
            </div>
          )}

          {/* Products Table */}
          <Card className="border border-gray-200">
            <CardHeader className="pb-0 pt-4">
              <CardTitle className="text-base font-medium text-gray-700 flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3">
                <span>Itens do Romaneio</span>
                <div className="relative w-full sm:w-64">
                  <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="text"
                    placeholder="Buscar itens..."
                    className="pl-9 text-sm"
                    value={searchTerm}
                    onChange={e => setSearchTerm(e.target.value)}
                  />
                </div>
              </CardTitle>
              <div className="text-sm text-gray-500 pt-2">
                {filteredProducts.length} itens encontrados
              </div>
            </CardHeader>
            <CardContent className="pt-4">
              <div className="border rounded-lg overflow-hidden">
                {isMobile ? (
                  // Mobile card view for products
                  <div className="divide-y">
                    {paginatedProducts.length > 0 ? (
                      paginatedProducts.map((product: any) => (
                        <div key={product.id} className="p-3 space-y-3">
                          <div className="flex justify-between items-center">
                            <div className="font-medium">{product.idExternal}</div>
                            <Button
                              variant={isProductVerified(product.id) ? 'default' : 'outline'}
                              size="sm"
                              className={`gap-1 ${isProductVerified(product.id)
                                ? 'bg-green-600 hover:bg-green-700 text-white'
                                : ''}`}
                              onClick={() => toggleProductVerificationState(product.id)}
                            >
                                                                                            {processingProducts[product.id] ? (
                                <>
                                  <Loader2 className="h-3 w-3 animate-spin" />
                                  Processando...
                                </>
                              ) : isProductVerified(product.id) ? (
                                <>
                                  <Check className="h-3 w-3" />
                                  {pendingVerifications[product.id] ? 'Pendente' : 'Verificado'}
                                </>
                              ) : (
                                'Verificar'
                              )}
                            </Button>
                          </div>

                          <div>
                            <p className="text-sm text-gray-900">{product.name}</p>
                            <p className="text-xs text-gray-500">Depositante: {product.depositor}</p>
                          </div>

                          {hasVerificationDetails(product.id) && (
                            <div className="bg-green-50 p-2 rounded text-xs space-y-1">
                              <div className="flex items-center gap-1 text-green-700">
                                <User className="h-3 w-3" />
                                <span>Verificado por: {product.verifiedBy}</span>
                              </div>
                              <div className="flex items-center gap-1 text-green-700">
                                <Clock className="h-3 w-3" />
                                <span>
                                  Em: {formatVerificationDate(product.verifiedDate, product.verifiedTime)}
                                </span>
                              </div>
                            </div>
                          )}

                          <div className="grid grid-cols-3 gap-2 text-sm">
                            <div>
                              <p className="text-xs text-gray-500">Qtd</p>
                              <p>{product.quantity}</p>
                            </div>
                            <div>
                              <p className="text-xs text-gray-500">Volume</p>
                              <p>{product.volume}</p>
                            </div>
                            <div>
                              <p className="text-xs text-gray-500">Peso</p>
                              <p>{product.weight || 0} kg</p>
                            </div>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-6 text-gray-500">
                        Nenhum produto encontrado
                      </div>
                    )}
                  </div>
                ) : (
                  // Desktop table view for products
                  <Table>
                    <TableHeader className="bg-gray-50">
                      <TableRow>
                        <TableHead>Código</TableHead>
                        <TableHead>Nome do Produto</TableHead>
                        <TableHead>Quantidade</TableHead>
                        <TableHead>Depositante</TableHead>
                        <TableHead>Volume</TableHead>
                        <TableHead>Peso</TableHead>
                        <TableHead>Verificação</TableHead>
                        <TableHead>Detalhes da Verificação</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {paginatedProducts.length > 0 ? (
                        paginatedProducts.map((product: any) => (
                          <TableRow key={product.id}>
                            <TableCell className="font-medium">{product.idExternal}</TableCell>
                            <TableCell>{product.name}</TableCell>
                            <TableCell>{product.quantity}</TableCell>
                            <TableCell>{product.depositor}</TableCell>
                            <TableCell>{product.volume}</TableCell>
                            <TableCell>{product.weight || 0} kg</TableCell>
                            <TableCell className="w-36 min-w-[110px] max-w-[140px]">
                              <Button
                                variant={isProductVerified(product.id) ? 'default' : 'outline'}
                                size="sm"
                                className={`gap-1 w-full px-2 py-1 text-xs ${isProductVerified(product.id)
                                  ? 'bg-green-600 hover:bg-green-700 text-white'
                                  : ''}`}
                                onClick={() => toggleProductVerificationState(product.id)}
                              >
                                {processingProducts[product.id] ? (
                                  <>
                                    <Loader2 className="h-3 w-3 animate-spin" />
                                    Processando...
                                  </>
                                ) : isProductVerified(product.id) ? (
                                  <>
                                    <Check className="h-3 w-3" />
                                    {pendingVerifications[product.id] ? 'Pendente' : 'Verificado'}
                                  </>
                                ) : (
                                  'Verificar'
                                )}
                              </Button>
                            </TableCell>
                            <TableCell className="min-w-[200px]">
                              {hasVerificationDetails(product.id) ? (
                                <div className="text-xs space-y-1">
                                  <div className="flex items-center gap-1 text-green-700">
                                    <User className="h-3 w-3" />
                                    <span>{product.verifiedBy}</span>
                                  </div>
                                  <div className="flex items-center gap-1 text-green-700">
                                    <Clock className="h-3 w-3" />
                                    <span>
                                      {formatVerificationDate(product.verifiedDate, product.verifiedTime)}
                                    </span>
                                  </div>
                                </div>
                              ) : (
                                <span className="text-gray-400 text-xs">
                                  {product.verified ? 'Verificado' : 'Não verificado'}
                                </span>
                              )}
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={8} className="text-center py-6 text-gray-500">
                            Nenhum produto encontrado
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                )}
              </div>

              {/* Pagination */}
              {filteredProducts.length > itemsPerPage && (
                <div className="flex justify-center mt-4">
                  <SmartPagination
                    totalPages={totalPages}
                    currentPage={currentPage}
                    onPageChange={setCurrentPage}
                  />
                </div>
              )}
            </CardContent>
          </Card>

          {/* Observations */}
          <Card className="border border-gray-200">
            <CardHeader className="pb-0 pt-4">
              <CardTitle className="text-base font-medium text-gray-700">
                Observações da Verificação
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-4">
              <Textarea
                placeholder="Digite observações sobre a verificação..."
                value={observations}
                onChange={(e) => setObservations(e.target.value)}
                className="min-h-[100px]"
              />
            </CardContent>
          </Card>
        </div>

        {/* Footer */}
        <div className="p-3 sm:p-6 border-t border-gray-100">
          <div className="flex flex-col sm:flex-row sm:justify-end gap-2 sm:gap-3">
            <Button variant="outline" className="w-full sm:w-auto" onClick={onClose}>
              Cancelar
            </Button>
            <Button
              className="bg-blue-600 hover:bg-blue-700 w-full sm:w-auto gap-2"
              onClick={handleSaveVerification}
              disabled={isSaving || Object.keys(pendingVerifications).length === 0}
            >
              {isSaving ? (
                'Salvando...'
              ) : (
                <>
                  <Save className="h-4 w-4" />
                  Salvar Verificação
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RomaneioVerificationModal;
