import React from 'react'; // 👈 necessário para usar React.ReactNode
import { useState, useCallback, createContext, useContext } from 'react';

type ToastType = 'default' | 'destructive' | 'success';

export interface ToastContextType {
  toasts: Array<{
    id: string;
    title?: string;
    description?: string;
    variant?: ToastType;
  }>;
  toast: (props: {
    title?: string;
    description?: string;
    variant?: ToastType;
  }) => void;
  dismiss: (id: string) => void;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

export const useToast = () => {
  const context = useContext(ToastContext);

  if (!context) {
    throw new Error("useToast must be used within a ToastProvider");
  }

  return context;
};

export const toast = (props: {
  title?: string;
  description?: string;
  variant?: ToastType;
}) => {
  const { toast } = useToast();
  toast(props);
};

toast.error = (message: string) => {
  const { toast } = useToast();
  toast({
    title: 'Erro',
    description: message,
    variant: 'destructive',
  });
};

toast.success = (message: string) => {
  const { toast } = useToast();
  toast({
    title: 'Sucesso',
    description: message,
    variant: 'success',
  });
};

export const ToastProvider = ({ children }: { children: React.ReactNode }) => {
  const [toasts, setToasts] = useState<ToastContextType['toasts']>([]);

  const toast = useCallback((props: {
    title?: string;
    description?: string;
    variant?: ToastType;
  }) => {
    const id = Math.random().toString(36).substring(2, 9);
    setToasts([]);
    setToasts((prev) => [...prev, { id, ...props }]);

    setTimeout(() => {
      setToasts((prev) => prev.filter((t) => t.id !== id));
    }, 5000);
  }, []);

  const dismiss = useCallback((id: string) => {
    setToasts((prev) => prev.filter((t) => t.id !== id));
  }, []);

  return (
    <ToastContext.Provider value={{ toasts, toast, dismiss }}>
      {children}
    </ToastContext.Provider>
  );
};
