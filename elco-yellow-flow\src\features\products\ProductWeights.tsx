import React, { useState, useEffect } from 'react';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import MainLayout from '@/layouts/MainLayout';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { useToast } from '@/hooks/use-toast';
import { Weight, Scale, Package, Edit, Trash } from 'lucide-react';
import { api } from '@/lib/api';

const formSchema = z.object({
  productCode: z.string().min(1, { message: 'Código do produto é obrigatório' }),
  productName: z.string().min(1, { message: 'Nome do produto é obrigatório' }),
  weight: z.coerce.number().positive({ message: 'O peso deve ser um número positivo' }),
  unit: z.string().min(1, { message: 'Unidade de medida é obrigatória' }),
});

type FormValues = z.infer<typeof formSchema>;

type ProductWeight = {
  id: string;
  productCode: string;
  productName: string;
  weight: number;
  unit: string;
  createdAt: Date;
};

const ProductWeights = () => {
  const { toast } = useToast();
  const [products, setProducts] = useState<ProductWeight[]>([]);
  console.log("🚀 ~ ProductWeights ~ products:", products)
  const [isLoading, setIsLoading] = useState(false);
  const [editingProduct, setEditingProduct] = useState<ProductWeight | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [productToDelete, setProductToDelete] = useState<ProductWeight | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const fetchProductWeights = async () => {
    setIsLoading(true);
    try {
      const data = await api.get('/products/weights');
      setProducts(Array.isArray(data.data) ? data.data : []);
    } catch (error) {
      setProducts([]);
      toast({
        title: "Erro ao buscar produtos",
        description: "Não foi possível carregar os produtos cadastrados",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchProductWeights();
  }, []);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      productCode: '',
      productName: '',
      weight: 0,
      unit: 'kg',
    },
  });

  const editForm = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      productCode: '',
      productName: '',
      weight: 0,
      unit: 'kg',
    },
  });

  const onSubmit = async (data: FormValues) => {
    setIsLoading(true);

    try {
      await api.post('/products/weights', data);
      form.reset();
      toast({
        title: "Peso cadastrado",
        description: `Peso do produto ${data.productName} cadastrado com sucesso`,
        variant: "success",
      });
      fetchProductWeights();
    } catch (error: any) {
      toast({
        title: "Erro ao cadastrar",
        description: error?.response?.data?.error || "Ocorreu um erro ao cadastrar o peso do produto",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleEdit = (product: ProductWeight) => {
    setEditingProduct(product);
    editForm.reset({
      productCode: product.productCode,
      productName: product.productName,
      weight: product.weight,
      unit: product.unit,
    });
    setIsEditDialogOpen(true);
  };

  const onEditSubmit = async (data: FormValues) => {
    if (!editingProduct) return;
    
    setIsLoading(true);
    try {
      await api.patch(`/products/weights/${editingProduct.id}`, data);
      setIsEditDialogOpen(false);
      setEditingProduct(null);
      editForm.reset();
      toast({
        title: "Produto atualizado",
        description: `Peso do produto ${data.productName} atualizado com sucesso`,
        variant: "success",
      });
      fetchProductWeights();
    } catch (error: any) {
      toast({
        title: "Erro ao atualizar",
        description: error?.response?.data?.error || "Ocorreu um erro ao atualizar o peso do produto",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = (product: ProductWeight) => {
    setProductToDelete(product);
    setIsDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!productToDelete) return;
    
    setIsLoading(true);
    try {
      await api.delete(`/products/weights/${productToDelete.id}`);
      setIsDeleteDialogOpen(false);
      setProductToDelete(null);
      toast({
        title: "Produto excluído",
        description: `Peso do produto ${productToDelete.productName} excluído com sucesso`,
        variant: "success",
      });
      fetchProductWeights();
    } catch (error: any) {
      toast({
        title: "Erro ao excluir",
        description: error?.response?.data?.error || "Ocorreu um erro ao excluir o peso do produto",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <MainLayout title="Cadastro de Pesos de Produtos">
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <div className="flex items-center space-x-2">
              <Weight className="h-5 w-5 text-elco-600" />
              <CardTitle>Cadastro de Pesos de Produtos</CardTitle>
            </div>
            <CardDescription>
              Cadastre os pesos dos produtos para controle e expedição
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FormField
                    control={form.control}
                    name="productCode"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Código do Produto</FormLabel>
                        <FormControl>
                          <Input placeholder="Ex: PROD001" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="productName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Nome do Produto</FormLabel>
                        <FormControl>
                          <Input placeholder="Ex: Válvula Hidráulica" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FormField
                    control={form.control}
                    name="weight"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Peso</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            step="0.01"
                            placeholder="Ex: 5.75" 
                            {...field} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="unit"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Unidade</FormLabel>
                        <Select 
                          onValueChange={field.onChange} 
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Selecione a unidade" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="kg">Quilograma (kg)</SelectItem>
                            <SelectItem value="g">Grama (g)</SelectItem>
                            <SelectItem value="t">Tonelada (t)</SelectItem>
                            <SelectItem value="lb">Libra (lb)</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="flex justify-end">
                  <Button 
                    type="submit" 
                    disabled={isLoading}
                    className="flex items-center gap-2"
                  >
                    <Scale className="h-4 w-4" />
                    {isLoading ? 'Cadastrando...' : 'Cadastrar Peso'}
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <div className="flex items-center space-x-2">
              <Package className="h-5 w-5 text-elco-600" />
              <CardTitle>Produtos Cadastrados</CardTitle>
            </div>
            <CardDescription>
              Lista de produtos com pesos cadastrados
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Código</TableHead>
                    <TableHead>Nome</TableHead>
                    <TableHead className="text-right">Peso</TableHead>
                    <TableHead className="text-right">Unidade</TableHead>
                    <TableHead className="text-right">Ações</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {products.length > 0 ? (
                    products.map((product) => (
                      <TableRow key={product.id}>
                        <TableCell className="font-medium">{product.productCode}</TableCell>
                        <TableCell>{product.productName}</TableCell>
                        <TableCell className="text-right">{product.weight}</TableCell>
                        <TableCell className="text-right">{product.unit}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEdit(product)}
                              className="flex items-center gap-1"
                            >
                              <Edit className="h-3 w-3" />
                              Editar
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDelete(product)}
                              className="flex items-center gap-1 text-red-600 hover:text-red-700"
                            >
                              <Trash className="h-3 w-3" />
                              Excluir
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center text-muted-foreground py-6">
                        Nenhum produto com peso cadastrado ainda.
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        {/* Edit Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Editar Peso do Produto</DialogTitle>
              <DialogDescription>
                Faça as alterações necessárias no peso do produto.
              </DialogDescription>
            </DialogHeader>
            <Form {...editForm}>
              <form onSubmit={editForm.handleSubmit(onEditSubmit)} className="space-y-4">
                <FormField
                  control={editForm.control}
                  name="productCode"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Código do Produto</FormLabel>
                      <FormControl>
                        <Input placeholder="Ex: PROD001" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={editForm.control}
                  name="productName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nome do Produto</FormLabel>
                      <FormControl>
                        <Input placeholder="Ex: Válvula Hidráulica" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={editForm.control}
                    name="weight"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Peso</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            step="0.01"
                            placeholder="Ex: 5.75" 
                            {...field} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={editForm.control}
                    name="unit"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Unidade</FormLabel>
                        <Select 
                          onValueChange={field.onChange} 
                          value={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Selecione a unidade" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="kg">Quilograma (kg)</SelectItem>
                            <SelectItem value="g">Grama (g)</SelectItem>
                            <SelectItem value="t">Tonelada (t)</SelectItem>
                            <SelectItem value="lb">Libra (lb)</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <DialogFooter>
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => setIsEditDialogOpen(false)}
                  >
                    Cancelar
                  </Button>
                  <Button type="submit" disabled={isLoading}>
                    {isLoading ? 'Salvando...' : 'Salvar Alterações'}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>

        {/* Delete Confirmation Dialog */}
        <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Confirmar Exclusão</AlertDialogTitle>
              <AlertDialogDescription>
                Tem certeza que deseja excluir o peso do produto "{productToDelete?.productName}"? 
                Esta ação não pode ser desfeita.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancelar</AlertDialogCancel>
              <AlertDialogAction
                onClick={confirmDelete}
                className="bg-red-600 hover:bg-red-700"
                disabled={isLoading}
              >
                {isLoading ? 'Excluindo...' : 'Excluir'}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </MainLayout>
  );
};

export default ProductWeights;
